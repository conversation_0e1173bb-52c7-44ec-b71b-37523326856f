<?php

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It is a breeze. Simply tell Lumen the URIs it should respond to
| and give it the Closure to call when that URI is requested.
|
*/

$router->get('/', function () use ($router) {
    return $router->app->version();
});
$router->group([
    'middleware' => ['sso', 'systemSafeguard'],
    'prefix' => 'schedule'
], function () use ($router) {
    $router->post('/create', 'Schedule\IndexController@create');
    $router->post('/edit', 'Schedule\IndexController@edit');
    $router->post('/finish', 'Schedule\IndexController@finish');
    $router->post('/delete', 'Schedule\IndexController@delete');
    $router->post('/index', 'Schedule\IndexController@index');
    $router->post('/studentList', 'Schedule\IndexController@studentList');
    $router->post('/studentScheduleList', 'Schedule\IndexController@studentScheduleList');
    $router->post('/studentInfo', 'Schedule\IndexController@studentInfo');
    $router->post('/attentionStudent', 'Schedule\IndexController@attentionStudent');
    $router->post('/assignPlanAttachUser', 'Schedule\IndexController@assignPlanAttachUser');
    // v1.2.0 日程安排 新接口
    $router->post("/scheduleList", 'Schedule\ScheduleController@index');
    $router->get("/teacher", 'Schedule\ScheduleController@teacher');
});

#小程序
$router->group([
//    'middleware' => 'ip',
    'prefix' => 'miniProgram'
], function () use ($router) {
    $router->get('/index', 'MiniProgram\IndexController@index');
    $router->get('/detail', 'MiniProgram\IndexController@detail');
});

$router->group(['prefix' => 'banner', 'middleware' => ['sso', 'systemSafeguard', 'student']], function () use ($router) {
    $router->get('list', 'Banner\BannerController@lists');
    $router->post('click', 'Banner\BannerController@click');
    $router->get('classInfo', 'Banner\BannerController@classInfo');
    $router->post('reserve', 'Banner\BannerController@reserve');
    //sc banner
    $router->get('index', 'Banner\BannerController@index');
});

//学员个人中心
$router->group(['prefix' => 'personalCenter', 'middleware' => ['sso', 'student']], function () use ($router) {
    $router->get('/studentDetail', 'PersonalCenter\IndexController@studentDetail');

    $router->group(['middleware' => ['systemSafeguard']], function () use ($router) {
        $router->get('/index', 'PersonalCenter\IndexController@index');
        $router->get('/learningPlanList', 'PersonalCenter\IndexController@learningPlanList');
        $router->get('/learningPlanDetail', 'PersonalCenter\IndexController@learningPlanDetail');
        $router->get('/lessonList', 'PersonalCenter\IndexController@lessonList');
        $router->get('/lessonPickLists', 'PersonalCenter\IndexController@lessonPickLists');
        $router->post('/changeTarget', 'PersonalCenter\IndexController@changeTarget');
        $router->post('/changeNickname', 'PersonalCenter\IndexController@changeNickname');
        //resource
        $router->post('/resource/changeAvater', 'PersonalCenter\ResourceController@changeAvater');
        $router->post('/resource/changeAvaterFrame', 'PersonalCenter\ResourceController@changeAvaterFrame');//可以舍弃
        $router->post('/resource/changeSkin', 'PersonalCenter\ResourceController@changeSkin');//可以舍弃
        $router->post('/resource/changeResource', 'PersonalCenter\ResourceController@changeResource');
        $router->get('/resource/resourceList', 'PersonalCenter\ResourceController@resourceList');
        $router->post('/resource/exchangeResource', 'PersonalCenter\ResourceController@exchangeResource');
        $router->get('/resource/exchangeHistory', 'PersonalCenter\ResourceController@exchangeHistory');
    });
});

//待办相关
$router->group(['prefix' => 'task', 'middleware' => ['sso', 'systemSafeguard', 'student']], function () use ($router) {
    $router->get("/collect", 'Task\TaskController@collect');
    $router->get("/list", 'Task\TaskController@taskList');
    $router->get("/taskInfoOfWord", 'Task\TaskController@taskInfoOfWord');
    $router->post("/taskOfWordAny", 'Task\TaskController@taskOfWordAny');
    $router->post("/taskOfLmsAny", 'Task\TaskController@taskOfLmsAny');
    $router->post("/taskOfGemsAny", 'Task\TaskController@taskOfGemsAny');
});

//勋章相关
$router->group(['prefix' => 'medal', 'middleware' => ['sso', 'systemSafeguard', 'student']], function () use ($router) {
    $router->get("/list", 'Medal\MedalController@userMedalList');
    $router->get("/dropMedal", 'Medal\MedalController@dropMedalList');
    $router->post("/drop", 'Medal\MedalController@drop');
    $router->get("/categoryMedal", 'Medal\MedalController@categoryMedal');
});

//消息相关
$router->group(['prefix' => 'msg', 'middleware' => ['sso', 'systemSafeguard', 'student']], function () use ($router) {
    $router->get("/count", 'Message\StuMessageController@userMessageCount');
    $router->get("/list", 'Message\StuMessageController@userMessageList');
    $router->get("/read", 'Message\StuMessageController@userReadMessage');
    //首页返回是否有消息
    $router->get("/hasMessage", 'Message\StuMessageController@getHasMessage');

    //toastr
    $router->get("/toastr", 'Message\StuMessageController@toastr');
    $router->get("/toastr/read", 'Message\StuMessageController@toastrRead');
});

//话题相关
$router->group(['prefix' => 'talk', 'middleware' => ['sso', 'systemSafeguard', 'student']], function () use ($router) {
    $router->get("/top", 'Talk\TalkController@getTopTalk');
    $router->get("/all", 'Talk\TalkController@getAllTalks');
    $router->get("/myJoin", 'Talk\TalkController@getMyJoinTalks');
    $router->get("/detail", 'Talk\TalkController@getTalkDetail');
    $router->get("/comments", 'Talk\TalkController@getTalkComments');
    $router->get("/childComments", 'Talk\TalkController@getChildComments');
    $router->get("/other", 'Talk\TalkController@getRoundOtherTalks');
    $router->post("/saveComment", 'Talk\TalkController@saveTalkComment');
    $router->get("/delComment", 'Talk\TalkController@delTalkComment');
    $router->get("/actionComment", 'Talk\TalkController@actionComment');
    $router->get("/rankingTypes", 'Talk\TalkController@getRankingTypes');
    $router->get("/rankingDetail", 'Talk\TalkController@getRankingDetail');
    $router->get("/allComments", 'Talk\TalkController@getAllComments');
    $router->get("/closeTip", 'Talk\TalkController@closeTalkTip');
    $router->get("/getEmoji", 'Talk\TalkController@getEmoji');
});

//话题相关
$router->group(['prefix' => 'student', 'middleware' => ['sso', 'systemSafeguard', 'student']], function () use ($router) {
    $router->get("studyTime", "Student\StudyTimeController@index");
    $router->get("evaluationConfig", "Student\StudentEvaluationController@config");
    $router->post("submitEvaluation", "Student\StudentEvaluationController@submitEvaluation");
});

//其它无法被归类的
$router->group(['prefix' => 'others', 'middleware' => ['sso', 'student']], function () use ($router) {
    $router->group(['middleware' => ['systemSafeguard']], function () use ($router) {
        $router->get("menu", "Others\OthersController@menu");
        $router->get("hasOfThirdPartMessage", "Others\OthersController@hasOfThirdPartMessage");
        $router->get("thirdPartMessage", "Others\OthersController@thirdPartMessage");
        $router->post("readMessage", "Others\OthersController@readMessage");
    });
    $router->get("uaLinks", "Others\OthersController@uaLinks");
    $router->get("uaTarget/{id}", "Others\OthersController@uaTarget");
});
//无状态
$router->group(['prefix' => 'auth'], function () use ($router) {
    $router->get("logout", "Auth\AuthController@logout");
});


//无状态
$router->group(['prefix' => 'callback'], function () use ($router) {
    $router->post("oss", "Others\OthersController@ossCallback");
});

//调研
$router->group(['prefix' => 'survey', 'middleware' => ['sso', 'systemSafeguard', 'student']], function () use ($router) {
    $router->get('/getStudentSurvey', 'Survey\SurveyController@getStudentSurvey');
    $router->post('/saveStudentSurvey', 'Survey\SurveyController@saveStudentSurvey');
});

$router->group(['prefix' => 'index', 'middleware' => ['sso', 'student']], function () use ($router) {
    $router->get('/navigation', 'Index\IndexController@navigation');
});

//给其他平台的对接接口
$router->group(['prefix' => 'service', 'middleware' => ['ip',]], function () use ($router) {
    $router->post('/crm/firstOrder', 'Others\CrmController@firstOrder');
    $router->get('/word/studentInfo', 'Others\WordController@studentInfo');
    //cast 权限维护
    $router->post("/teacherEvaluate/teacherEvaluatePermissionChange", "V1\TeacherEvaluateController@teacherEvaluatePermissionChange");
});
//给其他平台的对接接口 带cookie V1
$router->group(['prefix' => 'V1', 'middleware' => ['ip', 'sso']], function () use ($router) {
    $router->group(["prefix" => "interimReport"], function () use ($router) {
        $router->get("studentList", "V1\StudentInterimReportController@studentList");
        $router->get("timeOfStudentClassTeacher", "V1\StudentInterimReportController@timeOfStudentClassTeacher");
        $router->get("reportInfo/{id}", "V1\StudentInterimReportController@reportInfo");
        $router->post("updateReport", "V1\StudentInterimReportController@updateReport");
        $router->post("createReport", "V1\StudentInterimReportController@createReport");
        $router->get("deleteReport/{id}", "V1\StudentInterimReportController@deleteReport");
        $router->get("teacherList", "V1\StudentInterimReportController@teacherList");
        $router->get("supervisorList", "V1\StudentInterimReportController@supervisorList");
        $router->get("createUser", "V1\StudentInterimReportController@createUser");
    });
    $router->group(["prefix" => "teacherEvaluate", 'middleware' => ["sso", "ip"]], function () use ($router) {
        $router->get("evaluateUser", "V1\TeacherEvaluateController@evaluateUser");
        $router->get("evaluateList/{id}", "V1\TeacherEvaluateController@evaluateList");
        $router->get("teacherEvaluateSearchParam", "V1\TeacherEvaluateController@teacherEvaluateSearchParam");
        $router->get("teacherEvaluateSearchParam2/{id}", "V1\TeacherEvaluateController@teacherEvaluateSearchParam2");
        $router->get("exportEvaluate", "V1\TeacherEvaluateController@exportEvaluate");
        $router->post("teacherEvaluateDetail/{id}", "V1\TeacherEvaluateController@editTeacherEvaluate");
        $router->get("teacherEvaluateDetail/{id}", "V1\TeacherEvaluateController@teacherEvaluateDetail");
        $router->post("importEvaluate", "V1\TeacherEvaluateController@importEvaluate");
        $router->get("doEvalShare/{id}", "V1\TeacherEvaluateController@doEvalShare");
    });
});

//给其他平台的对接接口 带cookie V2 只返回具体数据，不再返回状态码 类rpc调用
$router->group(['prefix' => 'V2'], function () use ($router) {
    $router->group(["prefix" => "gift", 'middleware' => ['ip', 'sso']], function () use ($router) {
        $router->get("giftRankType", "V2\GiftRewardController@giftRankType");
        $router->get("giftRank", "V2\GiftRewardController@giftRank");
        $router->get("giftRecord", "V2\GiftRewardController@giftRecord");
    });
    //无状态
    $router->group(["prefix" => "teacherEvaluate"], function () use ($router) {
        $router->get("evaluateUser", "V2\TeacherEvaluateStatelessController@evaluateUser");
        $router->post("submitShareEvaluate", "V2\TeacherEvaluateStatelessController@submitShareEvaluate");
        $router->get("config", "V2\TeacherEvaluateStatelessController@config");
    });
});

$router->group(['prefix' => 'evaluate', 'middleware' => ["sso", "systemSafeguard", "student"]], function () use ($router) {
//    $router->get('/debug', 'Evaluate\EvaluateController@debug');
    $router->get('/evaluateUser', 'Evaluate\EvaluateController@evaluateUser');
    $router->post('/submitEvaluate', 'Evaluate\EvaluateController@submitEvaluate');
    $router->post('/closeButton', 'Evaluate\EvaluateController@closeButton');
});

$router->group(['prefix' => 'config', 'middleware' => ["sso", "student"]], function () use ($router) {
    $router->get("/getConfig", "Config\ConfigController@config");
});


$router->group(['prefix' => 'gift', 'middleware' => ["sso", "student"]], function () use ($router) {
    $router->post("/sendGift", "Gift\GiftController@sendGift");
});
