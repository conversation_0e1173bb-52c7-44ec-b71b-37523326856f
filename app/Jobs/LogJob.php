<?php

namespace App\Jobs;

use App\Models\OperationLog;

class LogJob extends Job
{
    public $userData;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($userData)
    {
        $this->userData = $userData;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $array = $this->userData;
        if ($array['table'] == 1) {
            $log = new OperationLog();
            $log->setAttribute('user_id', $array['user_id']);
            $log->setAttribute('ip', $array['ip']);
            $log->setAttribute('api', $array['api']);
            $log->setAttribute('method', $array['method']);
            $log->setAttribute('input', $array['input']);
            $log->setAttribute('created_at', $array['created_at']);
            $log->save();
        }
    }
}
