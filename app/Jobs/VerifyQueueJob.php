<?php

namespace App\Jobs;

use App\Models\Verify\UserData;
use App\Utils\QueueUtil;

class VerifyQueueJob extends Job
{
    public $userData;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(UserData $userData)
    {
        $this->userData = $userData;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        /*
        var_dump($this->data);
        $log = $this->data;
        $monolog = new Logger('info');
        $monolog->pushHandler(new RotatingFileHandler(storage_path('logs/queue/info.log')));
        $monolog->error('Http Error', $log);
        */
        echo ("start:" . date('Y-m-d H:i:s')) . PHP_EOL;
        echo ("parm:" . json_encode($this->userData)) . PHP_EOL;
        $userData = $this->userData;
        $classPath = $userData->classPath;
        $methodName = $userData->methodName;
        $notifyData = $userData->notifyData;
        $arr = $userData->data;
        $retData = QueueUtil::verifyByThirdParty($arr, md5(json_encode($notifyData)));
        echo ("ret:" . json_encode($retData)) . PHP_EOL;
        call_user_func_array(array($classPath, $methodName), array($notifyData, $retData));
        echo ("end:" . date('Y-m-d H:i:s')) . PHP_EOL;
    }
}
