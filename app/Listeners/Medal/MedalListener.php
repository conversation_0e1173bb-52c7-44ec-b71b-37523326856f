<?php


namespace App\Listeners\Medal;

use App\ConstDir\Medal\MedalConst;
use App\ConstDir\ThirdPart\ThirdPartMessageConst;
use App\Events\Medal\MedalEvent;
use App\Http\Services\Medal\MedalService;
use App\Http\Services\Talk\TalkService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;

/**
 * 勋章事件观察者
 * Class MedalListener
 * @package App\Listeners\Medal
 */
class MedalListener implements ShouldQueue
{
    use InteractsWithQueue;

    public $connection = "redis";

    public $queue = 'listeners';

    /**
     * Handle the event.
     *
     * @param  \App\Events\Medal\MedalEvent  $event
     * @return void
     */
    public function handle(MedalEvent $event)
    {
        if ($event->medalType == ThirdPartMessageConst::MILLION_WORD) {
            //百万单词的下发勋章
            (new MedalService())->medalTriggerFromThirdPart($event->data);
        } else if ($event->medalType == MedalConst::POINT_HALL_OF_FAME) {
            //todo 这个时候需要拿top
            $top = (new TalkService())->getRankingDetail(3, true);
            $top = collect(Arr::get($top, "list", []));
            $top = $top->mapWithKeys(function ($item, $key) {
                return [$key => [
                    'id' => $item->user_id,
                    'progress' => 1,
                    'rank' => $item->top,
                ]];
            });
            (new MedalService())->topMedal($top, $event->medalType);
        } else if ($event->medalType == MedalConst::STUDY_STAR) {
            //排行榜前3名
            (new MedalService())->topMedal($event->data, $event->medalType);
        } else {
            (new MedalService())->addMedalProgress($event->medalType, $event->data);
        }
    }
}
