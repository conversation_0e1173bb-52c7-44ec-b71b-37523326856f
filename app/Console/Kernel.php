<?php

namespace App\Console;

use App\Console\Commands\EvaluateDataFillCommand;
use App\Console\Commands\EvaluateTriggerCommand;
use Illuminate\Console\Scheduling\Schedule;
use <PERSON><PERSON>\Lumen\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        \App\Console\Commands\MQCommand::class,
        \App\Console\Commands\DateCommand::class,
        \App\Console\Commands\StudentStudyTimeCommand::class,
        \App\Console\Commands\SyncOssFileCommand::class,
        \App\Console\Commands\MedalTriggerCommand::class,
        \App\Console\Commands\WeeklyRankCommand::class,
        \App\Console\Commands\MonthlyRankCommand::class,
        \App\Console\Commands\SyncStudentOldMedalCommand::class,
        \App\Console\Commands\CheckCommentsReplayCommand::class,
        \App\Console\Commands\UaBannerClickUserChangeCommand::class,
        \App\Console\Commands\FixUserPointOfMedalCommand::class,
        EvaluateTriggerCommand::class,
        EvaluateDataFillCommand::class
    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('sync:oss')->everyTenMinutes()->runInBackground()->withoutOverlapping()->appendOutputTo(storage_path('logs/SyncOss.log'));
        $schedule->command('medal:trigger')->daily()->runInBackground()->withoutOverlapping()->appendOutputTo(storage_path('logs/MedalTrigger.log'));
        $schedule->command('study:time')->daily()->runInBackground()->withoutOverlapping()->appendOutputTo(storage_path('logs/StudyTime.log'));
        $schedule->command('rank:week')->weeklyOn(1, '00:00')->withoutOverlapping();
        $schedule->command('rank:month')->monthlyOn(1, '00:00')->withoutOverlapping();

        $schedule->command('evaluate:trigger')->daily()->runInBackground()->withoutOverlapping()->appendOutputTo(storage_path('logs/EvaluateTrigger.log'));

    }
}
