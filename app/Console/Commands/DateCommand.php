<?php


namespace App\Console\Commands;


use Illuminate\Console\Command;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class DateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'general:date {start} {end}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '生成财务年日数据';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if (!Schema::hasTable("date_of_year")) {
            Schema::create("date_of_year", function (Blueprint $table) {
                $table->integer("date_id")->index("index_date_id")->unique();
                $table->integer("year_finance")->comment("所属财务年");
                $table->integer("month_finance")->comment("所属财务月");
                $table->integer("year_finance_half")->comment("所属财务半年 0是上半年 1是下半年");
                $table->string("month_finance_str")->comment("财务月英文");
                $table->date("year_finance_half_start")->comment("所属财务半年开始时间");
                $table->date("year_finance_half_end")->comment("所属财务半年结束时间");
                $table->date("month_finance_start")->comment("所属财务月开始时间");
                $table->date("month_finance_end")->comment("所属财务月结束时间");
                $table->integer("year_week")->comment("所在周");
            });
        }
        var_dump($this->arguments());
        $startYear = (int)$this->argument("start");
        $endYear = (int)$this->argument("end");
        $month_en = $months = [
            1 => 'January',
            2 => 'February',
            3 => 'March',
            4 => 'April',
            5 => 'May',
            6 => 'June',
            7 => 'July',
            8 => 'August',
            9 => 'September',
            10 => 'October',
            11 => 'November',
            12 => 'December'
        ];
        $startDay = strtotime(sprintf("%s-%s-%s", $startYear, "01", "01"));
        $endDay = strtotime(sprintf("%s-%s-%s", $endYear, "12", "31"));
        $data = [];
        $startHalf = ["%s.12.21", "%s.06.21"];
        $endHalf = ["%s.06.20", "%s.12.20"];
        while ($startDay <= $endDay) {
            $month = date("n", $startDay);
            $day = date("j", $startDay);
            $year = date("Y", $startDay);
            // 财务月 21 ～次月20日
            $add = 0;
            if ($day >= 21) {
                $add = 1;
            }
            $month += $add;
            if ($month == 13) {
                $year += 1;
                $month = 1;
            }
            $half = $month > 6 ? 1 : 0;
            $monthFinance = strtotime($year . "-" . $month . "-" . "01");
            $tmp = [date("Ymd", $startDay), $year, $month, $half, $month_en[$month], sprintf($startHalf[$half], $half ? $year : $year - 1), sprintf($endHalf[$half], $year), date("Y-m-21", $monthFinance - 1), date("Y-m-20", $monthFinance)];
            $tmp[] = date("YW", $startDay);
            $data[] = $tmp;
            $startDay += 86400;
        }
        $insert = [];
        foreach ($data as $item) {
            $insert[] = [
                "date_id" => $item[0],
                "year_finance" => $item[1],
                "month_finance" => $item[2],
                "year_finance_half" => $item[3],
                "month_finance_str" => $item[4],
                "year_finance_half_start" => $item[5],
                "year_finance_half_end" => $item[6],
                "month_finance_start" => $item[7],
                "month_finance_end" => $item[8],
                "year_week" => $item[9],
            ];
        }
        DB::table("date_of_year")->truncate();
        DB::table("date_of_year")->insert($insert);
        echo "Run Over!" . PHP_EOL;
        return 0;
    }
}
