<?php

namespace App\Console\Commands;

use App\Http\Services\Point\PointService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixUserPointOfMedalCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:user:point:medal';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '特定勋章积分回扣';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $point = new PointService();
        $point->fixUserPointMedal();
    }
}
