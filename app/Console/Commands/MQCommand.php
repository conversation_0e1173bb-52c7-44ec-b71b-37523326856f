<?php


namespace App\Console\Commands;


use App\ConstDir\ThirdPart\ThirdPartMessageConst;
use App\Http\Services\Other\ThirdPartRecordService;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use PhpAmqpLib\Connection\AMQPStreamConnection;

class MQCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mq:consume';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '消费队列';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $arrConfig = config('queue.connections.mq');
        $objConnection = new AMQPStreamConnection(
            $arrConfig['host'],
            $arrConfig['port'],
            $arrConfig['login'],
            $arrConfig['password'],
            $arrConfig['vhost'],
            true,
            'AMQPLAIN',
            'null',
            'en_US',
            '3.0',
            '3.0',
            null,
            true,
            30
        );
        $objChannel = $objConnection->channel();
        $objChannel->confirm_select();
        $objChannel->exchange_declare($arrConfig['exchange'], $arrConfig['exchangeType'], false, true, false);
        $objChannel->queue_declare($arrConfig['queue'], false, true, false, false);
        $objChannel->queue_bind($arrConfig['queue'], $arrConfig['exchange']);
        $objChannel->basic_qos(null, 1, null);
        $callback = function ($msg) use ($objChannel) {
            $this->info(date('Y-m-d H:i:s') . ',msg:' . $msg->body);
            try {
                $arrData = json_decode($msg->body, true);
                //todo 逻辑处理待定
                $type = Arr::get($arrData, "type", "");
                if (in_array($type, ThirdPartMessageConst::MQ_DEAL_MESSAGE)) {
                    $service = new ThirdPartRecordService();
                    $service->dealMessage($arrData);
                }
            } catch (\Exception $e) {
                $this->info("数据解析失败！");
                $this->info($e->getMessage());
            } finally {
                //默认ack
                $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']);
            }
        };
        $objChannel->basic_consume($arrConfig['queue'], '', false, false, false, false, $callback);
        while ($objChannel->is_consuming()) {
            $objChannel->wait();
        }
    }
}
