<?php


namespace App\Console\Commands;


use App\Http\Services\Evaluate\EvaluateService;
use App\Models\FastModel\FastQueryModel;
use Illuminate\Console\Command;

class EvaluateTriggerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'evaluate:trigger';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '调研触发';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $time = time();
        echo "脚本开始 ====> " . date("Y-m-d H:i:s", $time) . PHP_EOL;
        $evaluateService = new EvaluateService();
        $configs = FastQueryModel::configOfTeacherEvaluate()->orderBy("id", "DESC")->get();
        foreach ($configs as $config) {
            $id = $config->id;
            $config = json_decode($config->configs, true);
            if (!$config) {
                echo "配置异常!" . PHP_EOL;
                continue;
            }
            $config['id'] = $id;
            $evaluateService->evaluateTrigger($config);
        }
        $end = time();
        echo "脚本结束 ====> " . date("Y-m-d H:i:s", $end) . PHP_EOL;
        echo "总共用时: " . ($end - $time) . "秒" . PHP_EOL;
        return  true;
    }
}
