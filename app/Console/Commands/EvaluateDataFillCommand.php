<?php

namespace App\Console\Commands;

use App\Http\Services\Point\PointService;
use App\Models\FastModel\FastQueryModel;
use Illuminate\Console\Command;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class EvaluateDataFillCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'evaluate:data:fill';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'CSAT数据补充';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $model = FastQueryModel::teacherEvaluateStudentRecord()
            ->whereNotExists(function (Builder $q) {
                $q->select(DB::raw(1))->from("evaluate_record_info")
                    ->whereColumn("evaluate_record_info.belong_id", "teacher_evaluate_student_record.id")
                    ->where("type", "student");
            })->get();
        foreach ($model as $item) {
            $tmp = [
                "config_id" => $item->config_id,
                "type" => "student",
                "belong_id" => $item->id,
                "code" => "A",
                "created_at" => date("Y-m-d H:i:s"),
            ];
            if ($item->last_open_time) {
                $tmp["last_open_time"] = $item->last_open_time;
            }
            if ($item->close_status) {
                $tmp["close_status"] = $item->close_status;
            }
            $id = FastQueryModel::teacherEvaluateRecordInfo()->insertGetId($tmp);
            FastQueryModel::teacherEvaluate()->where("belong_student_eval_id", $item->id)->update(["belong_record_id" => $id]);
        }
        FastQueryModel::teacherEvaluate()->where("id", ">", 0)->update(["main_id" => DB::raw("id")]);

        $high_score = 9;

        //更新评分
        $subQuery = FastQueryModel::teacherEvaluate()
            ->selectRaw('belong_user_eval_id, COUNT(1) as total')
            ->where("status", 1)
            ->where("eval_score", ">=", $high_score)
            ->groupBy('belong_user_eval_id');

        DB::transaction(function () use ($subQuery) {
            $subSql = $subQuery->toSql();
            DB::update("UPDATE sc_teacher_evaluate_target_user AS main INNER JOIN ($subSql) AS sub ON main.id = sub.belong_user_eval_id SET main.high_eval_score_num = sub.total", $subQuery->getBindings());
        });
    }


}
