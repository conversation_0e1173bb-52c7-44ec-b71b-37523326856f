<?php


namespace App\Console\Commands;


use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class UaBannerClickUserChangeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ua:banner:click:user:change';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'ua banner 用户修改城uaid';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $sql = "SELECT user_id FROM sc_ua_banner_click GROUP BY user_id";
        $list = DB::select($sql);
        $list = collect($list);
        $sql2 = "select ua_id from ua.ua_users where u_id = ?";
        foreach ($list as $item) {
            $userId = $item->user_id;
            $singleUser = DB::select($sql2, [$userId]);
            if ($singleUser) {
                $singleUser = Arr::get($singleUser, "0", new \stdClass());
                $singleUser = optional($singleUser)->ua_id;
            }
            if (!$singleUser) {
                continue;
            }
            DB::update("update sc_ua_banner_click set user_id = ? where user_id = ?", [$singleUser, $userId]);
        }
        return  true;
    }
}
