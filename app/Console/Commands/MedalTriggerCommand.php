<?php


namespace App\Console\Commands;


use App\Http\Services\Medal\MedalService;
use Illuminate\Console\Command;

class MedalTriggerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'medal:trigger';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '勋章触发';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $server = new MedalService();
        $server->medalTrigger();
        return  true;
    }
}
