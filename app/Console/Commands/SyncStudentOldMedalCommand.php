<?php


namespace App\Console\Commands;


use App\ConstDir\Medal\MedalConst;
use App\Http\Services\Medal\MedalService;
use App\Utils\Common;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class SyncStudentOldMedalCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:medal';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步历史用户数据，构造勋章';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->syncLms();
        $this->syncWord();
        return true;
    }


    public function syncWord() {
        //直接同步单词勋章
        $url = env("WORD_API_URL") . "/api/sc/students/medal/info";
        $result = Common::getSimpleCurl($url);
        $result = json_decode($result, true);
        //存在6种勋章
        $part = [];
        collect(Arr::get($result, "data", []))->each(function ($item, $key) use (&$part) {
            collect($item)->each(function ($item) use ($key, &$part) {
                $progress = (int) Arr::get($item, "level", 0);
                $name = Arr::get($item, "name", "");
                if (!Arr::has($part, $name)) {
                    $part[$name] = [];
                }
                if ($progress) {
                    $part[$name][] = [
                        'id' => $key,
                        'progress' => $progress,
                        "step" => $progress,
                    ];
                }
            });
        });
        echo "In run word" . PHP_EOL;
        $server = new MedalService();
        foreach ($part as $medalType => $users) {
            echo $medalType . PHP_EOL;
            $server->addMedalProgress($medalType, $users, true);
        }
    }


    public function syncLms() {
        $sql = "SELECT t.ua_id as id,(SUM(h_c)+SUM(t_c)) AS progress FROM (
SELECT ua_id,count(*) AS h_c,0 AS t_c FROM lms_homework JOIN lms_homework_quest_student ON lms_homework.homework_id=lms_homework_quest_student.homework_id JOIN lms_user ON lms_user.id=lms_homework_quest_student.student_id AND lms_user.user_type_id=2 WHERE finish_time IS NOT NULL and finish_time != '0000-00-00 00:00:00' AND is_publish=1 and lms_homework.type = 1  GROUP BY student_id UNION ALL
SELECT ua_id,0 AS h_c,count(*) AS t_c FROM lms_test JOIN lms_test_with_student ON lms_test.test_id=lms_test_with_student.test_id JOIN lms_user ON lms_user.id=lms_test_with_student.student_id AND lms_user.user_type_id=2 WHERE finish_time IS NOT NULL and finish_time != '0000-00-00 00:00:00' AND is_publish=1 GROUP BY student_id) AS t GROUP BY t.ua_id";

        $result = DB::connection("lms")->select($sql);
        $result = collect($result)->map(function ($item){
            return [
                "id" => $item->id,
                "progress" => (int) $item->progress,
            ];
        });
        echo "in lms run" . PHP_EOL;
        //debug
        (new MedalService())->addMedalProgress(MedalConst::STUDY_INSIST, $result, true);
    }
}
