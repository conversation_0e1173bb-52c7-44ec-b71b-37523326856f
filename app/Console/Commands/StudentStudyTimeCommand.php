<?php


namespace App\Console\Commands;


use App\Models\Student\StudentStudyTimeModel;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class StudentStudyTimeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'study:time {init?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '学生学习时长';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //计算lms 的学习时长
        $init = $this->argument("init");
        if ($init) {
            $this->exec(30);
            return;
        }
        $this->exec(1);
        return;
    }
    public $needDays = 30;
    public $result = [];
    public $initData = [];

    public function exec($days = 1) {
        $start = date("Y-m-d", time() - 86400);
        for ($i = 0;$i < $this->needDays;$i ++) {
            $_day = date("Y-m-d", strtotime($start) - ($this->needDays - $i - 1) * 86400);
            $this->initData[$_day] = 0;
        }

        $this->result = collect(StudentStudyTimeModel::all())->mapWithKeys(function ($value) {
            return [$value->user_id => $value->record];
        })->toArray();
        for ($day = 0; $day < $days; $day ++) {
            $this->singleDate($start);
            $start = date("Y-m-d", strtotime($start) - 86400);
        }

        DB::beginTransaction();
        //保存
        foreach ($this->result as $uaId => $record) {
            //zip
//            $record["lms"] = collect($record["lms"])->sortKeys()->slice(count($record["lms"]) - $this->needDays, $this->needDays)->toArray();
//            $record["cas"] = collect($record["cas"])->sortKeys()->slice(count($record["cas"]) - $this->needDays, $this->needDays)->toArray();
            $record["total"] = collect($record["total"])->sortKeys()->slice(count($record["total"]) - $this->needDays, $this->needDays)->toArray();
            StudentStudyTimeModel::updateOrCreate(
                ["user_id" => $uaId],
                ["user_id" => $uaId, "record" => $record]
            );
        }
        DB::commit();
    }


    public function singleDate($day) {
        echo "Exec Student Time in {$day}" . PHP_EOL;
        $dayOfLms = $this->execLmsTimes($day);
        $dayOfCas = $this->execCasTimes($day);
        foreach ($dayOfLms as $uaId => $time) {
            if (!Arr::has($this->result, $uaId)) {
                $this->result[$uaId] = [
//                    "lms" => $this->initData,
//                    "word" => $this->initData,
//                    "cas" => $this->initData,
                    "total" => $this->initData,
                ];
            }
//            $this->result[$uaId]["lms"][$day] = (int) $time;
            if (!Arr::has($this->result, $uaId . ".total." . $day)) {
                $this->result[$uaId]["total"][$day] = (int) $time;
            } else {
                $this->result[$uaId]["total"][$day] += $time;
            }
        }
        foreach ($dayOfCas as $uaId => $time) {
            if (!Arr::has($this->result, $uaId)) {
                $this->result[$uaId] = [
//                    "lms" => $this->initData,
//                    "word" => $this->initData,
//                    "cas" => $this->initData,
                    "total" => $this->initData,
                ];
            }
//            $this->result[$uaId]["cas"][$day] = (int) $time;
            if (!Arr::has($this->result, $uaId . ".total." . $day)) {
                $this->result[$uaId]["total"][$day] = (int) $time;
            } else {
                $this->result[$uaId]["total"][$day] += $time;
            }
        }


    }


    public function execLmsTimes($day) {
        $start = $day . " 00:00:00";
        $end = $day . " 23:59:59";
        $sql = "SELECT ua_id, (SUM(t.t_time) + SUM(t.h_time)) as total_time FROM (
SELECT
	ua_id,SUM(used_time) as t_time,0 as h_time
FROM
	lms_test_section_score
	JOIN lms_test_with_student ON lms_test_with_student.test_id = lms_test_section_score.test_id
	JOIN lms_user on lms_user.id = lms_test_with_student.student_id
WHERE
	lms_test_with_student.is_finish = 1
	and lms_test_with_student.finish_time >= '{$start}'
	and lms_test_with_student.finish_time <= '{$end}'
GROUP BY
	lms_test_with_student.student_id
UNION ALL
SELECT
	ua_id,
	0 as t_time,
	(SUM(used_time) + SUM(used_time_other)) as h_time
FROM
	lms_student_answer JOIN lms_user on lms_student_answer.student_id = lms_user.id
    JOIN lms_homework ON lms_student_answer.homework_id = lms_homework.homework_id
WHERE
	test_id = 0
	AND is_finish = 1
	and lms_homework.type = 1
	AND lms_student_answer.created_at >= '{$start}'
	AND lms_student_answer.created_at <= '{$end}'
GROUP BY
	student_id) as t
	GROUP BY ua_id";
       return $this->execSql($sql, "lms");
    }

    public function execCasTimes($day) {
        $start = $day . " 00:00:00";
        $end = $day . " 23:59:59";
        if (env("APP_ENV", "") == "production") {
            $sql = "SELECT t.ua_id,(SUM(TIMESTAMPDIFF(MINUTE,t.start_time,t.end_time)) * 60) as total_time FROM (
SELECT l.start_time,l.end_time,u.ua_id FROM cas_lessoninfo l JOIN v_classes_for_sc vc ON vc.class_id=l.class_id AND vc.class_specification IN (8,11) JOIN crm_v_class_student v ON l.class_id=v.class_id AND v.STATUS IN (2,3,4,5,6) JOIN vtiger_account u ON u.accountid=v.student_id WHERE l.deleted=0 AND l.lesson_type=0 AND l.courses_status=1 AND l.start_time>= '{$start}' AND l.start_time<= '{$end}' UNION
SELECT l.start_time,l.end_time,u.ua_id FROM cas_lessoninfo l JOIN v_classes_for_sc vc ON vc.class_id=l.class_id AND vc.class_specification NOT IN (8,11) JOIN new_v_cas_lessonstudent2cas v ON l.id=v.lesson_id AND v.l_status IN (1,2,4) JOIN vtiger_account u ON u.accountid=v.student_id WHERE l.deleted=0 AND l.lesson_type=0 AND l.courses_status=1 AND l.start_time>= '{$start}' AND l.start_time<= '{$end}') t GROUP BY t.ua_id";
        } else {
            $sql = "SELECT t.ua_id,(SUM(TIMESTAMPDIFF(MINUTE,t.start_time,t.end_time)) * 60) as total_time FROM (
SELECT l.start_time,l.end_time,u.ua_id FROM cas_lessoninfo l JOIN v_classes vc ON vc.class_id=l.class_id AND vc.class_specification IN (8,11) JOIN crm_v_class_student v ON l.class_id=v.class_id AND v.STATUS IN (2,3,4,5,6) JOIN vtiger_account u ON u.accountid=v.student_id WHERE l.deleted=0 AND l.lesson_type=0 AND l.courses_status=1 AND l.start_time>= '{$start}' AND l.start_time<= '{$end}' UNION
SELECT l.start_time,l.end_time,u.ua_id FROM cas_lessoninfo l JOIN v_classes vc ON vc.class_id=l.class_id AND vc.class_specification NOT IN (8,11) JOIN new_v_cas_lessonstudent2cas v ON l.id=v.lesson_id AND v.l_status IN (1,2,4) JOIN vtiger_account u ON u.accountid=v.student_id WHERE l.deleted=0 AND l.lesson_type=0 AND l.courses_status=1 AND l.start_time>= '{$start}' AND l.start_time<= '{$end}') t GROUP BY t.ua_id";
        }
        return $this->execSql($sql, "cas_crm");
    }

    public function execSql($sql, $connect = "") {
        $r = DB::connection($connect)->select($sql);
        return collect($r)->mapWithKeys(function ($value) {
            return [$value->ua_id => $value->total_time];
        });
    }


}
