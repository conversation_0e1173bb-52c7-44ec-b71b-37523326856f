<?php

namespace App\Console\Commands;

use App\Http\Services\File\FileService;
use App\Models\Verify\UserData;
use App\Utils\QueueUtil;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class CheckCommentsReplayCommand extends Command
{
    protected $signature = 'check-replay:comments';

    protected $description = '话题评论是异步审核，若审核服务挂掉或异常可重新审核未审核的评论';

    public function handle(): void
    {
        $notCheckTalkComments = DB::select("SELECT a.id,a.talk_id,a.cont FROM `sc_talk_comments` a,`sc_talks` b WHERE a.talk_id=b.id and a.`status` = '审核中' and a.deleted_at is null and b.deleted_at is null");
        if (count($notCheckTalkComments) == 0) {
            echo 'no data';
        }
        foreach ($notCheckTalkComments as $checkTalkComment) {
            echo "talkComment_" . $checkTalkComment->id;
            $sourceCont = $checkTalkComment->cont;
            $pattern = '/<img[^>]+>/i';
            $replacement = '';
            $checkData = array();
            $filterCont = preg_replace($pattern, $replacement, $sourceCont);
            $checkData[] = array("targetType" => UserData::TargetType_TEXT, "content" => $filterCont, "only" => env("SENSITIVE_TEXT_ONLY", true));
            $userData = new UserData();
            $userData->classPath = "App\Http\Services\Talk\TalkService";
            $userData->methodName = "checkTalkComment";
            $userData->notifyData = array("id" => $checkTalkComment->id, "talkId" => $checkTalkComment->talk_id, "imgs" => array());
            $imgs = DB::select("SELECT a.img_id,b.file_path FROM `sc_comment_imgs` a,sc_file b where a.img_id=b.id and a.comment_id=$checkTalkComment->id");
            foreach ($imgs as $img) {
                $storage = Storage::disk(FileService::$filesystem);
                $imgContent = base64_encode($storage->get($img->file_path));
                $checkData[] = array("targetType" => UserData::TargetType_IMG, "content" => $imgContent, "platform" => env("CHECK_PLATFORM", "ali"), "fileUrl" => $storage->url($img->file_path), "dataId" => "talkComment_" . $checkTalkComment->id);
            }
            //待审核的具体数据
            $userData->data = $checkData;
            QueueUtil::verifyInfoAsync($userData);
        }
    }
}
