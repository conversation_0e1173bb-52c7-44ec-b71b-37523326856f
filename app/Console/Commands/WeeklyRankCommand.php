<?php

namespace App\Console\Commands;

use App\Http\Services\Point\PointService;
use Illuminate\Console\Command;

class WeeklyRankCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rank:week';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '每周结算排行榜';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        (new PointService())->weeklyRank();
    }


}