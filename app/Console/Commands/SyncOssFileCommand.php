<?php


namespace App\Console\Commands;


use App\Http\Services\File\FileService;
use App\Models\File\FileModel;
use App\Utils\Common;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class SyncOssFileCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:oss';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步oss 文件';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $storage = Storage::disk(FileService::$filesystem);
        $syncUrl = env("YK_URL") . "/api/mq/index";
        $list = FileModel::where("oss_status", 0)->where("status", 1)->where("check_status", 1)->where("user_id", "!=", "0")->get();
        $data = [
            "type" => 80000029,
            "body" => [
                "data" => [
                    "bucket" => "sh-res",
                    "folder" => env("APP_ENV", '') == 'production' ? "sc" : "sc_" . env("APP_ENV", ''),
                    "code" => "902002",
                    "watermark" => "Thinktown",
                    "id" => 0,
                    "url" => ""
                ],
                "callback" => env("APP_URL") . "/api/callback/oss",
            ]];
        foreach ($list as $item) {
            if ($storage->exists($item->file_path)) {
                $_data = $data;
                $_data["body"]["data"]["id"] = $item->id;
                $_data["body"]["data"]["url"] = $storage->url($item->file_path);
                $r = Common::postSimpleCurlByRawData($syncUrl, $_data);
                echo "Sync Oss File {$item->id}" . PHP_EOL;
                echo $r . PHP_EOL;
                $item->oss_status = FileModel::OSS_ING;
                $item->save();
            } else {
                //文件不存在
                $item->oss_status = FileModel::OSS_SYNC_ERROR;
                $item->save();
            }
        }
        return true;
    }
}
