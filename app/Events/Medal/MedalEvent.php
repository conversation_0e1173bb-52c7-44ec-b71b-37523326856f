<?php


namespace App\Events\Medal;


use App\Events\Event;

/**
 * 勋章事件
 * Class MedalEvent
 * @package App\Events\Medal
 */
class MedalEvent extends Event
{


   public $data = [];
   public $medalType;

    /**
     * MedalEvent constructor.
     * @param $medalType '勋章类型'
     * @param $uaId '用户ID'
     * @param $process '进度'
     */
    public function __construct($medalType, $data = [])
    {
        $this->medalType = $medalType;
        $this->data = $data;
    }
}
