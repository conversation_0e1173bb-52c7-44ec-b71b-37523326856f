<?php

namespace App\Utils;

use App\Jobs\LogJob;
use App\Jobs\VerifyQueueJob;
use App\Models\Verify\UserData;
use Closure;

class QueueUtil
{
    /** 同步执行审核逻辑,该方法返回$userData->methodName方法定义的返回值或者第二个参数闭包的返回值
     * @param UserData $data
     * $userData=new UserData();
     * $userData->classPath="App\Models\Verify\UserData\IndexController";
     * $userData->methodName="ok";
     * $userData->notifyData="{'id':6}";
     * $userData->data=$data;
     * QueueUtil::verifyInfoSync($userData);
     * @param $closure 闭包调用,可忽略$userData->classPath和$userData->methodName
     * @return mixed
     */
    public static function verifyInfoSync(UserData $data, $closure = null)
    {
        $userData = $data;
        $classPath = $userData->classPath;
        $methodName = $userData->methodName;
        $notifyData = $userData->notifyData;
        $arr = $userData->data;
        $retData = QueueUtil::verifyByThirdParty($arr, md5(json_encode($notifyData)));
        if ($closure instanceof Closure) {
            $ret = $closure($notifyData, $retData);
        } else {
            $ret = call_user_func_array(array($classPath, $methodName), array($notifyData, $retData));
        }
        return $ret;
    }

    public static function verifyByThirdParty($arr, $md5)
    {
        $url = env("VERIFY_URL");
        $retData = array();
        $headers = ['xkt' => env("VERIFY_KEY", "f451eef9705804ad8854bca5030ece84")];
        foreach ($arr as $item) {
            $targetType = $item['targetType'];
            $content = $item['content'];
            if ($targetType == UserData::TargetType_TEXT) {
                $target = $content;
                $replaceStr = $item['replaceStr'] ?? "*";
                $only = $item['only'] ?? "false";
                $data = ['target' => $target, "replaceStr" => $replaceStr, "env" => env("APP_ENV"), "only" => $only];
                $result = Common::postSimpleCurl($url . '/sensitive/replaceFast', $data, $headers);
                if ($result == "") {
                    dd('审核服务没有返回数据，请联系管理员');
                }
                $retJson = json_decode($result, true);
                $retArr = array();
                if ($retJson['code'] != 1) {
                    $retArr['pass'] = false;
                    $retArr['content'] = '';
                    $retArr['errorMessage'] = $retJson['errorMessage'];
                } else {
                    $retArr['pass'] = $retJson['result']['pass'];
                    $retArr['content'] = $retJson['result']['replaceSensitive'];
                    $retArr['errorMessage'] = $retJson['errorMessage'];
                }
                $retArr['targetType'] = UserData::TargetType_TEXT;
                $retArr['source'] = $retJson;
                $retData[] = $retArr;
            } else if ($targetType == UserData::TargetType_IMG) {
                $platform = $item['platform'] ?? "";
                $fileUrl = $item['fileUrl'] ?? "";
                $dataId = $item['dataId'] ?? "";
                if ($platform == 'ali' || $platform == '') {
                    if ($fileUrl == "") {
                        dd('如果平台是ali 只能用fileUrl');
                    }
                }
                $fileContent = $content;
                $data = ['dataId' => 'sc:' . time() . ':' . $md5 . ':' . $dataId, 'sync' => 1, 'fileContent' => $fileContent, 'platform' => $platform, 'fileUrl' => $fileUrl];
                $result = Common::postSimpleCurl($url . '/image/check', $data, $headers);
                if ($result == "") {
                    dd('审核服务没有返回数据，请联系管理员');
                }
                $retJson = json_decode($result, true);
                $retArr = array();
                if ($retJson['code'] != 1) {
                    $retArr['pass'] = false;
                    $retArr['content'] = '';
                    $retArr['errorMessage'] = $retJson['errorMessage'];
                } else {
                    $retArr['pass'] = $retJson['result']['pass'];
                    $retArr['content'] = '';
                    $retArr['errorMessage'] = $retJson['errorMessage'];
                }
                $retArr['targetType'] = UserData::TargetType_IMG;
                $retArr['source'] = $retJson;
                $retData[] = $retArr;
            }
        }
        return $retData;
    }

    /** 异步执行审核逻辑,审核成功会回调$userData->methodName方法，若失败，每10秒再回调一次共十次
     *$userData=new UserData();
     * $userData->classPath="App\Models\Verify\UserData\IndexController";
     * $userData->methodName="ok";
     * $userData->notifyData="{'id':6}";
     * $userData->data=$data;
     * QueueUtil::verifyInfoAsync($userData);
     */
    public static function verifyInfoAsync(UserData $data)
    {
        dispatch(new VerifyQueueJob($data))->onConnection('redis')->onQueue(env("REDIS_VERIFY_QUEUE", "SC_VERIFY_QUEUE" . "_" . env("APP_ENV")));
    }

    /** 记录操作日志和业务日志
     * @param $data
     * @return void
     */
    public static function log($data)
    {
        dispatch(new LogJob($data))->onConnection('redis')->onQueue(env("REDIS_LOG_QUEUE", "SC_LOG_QUEUE" . "_" . env("APP_ENV")));
    }
}
