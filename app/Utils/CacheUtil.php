<?php


namespace App\Utils;


use Illuminate\Support\Facades\Redis;

class CacheUtil
{

    public static function userVisitToday($userId, $visit = false) {
        $today = date("Y-m-d", time());
        $key = "sc::user:visit";
        if ($visit) {
            return Redis::hset($key, $userId, $today);
        } else {
            $date = Redis::hget("sc::user:visit", $userId);
            return $today == $date;
        }
    }

    /**
     * 获取用户上一次登录时间
     * @param $userId
     * @return mixed
     */
    public static function userLastVisitTime($userId)
    {
        return Redis::hget("sc::user:visit", $userId);
    }

    /**
     * 判断用户是否为本月首次登陆
     * @param $userId
     * @return bool
     */
    public static function firstLoginThisMonth($userId)
    {
        $lastTime = self::userLastVisitTime($userId);
        if (!$lastTime) return true;
        $thisMonth = date("Y-m", time());
        $lastMonth = substr($lastTime, 0, 7);
        return $thisMonth != $lastMonth;
    }
}
