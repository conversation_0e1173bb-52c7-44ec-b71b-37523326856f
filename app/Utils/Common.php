<?php


namespace App\Utils;


use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Logger;

class Common
{
    /**
     * 简单的post 请求
     *
     * @param [type] $url
     * @param array $parameters
     * @param array $headers
     * @return string|array
     */
    public static function postSimpleCurl($url, $parameters = [], $headers = [], $timeout = 30)
    {
        $client = new Client(['timeout' => $timeout, 'verify' => false]);
        $data = [];
        if ($parameters) {
            $data['form_params'] = $parameters;
        }
        if ($headers) {
            $data['headers'] = $headers;
        }
        $result = '';
        try {
            if ($data) {
                $response = $client->request('POST', $url, $data);
            } else {
                $response = $client->request('POST', $url);
            }
            $result = $response->getBody()->getContents();
        } catch (ClientException $e) {
            self::exceptionLog($e);
        } finally {
            return $result;
        }
    }

    /**
     * 简单的get请求
     *
     * @param [type] $url
     * @param array $parameters
     * @param array $headers
     * @return string
     */
    public static function getSimpleCurl($url, $parameters = [], $headers = [], $onlyResponseCode = false)
    {

        $client = new Client(['verify' => false]);
        $data = [];
        if ($headers) {
            $data['headers'] = $headers;
        }
        if (!empty($parameters)) {
            $parameters = http_build_query($parameters);
            $url = $url . '?' . $parameters;
        }
        $result = '';
        try {
            if ($data) {
                $response = $client->request('GET', $url, $data);
            } else {
                $response = $client->request('GET', $url);
            }
            if ($onlyResponseCode) {
                $result = $response->getStatusCode();
            } else {
                $result = $response->getBody()->getContents();
            }
        } catch (ClientException $e) {
            self::exceptionLog($e);
            if ($onlyResponseCode) {
                $result = 404;
            } else {
                return '';
            }
        } finally {
            return $result;
        }
    }

    /**
     * 异常记录日志
     * @param ClientException $e
     */
    public static function exceptionLog(ClientException $e)
    {
        $log = [
            'errorMsg' => $e->getMessage(),
            'request' => $e->getRequest(),
        ];
        if ($e->hasResponse()) {
            $log['response'] = $e->getResponse();
        }
        $monolog = new Logger('Http error');
        $monolog->pushHandler(new RotatingFileHandler(storage_path('logs/http/error.log')));
        $monolog->error('Http Error', $log);
    }

    public static function getCookieStr() {
        return request()->header("cookie", "");
    }


    /**
     * 机器人消息
     */
    public static function sendBotMessage($message, $type = 1, $member = [], $mobile = []) {
        $url = env('BOT_URL', 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send');
        $token = env('BOT_TOKEN', '');
        $url .= '?key=' . $token;
        $sendData = [];
        $messageType = 'text';
        if (mb_strlen($message) > 2048) {
            $message = mb_substr($message, 0, 2000);
        }
        switch ($type) {
            case 1:
                $messageType = 'text';
                $content = ['content' => $message];
                break;
        }
        $sendData['msgtype'] = $messageType;
        $sendData[$messageType] = $content;
        return self::postSimpleCurlByRawData($url, $sendData);
    }

    /**
     * 简单的get 请求 json
     *
     * @param [type] $url
     * @param array $parameters
     * @param array $headers
     * @return string|array
     */

    public static function postSimpleCurlByRawData($url, $parameters = [], $headers = [])
    {

        $client = new Client(['verify' => false]);
        $data = [];
        if ($parameters) {
            $data['json'] = $parameters;
        }
        if ($headers) {
            $data['headers'] = $headers;
        }
        $result = '';
        try {
            if ($data) {
                $response = $client->request('POST', $url, $data);
            } else {
                $response = $client->request('POST', $url);
            }
            $result = $response->getBody()->getContents();
        } catch (ClientException $e) {
            self::exceptionLog($e);
        } finally {
            return $result;
        }
    }

    /**
     * 简单的get 请求 json
     *
     * @param [type] $url
     * @param array $parameters
     * @param array $headers
     * @return string|array
     */

    public static function getSimpleCurlByRawData($url, $parameters = [], $headers = [])
    {

        $client = new Client(['verify' => false]);
        $data = [];
        if ($parameters) {
            $data['json'] = $parameters;
        }
        if ($headers) {
            $data['headers'] = $headers;
        }
        $result = '';
        try {
            if ($data) {
                $response = $client->request('GET', $url, $data);
            } else {
                $response = $client->request('GET', $url);
            }
            $result = $response->getBody()->getContents();
        } catch (ClientException $e) {
            self::exceptionLog($e);
        } finally {
            return $result;
        }
    }
    public static function getDateBetween($days = 0, $beginOfWeek = true, $startOfSun = false,  $endOfDay = false, $format = ""){
        $time = time();
        if ($beginOfWeek) {
            $day = date($startOfSun ? 'w' : 'N', $time);
            $day = $startOfSun ? $day + 1 : $day;
            $time = strtotime(date('Y-m-d', strtotime('-'.$day.' days', $time)));
        } else {
            $time = strtotime(date("Y-m-d", $time));
        }
        if ($endOfDay) {
            $time += 86400;
        }
        $r = [$time - $days * 86400, $time];
        if ($format) {
             $r[0] = date($format, $r[0]);
             $r[1] = date($format, $r[1]);
        }
        return $r;
    }


}

