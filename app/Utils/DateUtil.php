<?php

namespace App\Utils;

use Carbon\Carbon;

class DateUtil
{

    /**
     * 根据传入的日期计算每个月21号到次月20号的时间区间
     * 确保传入的日期属于计算出的时间区间内
     *
     * @param string $date 输入日期，格式：Y-m-d
     * @param bool $returnCarbon 是否返回Carbon对象，默认false返回格式化字符串
     * @return array ['start' => 开始时间, 'end' => 结束时间]
     */
    public static function getMonthlyPeriodRange($date, $returnCarbon = false)
    {
        $inputDate = Carbon::parse($date);

        // 判断传入日期是属于哪个周期
        if ($inputDate->day >= 21) {
            // 如果是21号及以后，周期是当月21号到次月20号
            $startDate = $inputDate->copy()->day(21)->startOfDay();
            $endDate = $inputDate->copy()->addMonth()->day(20)->endOfDay();
        } else {
            // 如果是20号及以前，周期是上月21号到当月20号
            $startDate = $inputDate->copy()->subMonth()->day(21)->startOfDay();
            $endDate = $inputDate->copy()->day(20)->endOfDay();
        }

        if ($returnCarbon) {
            return [
                'start' => $startDate,
                'end' => $endDate
            ];
        }

        return [
            'start' => $startDate->format('Y-m-d H:i:s'),
            'end' => $endDate->format('Y-m-d H:i:s')
        ];
    }

    /**
     * 获取用于数据库查询的between数组
     *
     * @param string $date 输入日期，格式：Y-m-d
     * @return array [开始时间, 结束时间] 用于whereBetween查询
     */
    public static function getMonthlyPeriodBetween($date)
    {
        $range = self::getMonthlyPeriodRange($date);
        return [$range['start'], $range['end']];
    }


    public static function getFinancialMonth($date, $format = 'Y-m-01') {
        //获取当前财务月
        $inputDate = Carbon::parse($date);

        // 判断传入日期是属于哪个周期
        if ($inputDate->day >= 21) {
            // 如果是21号及以后，周期是当月21号到次月20号
            $inputDate = $inputDate->addMonth()->startOfDay();
        }
        return $inputDate->format('Y-m-01');
    }

}
