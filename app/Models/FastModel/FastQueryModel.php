<?php

namespace App\Models\FastModel;

use App\Models\FastModelTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class FastQueryModel extends Model
{
    protected static $unguarded = true;
    use FastModelTrait;

    public static function navigationConfig(): Builder
    {
        return self::table("navigation_config");
    }

    public static function config(): Builder
    {
        return self::table("config");
    }

    public static function configOfTeacherEvaluate(): Builder
    {
        return self::config()->where("type", "TAP教师评价");
    }
    public static function configOfTeacherEvaluateV2(): Builder
    {
        return self::config()->where("type", "TAP教师评价V2");
    }

    public static function configOfGifts() {
        return self::config()->where("type", "like",  "CAST礼物配置%");
    }

    /**
     * 通过pas 获取学生与教师关联
     * @return Builder
     */
    public static function pasStudentWithTeacher(): Builder
    {
        return self::table(DB::raw("cas_crm.pas_ls"));
    }

    public static function pasStudentWithAbroad(): Builder
    {
        return self::table(DB::raw("cas_crm.sms_order"));
    }

    public static function pasPcaUser(): Builder
    {
        return self::table(DB::raw("cas_crm.pas_staffpca"));
    }

    public static function crmStudent(): Builder
    {
        return self::table(DB::raw("cas_crm.vtiger_account"));
    }

    public static function teacherEvaluateStudentRecord(): Builder
    {
        return self::table("teacher_evaluate_student_record");
    }

    public static function teacherEvaluate(): Builder
    {
        return self::table("teacher_evaluate");
    }

    public static function teacherEvaluateTargetUser(): Builder
    {
        return self::table("teacher_evaluate_target_user");
    }


    public static function crmOwner(): Builder {
        return self::table(DB::raw("cas_crm.vtiger_crmentity"));
    }

    public static function crmUser(): Builder {
        return self::table(DB::raw("cas_crm.v_crm_user"));
    }

    public static function teacherEvaluateRecordInfo(): Builder {
        return self::table("evaluate_record_info");
    }

    public static function teacherEvaluateTags(): Builder
    {
        return self::table("teacher_evaluate_tags");
    }

    public static function giftRecord()
    {
        return self::table("gift_record");
    }

    public static function teacherEvaluateShare(): Builder
    {
        return self::table("teacher_evaluate_share");
    }

    public static function teacherEvaluateShareInfo(): Builder
    {
        return self::table("teacher_evaluate_share_info");
    }
}
