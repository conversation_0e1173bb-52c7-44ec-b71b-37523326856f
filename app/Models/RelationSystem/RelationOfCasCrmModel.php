<?php

namespace App\Models\RelationSystem;

use App\Models\FastModelTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * cas_crm 关联的快速连接模型
 */
class RelationOfCasCrmModel extends Model
{
    use FastModelTrait;

    protected $connection = "cas_crm";


    /**
     * 查找目标学生的课表
     * 可能存在
     * @param string $type all 全部类型 | 一人班 ｜ 多人班 ｜ 模考 | 非模考
     * @return Builder[]|Collection
     */
    public static function lessons($id, $start, $end, $teachers, string $type = 'all')
    {
        $models = [];
        $selectLesson = ["cas_lessoninfo.start_time", "cas_lessoninfo.end_time", "cas_lessoninfo.id", "cas_lessoninfo.lessonid"
            , DB::raw("crm_item.name_cn AS subject_name"), DB::raw("cas_rooms.code AS room_name")
            , "v_campus.campus", DB::raw("vtiger_users.last_name as teacher_name")
        ];
        $selectMock = ["cas_lessoninfo_extra.start_time", "cas_lessoninfo_extra.end_time", "cas_lessoninfo_extra.id", "cas_lessoninfo_extra.lessonid"
            , DB::raw("'模考' AS subject_name"), DB::raw("cas_rooms.code AS room_name")
            , "v_campus.campus", DB::raw("vtiger_users.last_name as teacher_name")
        ];;
        if ('all' == $type || '模考' == $type) {
            $models[] = self::lessonMock($id, $start, $end)->select($selectMock);
        }

        if ('all' == $type || '多人班' == $type || '非模考' == $type) {
            $models[] = self::lessonMulti($id, $start, $end, $teachers)->select($selectLesson);
        }

        if ('all' == $type || '一人班' == $type || '非模考' == $type) {
            $models[] = self::lessonSingle($id, $start, $end, $teachers)->select($selectLesson);
        }
        $first = null;
        foreach ($models as $model) {
            if (!$first) {
                $first = $model;
            } else {
                $first->union($model);
            }
        }
        return $first->get();
    }

    public static function teachers($id, $start = '', $end = '', string $type = 'all')
    {
        $models = [];
        $select = [DB::raw("vtiger_users.last_name as name"), DB::raw("vtiger_users.id as id")];
        if ('all' == $type || '多人班' == $type) {
            $models[] = self::lessonMulti($id, $start, $end)->select($select)->groupBy(DB::raw("vtiger_users.id"));
        }

        if ('all' == $type || '一人班' == $type || '非模考' == $type) {
            $models[] = self::lessonSingle($id, $start, $end)->select($select)->groupBy(DB::raw("vtiger_users.id"));
        }
        $first = null;
        foreach ($models as $model) {
            if (!$first) {
                $first = $model;
            } else {
                $first->union($model);
            }
        }
        return $first->get();
    }

    /**
     * 学生的多人班课表
     * @param $uaId
     * @param $start
     * @param $end
     * @param array $teachers
     * @return Builder
     */
    public static function lessonMulti($uaId, $start, $end, array $teachers = []): Builder
    {
        $model = self::casLessonTable()
            ->join("new_v_cas_lessonstudent2cas", "new_v_cas_lessonstudent2cas.lesson_id", "=", "cas_lessoninfo.id")
            ->join("vtiger_account", "vtiger_account.accountid", "=", "new_v_cas_lessonstudent2cas.student_id")
            ->join("crm_item", "crm_item.itemid", "=", "cas_lessoninfo.subject_id")
            ->join("vtiger_users", "vtiger_users.id", "cas_lessoninfo.staff_id")
            ->leftJoin("cas_rooms", "cas_rooms.id", "=", "cas_lessoninfo.room_id")
            ->leftJoin("v_campus", "v_campus.campusid", "=", "cas_lessoninfo.teach_campus");

        $model->where("vtiger_account.ua_id", $uaId);
        $model
            ->when($start, function ($q) use ($start) {
                $q->where("cas_lessoninfo.start_time", ">=", $start);
            })
            ->when($end, function ($q) use ($end) {
                $q->where("cas_lessoninfo.start_time", "<=", $end);
            })
            ->where("cas_lessoninfo.deleted", 0)
            ->where("cas_lessoninfo.courses_status", 1)
            ->whereIn("new_v_cas_lessonstudent2cas.l_status", [1, 2, 4])
            ->when($teachers, function ($q) use ($teachers) {
                $q->whereIn("cas_lessoninfo.staff_id", $teachers);
            });
        return $model;
    }


    /**
     * 学生的一人班课表
     * @param $uaId
     * @param $start
     * @param $end
     * @param array $teachers
     * @return Builder
     */
    public static function lessonSingle($uaId, $start, $end, array $teachers = []): Builder
    {
        $model = self::casLessonTable()
            ->join("crm_v_class_student", "crm_v_class_student.class_id", "=", "cas_lessoninfo.class_id")
            ->join("vtiger_account", "vtiger_account.accountid", "=", "crm_v_class_student.student_id")
            ->join("crm_item", "crm_item.itemid", "=", "cas_lessoninfo.subject_id")
            ->join("vtiger_users", "vtiger_users.id", "cas_lessoninfo.staff_id")
            ->leftJoin("cas_rooms", "cas_rooms.id", "=", "cas_lessoninfo.room_id")
            ->leftJoin("v_campus", "v_campus.campusid", "=", "cas_lessoninfo.teach_campus");

        $model->where("vtiger_account.ua_id", $uaId);
        $model
            ->when($start, function ($q) use ($start) {
                $q->where("cas_lessoninfo.start_time", ">=", $start);
            })
            ->when($end, function ($q) use ($end) {
                $q->where("cas_lessoninfo.start_time", "<=", $end);
            })
            ->where("cas_lessoninfo.courses_status", 1)
            ->where("cas_lessoninfo.deleted", 0)
            ->whereIn("crm_v_class_student.status", [2, 3, 4, 5, 6])
            ->when($teachers, function ($q) use ($teachers) {
                $q->whereIn("cas_lessoninfo.staff_id", $teachers);
            });
        return $model;
    }

    /**
     * 学生的模考
     * @param $uaId
     * @param $start
     * @param $end
     * @return Builder
     */
    public static function lessonMock($uaId, $start, $end): Builder
    {
        $model = self::table("cas_lessoninfo_extra")
            ->join("crm_v_class_student", "crm_v_class_student.class_id", "=", "cas_lessoninfo_extra.class_id")
            ->join("vtiger_account", "vtiger_account.accountid", "=", "crm_v_class_student.student_id")
            ->leftJoin("vtiger_users", "vtiger_users.id", "cas_lessoninfo_extra.staff_id")
            ->leftJoin("cas_rooms", "cas_rooms.id", "=", "cas_lessoninfo_extra.room_id")
            ->leftJoin("v_campus", "v_campus.campusid", "=", "cas_lessoninfo_extra.teach_campus");

        $model->where("vtiger_account.ua_id", $uaId);
        $model->where("cas_lessoninfo_extra.start_time", ">=", $start)
            ->where("cas_lessoninfo_extra.start_time", "<=", $end)
            ->where("cas_lessoninfo_extra.deleted", 0)
            ->whereIn("crm_v_class_student.status", [2, 3, 4, 5, 6]);
        return $model;
    }

    public static function casLessonTable(): Builder
    {
        return self::table("cas_lessoninfo");
    }
}
