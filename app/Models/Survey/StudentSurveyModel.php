<?php

namespace App\Models\Survey;

use Illuminate\Database\Eloquent\Model;

class StudentSurveyModel extends Model
{
    protected $table = "student_survey";
    protected $primaryKey = 'student_survey_id';
    protected $hidden = ['show', 'status', 'start_time', 'end_time', 'created_at', 'updated_at', 'survey_id', 'first', 'show_time', 'complete_time'];
    protected $casts = [
        "ext" => "json",
    ];

    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const STATUS_COMPLETED = 2;
    const SHOWED = 1;
    const UNSHOW = 0;


    public function studentSurveyDetail()
    {
        return $this->hasMany(StudentSurveyDetailModel::class, "student_survey_id", "student_survey_id");
    }

    public function survey()
    {
        return $this->hasOne(SurveyModel::class, "survey_id", "survey_id");
    }

    /**
     * 调研展示过了
     * @return $this
     */
    public function show()
    {
        $this->show = self::SHOWED;
        $this->show_time = date("Y-m-d H:i:s");
        $this->save();

        //展示后判断已经展示所有调研中本次调研是不是第一个展示的，如果是，打上标记
        $firstSurvey = self::where([
            ["uaid", $this->uaid],
            ["show", "=", self::SHOWED],
        ])->orderby("first", "DESC")
            ->orderby("show_time", "ASC")
            ->first();
        if ($firstSurvey->student_survey_id == $this->student_survey_id) {
            $this->first = 1;
            $this->save();
        }
        return $this;
    }

    /**
     * 调研完成
     * @return $this
     */
    public function complete()
    {
        $this->status = self::STATUS_COMPLETED;
        $this->complete_time = date("Y-m-d H:i:s");
        $this->save();


        return $this;
    }
}
