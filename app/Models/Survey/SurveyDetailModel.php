<?php


namespace App\Models\Survey;


use Illuminate\Database\Eloquent\Model;

class SurveyDetailModel extends Model
{
    protected $table = "survey_detail";

    protected $primaryKey = "survey_detail_id";

    protected $casts = [
        "options" => "json",
        "tags" => "json",
        "ext" => "json",
    ];
    protected $hidden = ['created_at', 'updated_at', 'status', 'survey_id'];

    public function survey()
    {
        return $this->belongsTo(SurveyModel::class, "survey_id", "survey_id");
    }

    /**
     * 重组options
     * @param $value
     * @return array
     */
    public function getOptionsAttribute($value)
    {
        $options = json_decode($value, true) ?: [];
        $newOptions = [];
        foreach ($options as $key => $option) {
            if (array_key_exists("optionTags", $option)) {
                $option['optionTags'] = array_values($option['optionTags']);//标签去除key
            }
            $newOptions[] = $option;
        }
        return $newOptions;
    }
}
