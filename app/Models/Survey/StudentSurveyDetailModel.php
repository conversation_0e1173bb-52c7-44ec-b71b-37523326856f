<?php

namespace App\Models\Survey;

use Illuminate\Database\Eloquent\Model;

class StudentSurveyDetailModel extends Model
{
    protected $table = "student_survey_detail";
    protected $primaryKey = 'student_survey_detail_id';
    protected $guarded = [];
    protected $hidden = ['student_survey_id', 'created_at', 'updated_at'];

    protected $casts = [
        "option" => "json",
        "tags" => "json",
    ];

    public function survey()
    {
        return $this->belongsTo(StudentSurveyModel::class, "student_survey_id", "student_survey_id");
    }
}
