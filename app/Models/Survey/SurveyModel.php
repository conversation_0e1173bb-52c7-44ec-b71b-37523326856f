<?php


namespace App\Models\Survey;


use Illuminate\Database\Eloquent\Model;

class SurveyModel extends Model
{
    protected $table = "survey";
    protected $primaryKey = 'survey_id';
    protected $casts = [
        "ext" => "json",
        "regular" => "json",
        "target" => "json",
    ];
    protected $hidden = ['survey_id', 'status', 'regular_type', 'regular', 'target_type', 'rank', 'target', 'handle', 'start_time', 'end_time', 'created_at', 'updated_at'];

    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;

    const TARGET_TYPE_ALL = 0;
    const TARGET_TYPE_STATIC = 1;
    const TARGET_TYPE_DYNAMIC = 2;
    const REGULAR_TYPE_FREE = 0;
    const REGULAR_TYPE_MONTHLY = 1;


    public function surveyDetails()
    {
        return $this->hasMany(SurveyDetailModel::class, "survey_id", "survey_id")->where("survey_detail.status", 1)->orderBy("survey_detail.sequence");
    }

}
