<?php

namespace App\Models\Message;

use Illuminate\Database\Eloquent\Model;

/**
 * 消息
 */
class StuMessageModel extends Model
{
    /**
     * 任务消息
     */
    const MSGTYPE_1 = 1;

    //定义主键
    /**
     * 积分消息
     */
    const MSGTYPE_2 = 2;
    /**
     * 评论消息
     */
    const MSGTYPE_3 = 3;
    /**
     * 系统消息
     */
    const MSGTYPE_4 = 4;
    /**
     *LMS
     */
    const FROMS_1 = 1;
    /**
     *百万单词
     */
    const FROMS_2 = 2;
    /**
     *修改昵称成功
     */
    const FROMS_3 = 3;
    /**
     *修改昵称失败
     */
    const FROMS_4 = 4;
    /**
     *评论审核通过
     */
    const FROMS_5 = 5;
    /**
     *评论审核未通过
     */
    const FROMS_6 = 6;
    /**
     *修改昵称失败返还积分
     */
    const FROMS_7 = 7;
    /**
     *头像审核通过
     */
    const FROMS_8 = 8;
    /**
     *头像审核未通过
     */
    const FROMS_9 = 9;
    /**
     *后台删除评论
     */
    const FROMS_10 = 10;
    /**
     *积分消息
     */
    const FROMS_11 = 11;
    /**
     *评论消息
     */
    const FROMS_12 = 12;
    /**
     *未读取
     */
    const STATUS_1 = 1;
    /**
     *已读取
     */
    const STATUS_2 = 2;
    /**
     *话题
     */
    const PARTY_TYPE_1 = 1;
    /**
     *评论
     */
    const PARTY_TYPE_2 = 2;
    /**
     *踩
     */
    const PARTY_TYPE_3 = 3;
    /**
     *赞
     */
    const PARTY_TYPE_4 = 4;
    /**
     *LMS
     */
    const PARTY_TYPE_5 = 5;
    /**
     *百万单词
     */
    const PARTY_TYPE_6 = 6;
    const UPDATED_AT = null;
    protected $table = "stu_message";
    protected $primaryKey = "id";
}
