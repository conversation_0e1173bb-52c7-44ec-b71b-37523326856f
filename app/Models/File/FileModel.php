<?php


namespace App\Models\File;


use Illuminate\Database\Eloquent\Model;

class FileModel extends Model
{
    //同步成功
    const OSS_SUCCESS = 1;
    //同步中
    const OSS_ING = 2;
    //未同步
    const OSS_UN_UPLOAD = 0;
    //同步回调结果失败
    const OSS_CALLBACK_ERROR = 4;
    //同步失败
    const OSS_SYNC_ERROR = 3;

    protected $table = "file";
    protected $fillable = ["user_id", "file_path", "file_name", "oss_path", "oss_status", "status", "check_status", "created_at", "updated_at", "deleted_at"];

    protected $casts = [
        "oss_path" => "json"
    ];
}
