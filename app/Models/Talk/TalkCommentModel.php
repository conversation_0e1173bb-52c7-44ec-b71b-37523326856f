<?php

namespace App\Models\Talk;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 评论
 */
class TalkCommentModel extends Model
{
    use SoftDeletes;

    /**
     *审核中
     */
    const STATUS_1 = 1;
    const STATUS_1_STR = '审核中';
    /**
     *审核已过
     */
    const STATUS_2 = 2;
    const STATUS_2_STR = '审核已过';
    /**
     *审核未过
     */
    const STATUS_3 = 3;
    const UPDATED_AT = null;
    protected $table = "talk_comments";
    protected $primaryKey = "id";
    protected $dates = ['deleted_at'];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->format('Y-m-d H:i:s');
    }
}
