<?php


namespace App\Models\ExchangeStore;


use App\Models\File\FileModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ExchangeStoreModel extends Model
{
    protected $table = "exchange_store";

    protected $casts = [
        "ext" => "json"
    ];

    /**
     * 获取资源对应文件
     * @return HasOne
     */
    public function file()
    {
        return $this->hasOne(FileModel::class, "id", "resource_id");
    }

}
