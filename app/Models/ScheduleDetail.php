<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ScheduleDetail extends Model
{
    use SoftDeletes;

    const FORM_MAP = [
        0 => 'custom',
        1 => 'million',
        2 => 'cas',
    ];

    const TYPE_MAP = [
        0 => 'custom',
        1 => 'drag',
        2 => 'class'
    ];

    const FREEZE = 1;
    const UN_FREEZE = 0;

    protected $table = 'schedule_detail';
    protected $fillable = ['name', 'remark', 'from', 'type', 'extension', 'is_freeze'];

    public function getTotalAttribute()
    {
        $json = json_decode($this->attributes['extension'], true);
        return $json["total"] ?? null;
    }

    public function getFinishAttribute()
    {
        $json = json_decode($this->attributes['extension'], true);
        return $json["finish"] ?? null;
    }

    public function getCampusAttribute()
    {
        $json = json_decode($this->attributes['extension'], true);
        return $json["campus"] ?? null;
    }

    public function getRoomAttribute()
    {
        $json = json_decode($this->attributes['extension'], true);
        return $json["room"] ?? null;
    }

    public function getFeedbackAttribute()
    {
        $json = json_decode($this->attributes['extension'], true);
        return $json["feedBack"] ?? null;
    }

    public function getSubjectAttribute()
    {
        $json = json_decode($this->attributes['extension'], true);
        return $json["subject"] ?? null;
    }

    public function getTeacherNameAttribute()
    {
        $json = json_decode($this->attributes['extension'], true);
        return $json["teacherName"] ?? null;
    }

    public function getPlanTypeAttribute()
    {
        $json = json_decode($this->attributes['extension'], true);
        return $json["planType"] ?? null;
    }

    public function getDescriptionAttribute()
    {
        $json = json_decode($this->attributes['extension'], true);
        return $json["description"] ?? null;
    }
}
