<?php

namespace App\Models\UaBanner;

use Illuminate\Database\Eloquent\Model;

class BannerModel extends Model
{
    const POSITION_1 = 1;#数学竞赛
    const POSITION_2 = 2;#workshop

    protected $table = 'ua_banner';
    protected $primaryKey = 'id';

    protected $casts = [
        'extension_json' => 'array',
    ];

//    public function setClassCodeAttribute($value)
//    {
//        $this->attributes['extension_json'] = json_encode(['class_code' => $value]);
//    }
//
    public function getClassCodeAttribute()
    {
        return $this->extension_json['class_code'] ?? '';
    }

    public function click()
    {
        return $this->hasMany(BannerClickModel::class, 'banner_id', 'id');
    }
}
