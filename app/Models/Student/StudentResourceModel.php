<?php

namespace App\Models\Student;

use App\Models\File\FileModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class StudentResourceModel extends Model
{
    protected $table = 'student_resource';
    protected $primaryKey = 'id';
    protected $fillable = ['uaid', 'type', 'resource_id', 'status', 'ext', 'created_at', 'updated_at', 'start_time', 'end_time', "check_id"];

    protected $casts = [
        "ext" => "json"
    ];

    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const STATUS_CHECKING = 2;
    const STATUS_CHECKING_FAILED = 3;

    const TYPE_AVATER = 1;
    const TYPE_AVATER_FRAME = 2;
    const TYPE_SKIN = 3;
    const TYPE_NICKNAME = 4;

    const ENDTIME = "2199-12-21 00:00:00";


    /**
     * 获取资源对应文件，获取时要明确对应资源直接对应文件，不然会获取错误数据
     * @return HasOne
     */
    public function file()
    {
        return $this->hasOne(FileModel::class, "id", "resource_id");
    }

}
