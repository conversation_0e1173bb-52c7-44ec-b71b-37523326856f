<?php

namespace App\Models\Student;

use App\Models\ExchangeStore\ExchangeStoreModel;
use App\Models\File\FileModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class StudentInfoModel extends Model
{
    protected $table = 'student_info';
    protected $primaryKey = 'uaid';
    protected $keyType = 'string';
    protected $fillable = ['uaid', 'code', 'gender', 'level', 'name', 'nickname', 'nickname_checking', 'avater', 'avater_checking', 'avater_frame', 'skin', 'target', 'point', 'history_point', 'nickname_change'];
    protected $appends = ['crm_info'];

    protected $hidden = [];

    /**
     * 获取头像相关所有信息
     * 文件复合信息太多，用模型调取有点冗余
     * @return $this
     */
    public function avaterInfo()
    {
        //正在用的
        $avater = DB::table("file")->where("id", $this->avater)->first();
        $this->avater_url = $avater ? $avater->file_path : null;
        //审核中的
        $avater_checking = DB::table("file")->where("id", $this->avater_checking)->first();
        $this->avater_checking_url = $avater_checking ? $avater_checking->file_path : null;
        //头像框
        $frame = ExchangeStoreModel::where("id", $this->avater_frame)->first();
        $this->avater_frame_info = $frame ? $frame->ext : null;
        $defaultId = $this->gender == 0 ? env("DEFAULT_AVATAR_1") : env("DEFAULT_AVATAR_2");
        $default_avater = DB::table("file")->where("id", $defaultId)->first();
        $this->avater_default_url = $default_avater ? $default_avater->file_path : null;

        return $this;
    }

    /**
     * 获取皮肤相关信息
     * @return $this
     */
    public function skinInfo()
    {
        $skin = DB::table("exchange_store")->where("id", $this->skin)->first();
        if ($skin) {
            $ext = $skin->ext;
            $json = json_decode($ext);
        }
        $this->skin_info = $json ?? '';
        return $this;
    }

    /**
     * 获取等级信息
     * @return $this
     */
    public function studentLevel($withLevel = false)
    {
        //获取等级全局配置
        $grades = DB::table('grade')
            ->Join('file', 'grade.resource_id', '=', 'file.id')
            ->select(DB::raw("sc_grade.id as grade_id"), 'grade.title', 'grade.lower_score', 'grade.upper_score', 'grade.resource2', 'file.file_path')
            ->orderBy('grade.lower_score')
            ->get();
        $nextPoint = 0;
        $current = null;
        $next = null;
        $flag = false;
        foreach ($grades as &$grade) {
            $grade->resource2 = json_decode($grade->resource2);
            if ($flag) {
                $next = $grade;
                $flag = false;
            }
            if ($grade->grade_id == $this->level) {
                $current = $grade;
                $nextPoint = $grade->upper_score - $this->history_point + 1;
                $flag = true;
            }
        }
        $this->next_point = ($next && $nextPoint > 0) ? $nextPoint : 0;
        $this->current_level = $current ? $current->title : null;
        $this->current_level_pic = $current ? $current->file_path : null;
        $this->current_level_gap = $current ? $current->upper_score - $current->lower_score : 0;
        $this->next_level = $next ? $next->title : null;
        $this->next_level_pic = $next ? $next->file_path : null;
        if ($withLevel) $this->levels = $grades;
        return $this;
    }


    public function avaterFile()
    {
        return $this->hasOne(FileModel::class, "id", "avater");
    }

    public function avaterCheckingFile()
    {
        return $this->hasOne(FileModel::class, "id", "avater_checking");
    }

    public function avaterFrameInfo()
    {
        return $this->hasOne(ExchangeStoreModel::class, "id", "avater_frame");
    }

    public function resources()
    {
        return $this->hasMany(StudentResourceModel::class, "uaid", "uaid");
    }

    public function resourceFiles()
    {
        return $this->hasManyThrough(FileModel::class, StudentResourceModel::class, "uaid", "id", "uaid", "resource_id");
    }

    /**
     * 获取学员crm数据
     * @return mixed
     */
    public function getCrmInfoAttribute()
    {
        if (array_key_exists('crm_info', $this->attributes)) {
            return $this->attributes['crm_info'];
        }
        $uaId = $this->uaid;
        $sql = "select * from cas_crm.vtiger_account where ua_id = ?";
        $info = Db::connection("cas_crm")->selectOne($sql, [$uaId]);
        $this->crm_info = $info;
        return $info;
    }

}
