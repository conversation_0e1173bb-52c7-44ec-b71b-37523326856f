<?php

namespace App\Models\Verify;

class UserData
{
    /**
     * 标识是文本审核,不用base64编码
     */
    const TargetType_TEXT = 1;
    /**
     * 标识是图片审核,图片时候content需是图片内容的base64编码
     */
    const TargetType_IMG = 2;
    /**
     * 回写的类名全称，例如App\Models\Verify\UserData
     */
    public $classPath = "";
    /**
     * 回写的静态方法名字，方法格式如：ok($notifyData, $retData)
     */
    public $methodName = "";
    /**
     * 想要回写时携带的数据
     */
    public $notifyData = "";
    /**
     * 待审核的数据，格式为array(array("targetType"=>UserData::TargetType_TEXT,"content"=>"测试","only"=>false),array("targetType"=>UserData::TargetType_IMG,"content"=>"图片内容的base64编码","platform"=>"默认ali 目前先用ali 可选：ali or tencent","fileUrl"=>"如果平台是ali 只能用fileUrl","dataId"=>"图片审核时传的值会回传回来"))
     */
    public $data = array();
}
