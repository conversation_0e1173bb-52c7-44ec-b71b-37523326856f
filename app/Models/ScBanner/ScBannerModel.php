<?php


namespace App\Models\ScBanner;


use App\Models\Talk\TalkModel;
use App\Models\UaBanner\BannerModel;
use Illuminate\Database\Eloquent\Model;

class ScBannerModel extends Model
{
    protected $table = "banner";

    const UA_BANNER = [1, 3];
    const SC_TALK = [2];

    protected $casts = [
        "ext" => 'json'
    ];

    public function uaBanner()
    {
        return $this->hasOne(BannerModel::class, "id", "resource_id");
    }

    public function talk()
    {
        return $this->hasOne(TalkModel::class, "id", "resource_id");
    }

}
