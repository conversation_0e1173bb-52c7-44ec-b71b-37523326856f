<?php

namespace App\Models\InterimReport;

use App\Models\Student\StudentInfoModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentInterimReportModel extends Model
{
    use SoftDeletes;
    protected $table = "student_interim_report";

    protected $fillable = ["report_name", "student_id", "create_user", "status", "report_start", "report_end", "source"];

    protected $casts = [
        "source" => "json"
    ];


    public function student()
    {
        return $this->hasOne(StudentInfoModel::class, "uaid", "student_id");
    }

    public function infos()
    {
        return $this->hasMany(StudentInterimReportInfoModel::class, "report_id", "id");
    }

    public function teacherFeedback()
    {
        return $this->hasMany(StudentInterimReportTeacherModel::class, "report_id", "id");
    }
}
