<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Schedule extends Model
{
    use SoftDeletes;

    const TIME_LIME = [
        ['startTime' => '7:55', 'endTime' => '8:40',],
        ['startTime' => '8:45', 'endTime' => '9:30',],
        ['startTime' => '9:35', 'endTime' => '10:20',],
        ['startTime' => '10:25', 'endTime' => '11:10',],
        ['startTime' => '11:15', 'endTime' => '12:00',],
        ['startTime' => '12:05', 'endTime' => '12:55',],
        ['startTime' => '13:00', 'endTime' => '13:45',],
        ['startTime' => '13:50', 'endTime' => '14:35',],
        ['startTime' => '14:40', 'endTime' => '15:25',],
        ['startTime' => '15:30', 'endTime' => '16:15',],
        ['startTime' => '16:20', 'endTime' => '17:05',],
        ['startTime' => '17:10', 'endTime' => '17:55',],
        ['startTime' => '18:00', 'endTime' => '18:45',],
        ['startTime' => '18:50', 'endTime' => '19:35',],
        ['startTime' => '19:40', 'endTime' => '20:25',],
    ];

    protected $table = 'schedule';
    protected $fillable = ['schedule_date', 'begin', 'end', 'owner_uaid', 'creator_uaid', 'all_day'];

    public function detail()
    {
        return $this->hasOne(ScheduleDetail::class, 'schedule_id', 'id');
    }

    public function creator()
    {
        return $this->hasOne(User::class, 'ua_id', 'creator_uaid');
    }
}
