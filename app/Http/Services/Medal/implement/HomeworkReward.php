<?php


namespace App\Http\Services\Medal\implement;


use App\ConstDir\Medal\MedalConst;
use App\ConstDir\ThirdPart\ThirdPartMessageConst;
use App\Http\Services\Medal\RewardMedal;
use App\Http\Services\Point\PointService;
use App\Models\Medal\MedalModel;
use App\Models\Medal\UserMedalModel;
use App\Models\ThirdPartRecordModel;
use App\Utils\Common;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

/**
 * 作业实现
 * Class HomeworkReward
 * @package App\Http\Services\Medal\implement
 */
class HomeworkReward implements RewardMedal
{

    public function reward()
    {
        //作业达人、作业王者、作业专家
        $this->rewardOfHomework();

    }

    /**
     * 作业达人
     */
    public function rewardOfHomework($day = 35)
    {
        $format = "Y-m-d H:i:s";
        $days = Common::getDateBetween($day, true, false,  true, $format);
        $lastWeekStr = date("YW", strtotime($days[1]) - 1);
        //完成作业统计
        $lastWeek = ThirdPartRecordModel::selectRaw("user_id,count(*) as total, sc_date_of_year.year_week")->where("platform", ThirdPartMessageConst::LMS)->whereIn("source_type", [ThirdPartMessageConst::LMS_FINISH_HOMEWORK, ThirdPartMessageConst::LMS_FINISH_TEST])
            ->join("date_of_year", "date_of_year.date_id", "=", "third_part_record.record_date")
            ->where("record_time", ">=", $days[0])
            ->where("record_time", "<", $days[1])
            ->groupBy("user_id", "date_of_year.year_week")
            ->get();
        $medals = MedalModel::whereIn("name", [MedalConst::H_FIRST, MedalConst::H_SECOND, MedalConst::H_THIRD])->with(['medalDetail'])->get();
        $reward = [
            MedalConst::H_FIRST => [],
            MedalConst::H_SECOND => [],
            MedalConst::H_THIRD => []
        ];
        $lastWeek = collect($lastWeek)->groupBy("user_id");
        $lastWeek->each(function ($value, $userId) use (&$reward, $lastWeekStr) {
            $value = collect($value)->sortByDesc("year_week");
            $value = $value->values();
            if ($value->count() == 5) {
                //作业专家
                $reward[MedalConst::H_THIRD][] = $userId;
            }
            $first = $value->first();
            if ($first['year_week'] != $lastWeekStr) {
                //说明这周没有作业
                return true;
            }
            if ($first['total'] >= 5) {
                $reward[MedalConst::H_FIRST][] = $userId;
                $second = $value->get(1);
                if ($second && $second['total'] >= 5) {
                    $reward[MedalConst::H_SECOND][] = $userId;
                }
            }
            return true;
        });
        //作业达人、作业王者、作业专家
        //新建的需要做以下这些事情
        //1. insert user_medal
        //2. insert user_medal_log
        //3. reward user score
        $insert = [
            'user_medal' => [],
            'user_medal_log' => [],
            'score' => [],
        ];
        $time = date("Y-m-d H:i:s", time());
        foreach ($medals as $medal) {
            $medalDetail = $medal->medalDetail->first();
            if (!$medalDetail) {
                continue;
            }
            if ($users = $reward[$medal->name]) {
                $rewarded = UserMedalModel::where("medal_id", $medal->id)->whereIn("user_id", $users)->get()->pluck("user_id");
                $canReward = collect($users)->diff($rewarded);
                foreach ($canReward as $userId) {
                    $insert['user_medal'][] = [
                        'user_id' => $userId,
                        'medal_id' => $medal->id,
                        'medal_detail_id' => $medalDetail->id,
                        'progress' => 1,
                        'created_at' => $time,
                    ];
                    $insert['user_medal_log'][] = [
                        'user_id' => $userId,
                        'medal_detail_id' => $medalDetail->id,
                        'source_id' => 0,
                        'source_type' => 'schedule',
                        'from' => 'sc',
                        'reward_score' => $medalDetail->reward_score,
                        'created_at' => $time,
                    ];
                    $insert['score'][] = [
                        'userId' => $userId,
                        'medalName' => $medal->name,
                        'point' => $medalDetail->reward_score,
                        'medalLevel' => $medalDetail->level,
                    ];
                }
            }

        }
        //处理
        $pointService = new PointService();
        DB::beginTransaction();
        //todo 积分资源生成
        foreach ($insert as $table => $values) {
            if ($table == "score") {
                if ($values) {
                    foreach ($values as $item) {
                        call_user_func_array([$pointService, 'medalPoint'], $item);
                    }
                }
                continue;
            }
            if ($values) {
                if ($table == "user_medal") {
                    DB::table($table)->insert($values);
                }
                if ($table == "user_medal_log") {
                    $_drop = [];
                    foreach ($values as $item) {
                        $id = DB::table($table)->insertGetId($item);
                        $_drop[] = [
                            "user_id" => Arr::get($item, "user_id"),
                            "medal_log_id" => $id,
                            "status" => 0,
                        ];
                    }
                    DB::table("medal_drop")->insert($_drop);
                }

            }
        }
        DB::commit();

    }
}
