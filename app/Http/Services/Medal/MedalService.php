<?php


namespace App\Http\Services\Medal;

use App\ConstDir\Medal\MedalConst;
use App\Http\Services\Medal\implement\HomeworkReward;
use App\Http\Services\Point\PointService;
use App\Models\Medal\MedalCategoryModel;
use App\Models\Medal\MedalDropModel;
use App\Models\Medal\MedalModel;
use App\Models\Medal\UserMedalLogModel;
use App\Models\Medal\UserMedalModel;
use App\Utils\Common;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * 勋章相关服务
 * Class MedalService
 * @package App\Http\Services\Medal
 */
class MedalService
{
    /**
     * 用户的勋章列表
     */
    public function userMedalList($onlyDrop = false)
    {
        $r = [
            "drop" => [],
            "medal" => [],
        ];
        $uaId = Auth::user()->ua_id;
        if ($onlyDrop) {
            $r["drop"] = $this->userMedalDrop($uaId);
        } else {
            $r['medal'] = $this->userMedal($uaId);
        }
        return $r;
    }

    public function userMedalDrop($uaId)
    {
        //todo 同一种勋章掉多次需要改成 Xn
        $drop = MedalDropModel::where("user_id", $uaId)
            ->where("status", 0)
            ->with(["userMedalLog", "userMedalLog.medalDetail", "userMedalLog.medalDetail.medal"])
            ->get();
        $dropResult = [];
        $dropResultSingle = [
            'id' => "",
            "image" => [],
            "image2" => [],
            "name" => "",
            "type" => 1,
            "level" => "",
            "drop_num" => 1
        ];
        collect($drop)->each(function ($item) use (&$dropResult, $dropResultSingle) {
            $medal = Arr::get($item, "userMedalLog.medalDetail.medal");
            $dropResultSingle["image"] = Arr::get($item, "userMedalLog.medalDetail.image");
            $dropResultSingle["image2"] = Arr::get($item, "userMedalLog.medalDetail.image2");
            $dropResultSingle["level"] = Arr::get($item, "userMedalLog.medalDetail.level");
            $dropResultSingle["name"] = Arr::get($item, "userMedalLog.medalDetail.medal.name");
            $dropResultSingle["id"] = $item->id . "";
            $medalDetailId = Arr::get($item, "userMedalLog.medalDetail.id", 0);
            if ($medal) {
                $dropResultSingle["type"] = $medal->type;
                if ($medal->type == 1) {
                    //构造
                    $dropResult[$medalDetailId] = $dropResultSingle;
                } else {
                    //需要聚合
                    if (!Arr::has($dropResult, $medalDetailId)) {
                        $dropResult[$medalDetailId] = $dropResultSingle;
                    } else {
                        $dropResult[$medalDetailId]["id"] = $dropResult[$medalDetailId]["id"] . "," . $item->id;
                        $dropResult[$medalDetailId]["drop_num"] += 1;
                    }
                }
            }
        });
        //需要告诉前端的内容
        return array_values($dropResult);
    }

    public function userMedal($uaId, $needWordProgress = true)
    {
        $medal = MedalModel::with(["medalDetail" => function($q) {
            $q->orderBy("step");
    }])->get();
        $userMedal = UserMedalModel::where("user_id", $uaId)->get();
        $userMedal = collect($userMedal)->groupBy("medal_id");
        //获取学员百万单词的进度
        $userWordMedalProgress = collect();
        if ($needWordProgress) {
            $userWordMedalProgress = Cache::remember("sc:user:wordMedal:" . $uaId, 3600, function () {
                return $this->userWordMedalProgress();
            });
        }
        foreach ($medal as $item) {
            $details = collect($item->medalDetail);
            $show = $details->first();
            if ($show) {
                $show = clone $show;
                $show->avtive = false;
                if ($item->type == 1) {
                    $item->desc = sprintf($item->desc, $show->progress);
                }
                //重置默认进度防止进度异常
                $show->progress = 0;
                $show->level = "";
            }
            if ($userMedal->has($item->id)) {
                //如果用户激活了
                $userMedalItem = $userMedal->get($item->id)->first();
                $_show = $details->first(function ($item) use ($userMedalItem) {
                    return $item->id == $userMedalItem->medal_detail_id;
                });
                if ($_show) {
                    $_show = clone $_show;
                    $_show->progress = $userMedalItem->progress;
                    $show = $_show;
                    $show->avtive = true;
                    if ($item->type == 1) {
                        $item->desc = sprintf($item->desc, $show->progress);
                    }
                    if ($userWordMedalProgress->has($item->name)) {
                        //如果匹配
                        $show->progress = $userWordMedalProgress->get($item->name);
                    }
                }
            }
            $item->show = $show;
        }
        $list = $medal;
        return $list;
    }

    public function userWordMedalProgress() {
        $uri = env("WORD_API_URL") . "/api/medal/list";
        $result = Common::postSimpleCurl($uri, [], ['cookie' => Common::getCookieStr()]);
        $result = json_decode($result, true);
        $result = collect(Arr::get($result, "data.medal", []))->mapWithKeys(function ($item) {
            return [Arr::get($item, 'name', '') => Arr::get($item, 'user_progress', 0)];
        });
        return $result;
    }


    public function categoryMedal() {
        $category = MedalCategoryModel::with("iconImg")->where("status", 1)->orderBy("sort")->get();
        $userMedal = $this->userMedal(Auth::user()->ua_id, true);
        foreach ($category as $item) {
            $detail = [];
            foreach ($userMedal as $medal) {
                if ($medal->category_id == $item->id) {
                    $detail[] = $medal;
                }
            }
            $item->detail = $detail;
            $item->icon = optional($item->iconImg)->file_path ?? "";
        }
        return $category;
    }

    /**
     * 勋章触发器 触发达到条件用户勋章，并触发掉落
     */
    public function medalTrigger()
    {
        //首先需要知道那些勋章需要触发，触发条件如何

        //根据条件获取所有用户对应触发条件的值，判断是否能够达到触发条件

        //触发的入库，更新积分等后续增量操作
        $lms = new HomeworkReward();
        $lms->reward();
    }

    /**
     * 勋章触发来源外部
     */
    public function medalTriggerFromThirdPart($data)
    {
        $data = is_string($data) ? json_decode($data, true) : $data;
        $uaId = Arr::get($data, "user_id", "");
        $ext = Arr::get($data, "ext", []);
        $medalName = Arr::get($ext, "name", "");
        $medalLevel = Arr::get($ext, "version", "");
        $medalNum = (int)Arr::get($ext, "nums", 1);
        $sourceId = (int)Arr::get($data, "sourceId", 0);
        $sourceType = Arr::get($data, "type", "");
        $from = Arr::get($data, "from", "millionWord");
        $medal = MedalModel::where("name", $medalName)->with(["medalDetail" => function ($q) {
            $q->orderBy('step');
        }])->first();
        if (!$medal) {
            return false;
        }
        $isCountType = $medal->type == 2;
        $detail = $medal->medalDetail;

        if ($isCountType) {
            $active = $detail->first();
        } else {
            $active = collect($detail)->first(function ($item) use ($medalLevel) {
                return $item->level == $medalLevel;
            });
        }
        if (!$active) {
            //message active
            echo "not active";
            return false;
        }
        $userMedal = UserMedalModel::where("user_id", $uaId)->where("medal_id", $medal->id)->first();
        if (!$userMedal) {
            $userMedal = new UserMedalModel();
            $userMedal->user_id = $uaId;
            $userMedal->medal_id = $medal->id;
            $nowActiveStep = -1;
        } else {
            $nowActiveStep = collect($detail)->first(function ($item) use ($userMedal) {
                return $item->id == $userMedal->medal_detail_id;
            });
            $nowActiveStep = $nowActiveStep ? $nowActiveStep->step : -1;
        }
        //添加log日志
        //更新进度
        //下发积分
        //todo

        $insertMedalLog = [
            "user_id" => $uaId,
            "medal_detail_id" => $active->id,
            "source_id" => $sourceId,
            "source_type" => $sourceType,
            "reward_score" => $active->reward_score * $medalNum,
            "from" => $from,
        ];
        if (!$isCountType) {
            $reward_score = 0;
            foreach ($detail as $item) {
                if ($item->step > $nowActiveStep && $item->step <= $active->step) {
                    $reward_score += $item->reward_score;
                }
            }
            $insertMedalLog['reward_score'] = $reward_score;
        }

        $userMedal->medal_detail_id = $active->id;
        $userMedal->progress += $medalNum;
        DB::beginTransaction();
        $r = $userMedal->save();
        $r2 = UserMedalLogModel::create($insertMedalLog);
        $point = new PointService();
        $point->medalPoint($uaId, $medal->name, $insertMedalLog['reward_score'], $active->level);
        //todo 下发积分
        if ($r && $r2) {
            DB::commit();
        } else {
            DB::rollBack();
        }
        return true;
    }


    /**
     * 掉落
     * @param $id
     */
    public function medalDrop($id) {
        if (is_string($id)) {
            $id = explode(",", $id);
        }

        $drop = MedalDropModel::whereIn("id", $id)->where("user_id", Auth::user()->ua_id)->where("status", 0)->get();
        if ($drop) {
            foreach ($drop as $item) {
                $item->status = 1;
                $item->save();
            }
        }
        return true;
    }
    public $cacheMedal = [];
    /**
     * 添加勋章进度
     * @param string $type 类型
     * @param array $data 需要添加的用户列表
     */
    public function addMedalProgress($type, $data = [], $isOld = false) {
        //id progress

        //添加勋章进度
        //添加后需要进行勋章 等级触发 积分下发
        if (Arr::has($this->cacheMedal, $type)) {
            $medal = Arr::get($this->cacheMedal, $type);
        } else {
            $medal = MedalModel::where("name", $type)->with(["medalDetail" => function ($q) {
                $q->orderBy("step");
            }])->first();
            $this->cacheMedal[$type] = $medal;
        }
        if (!$medal) {
            //未启用
            return false;
        }
        echo "Medal progress called:" . $type . PHP_EOL;
        $repeat = $medal->repeat;
        $data = collect($data)->mapWithKeys(function ($item) {
            return [Arr::get($item, "id", "") => $item];
        });
        $users = $data->keys();
        if (!$repeat) {
            //如果不能重复获得
            $users = $this->diffUserMedal($users, $medal->id);
        }
//        DB::beginTransaction();
        //需要分成2种情况
        foreach ($users as $user) {
            echo "Check User :" . $user . PHP_EOL;
            $userMedal = UserMedalModel::firstOrNew([
                "user_id" => $user,
                "medal_id" => $medal->id
            ]);
            if ($userMedal->id == null) {
                //初始化
                $userMedal->medal_detail_id = 0;
                $userMedal->progress =0;
            }
            $progress = (int) Arr::get($data, $user . ".progress", 0);
            if ($medal->type == 1 && Arr::has($data, $user.".step")) {
                //如果是等级 并且用了step 说明是直接赋予等级
                $step = (int) Arr::get($data, $user . ".step", 1);
                $t = collect($medal->medalDetail)->first(function ($item) use ($step) {
                    return $item->step == $step;
                });
                if ($t) {
                    //重置进度
                    $progress = $t->progress - $userMedal->progress;
                    $progress = $progress < 0 ? 0 : $progress;
                }
            }
            $lastProgress = $userMedal->progress;
            $userMedal->progress += $progress;
            //保存进度
            $userMedal->save();
            //校验进度情况，是否颁发实质勋章和下达积分
            $this->needRewardMedal($medal, $userMedal, $lastProgress, $isOld);
            echo "Check User :" . $user . " OVER " . PHP_EOL;
        }
//        DB::commit();
        return true;

    }

    /**
     * 积分名人堂|学霸之星 触发
     * @param array $users 二维数组 符合条件的用户ID 以及对应排名 如 index 0 => ['id '=> 'xkt123', 'rank' => 1]
     */
    public function topMedal($users = [], $medalType = MedalConst::POINT_HALL_OF_FAME) {
        //获取积分名人堂勋章的参数
        $medal = MedalModel::where("name", $medalType)->first();
        if (!$medal) {
            //该勋未启用
            return false;
        }
        //todo 排名可能有用
        $users = collect($users)->mapWithKeys(function ($item) {
            return [Arr::get($item, "id", "") => $item];
        });
        $userCollect = $users->keys();
        //获取到已经拿到过该勋章的用户
        if (!$medal->repeat) {
            $userCollect = $this->diffUserMedal($userCollect, $medal->id);
        }
        $data = $userCollect->map(function ($item) {
            return [
                "id" => $item,
                "progress" => 1,
            ];
        });
        return $this->addMedalProgress($medalType, $data);
    }

    public function diffUserMedal($users, $medalId): Collection {
        $users = collect($users);
        if ($users->count() <= 0) {
            return $users;
        }
        $userMedalList = UserMedalModel::whereIn("user_id", $users)->where("medal_id", $medalId)->get()->pluck("user_id");
        //users 必然 >= userMedalList
        return $users->diff($userMedalList);
    }

    public function needRewardMedal(MedalModel $medal, UserMedalModel $userMedal, $lastProgress = 0, $isOld = false) {
        $nowActive = $userMedal->medal_detail_id;
        $medalDetail = $medal->medalDetail ? : $medal->medalDetail()->orderBy("step")->get();
        if (!$medalDetail || $medalDetail->count() <= 0) {
            return false;
        }
        $over = false;
        $userMedalLog = [];
        $total = 0;
        //递归拿下一级，因为可能存在触发多级
        while (!$over) {
            $nexActive = null;
            if ($medal->type == 2 || $nowActive == 0) {
                //计数类的
                $over = $medal->type == 2 ? true : false;
                $nexActive = $medalDetail->first();
            } else {
                $_nowActive = collect($medalDetail)->first(function ($item) use ($nowActive) {
                    return $item->id == $nowActive;
                });
                if ($_nowActive) {
                    $nexActive = collect($medalDetail)->first(function ($item) use ($_nowActive) {
                        return $item->step >  $_nowActive->step;
                    });
                }
            }
            //没有下一级 或者没达到下一级的条件
            if (!$nexActive || $nexActive->progress > $userMedal->progress) {
                break;
            }
            $nowActive = $nexActive->id;
            $userMedal->medal_detail_id = $nexActive->id;
            //添加log
            $userMedalLog[] = [
                "user_id" => $userMedal->user_id,
                "medal_detail_id" => $nexActive->id,
                "from" => "sc",
                "reward_score" => $nexActive->reward_score
            ];
            $total += $nexActive->reward_score * ($medal->type == 2 ? ($userMedal->progress - $lastProgress) : 1);
        }
        $userMedal->save();
        if ($userMedalLog) {
            if ($medal->from != "sc") {
                DB::table("user_medal_log")->insert($userMedalLog);
            } else {
                $_drop = [];
                foreach ($userMedalLog as $item) {
                    $id = DB::table("user_medal_log")->insertGetId($item);
                    $_drop[] = [
                        "user_id" => Arr::get($item, "user_id"),
                        "medal_log_id" => $id,
                        "status" => 0,
                    ];
                }
                DB::table("medal_drop")->insert($_drop);
            }
        }
        $isTop = $medal->name == MedalConst::POINT_HALL_OF_FAME;
        if ($total > 0) {
            $nowLevel = collect($medalDetail)->first(function ($item) use ($nowActive) {
                return $item->id == $nowActive;
            })->level;
            //todo isOld
            if ($isOld) {
                (new PointService())->historyMedalPoint($userMedal->user_id, $medal->name, $total, "", !$isTop);
            } else {
                (new PointService())->medalPoint($userMedal->user_id, $medal->name, $total, $nowLevel, !$isTop);
            }
        }
        return true;
    }

}
