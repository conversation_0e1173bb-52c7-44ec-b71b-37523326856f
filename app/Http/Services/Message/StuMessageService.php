<?php

namespace App\Http\Services\Message;

use App\Http\Services\Talk\TalkService;
use App\Models\Message\StuMessageModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use stdClass;

class StuMessageService
{
    /** 给学生发送消息
     * @param $event 事件类型，StuMessageModel::FROMS_*
     * @param $content 消息内容
     * @param $actionUser 发送人ua_id,xkt***
     * @param $reciveUser 消息接受人ua_id,xkt***
     * @param $partyType 外部系统类型,StuMessageModel::PARTY_TYPE_*
     * @param $partyId  外部系统的id
     * @param $memo 积分消息时可传积分值，例如：+1 -5
     * @return int|mixed 消息表的id
     */
    public static function makeUserMsg($event, $content, $actionUser, $reciveUser, $partyType = 0, $partyId = 0, $memo = "")
    {
        $msg = new StuMessageModel();
        if ($event == StuMessageModel::FROMS_1 || $event == StuMessageModel::FROMS_2) {
            $msg->setAttribute('msgtype', StuMessageModel::MSGTYPE_1);
        } elseif ($event == StuMessageModel::FROMS_11) {
            $msg->setAttribute('msgtype', StuMessageModel::MSGTYPE_2);
        } elseif ($event == StuMessageModel::FROMS_12) {
            $msg->setAttribute('msgtype', StuMessageModel::MSGTYPE_3);
        } else {
            $msg->setAttribute('msgtype', StuMessageModel::MSGTYPE_4);
        }
        $msg->setAttribute('froms', $event);
        $msg->setAttribute('action_user', $actionUser);
        $msg->setAttribute('recive_user', $reciveUser);
        $msg->setAttribute('msg', $content);
        if (!empty($memo)) {
            $msg->setAttribute('memo', $memo);
        }
        if ($partyType > 0) {
            $msg->setAttribute('party_type', $partyType);
        }
        if ($partyId > 0) {
            $msg->setAttribute('party_id', $partyId);
        }
        $msg->save();
        return $msg;
    }

    /** 获取未读消息数量
     * @return mixed
     */
    public function userMessageCount()
    {
        $uaId = Auth::user()->ua_id;
        $status = StuMessageModel::STATUS_1;
        $res = DB::select("SELECT msgtype+0 msgType,COUNT(*) count FROM `sc_stu_message` where recive_user='$uaId' AND deleted_at is null and status=$status GROUP BY msgtype");
        function setNullData(&$res, $msgType)
        {
            $m = array_reduce($res, function ($carry, $item) use ($msgType) {
                if ($item->msgType == $msgType) {
                    return $item;
                }
                return $carry;
            });
            if ($m == null) {
                $obj = new stdClass();
                $obj->msgType = $msgType;
                $obj->count = 0;
                $res[] = $obj;
            }
        }

        setNullData($res, 2);
        setNullData($res, 3);
        setNullData($res, 4);
        return $res;
    }

    /** 读消息
     * @return void
     */
    public function userReadMessage($msgType, $msgId, $part)
    {
        $uaId = Auth::user()->ua_id;
        $uId = Auth::user()->u_id;
        $status1 = StuMessageModel::STATUS_1;
        $status2 = StuMessageModel::STATUS_2;
        $w = "";
        if (filter_var($msgId, FILTER_VALIDATE_INT) !== false) {
            $w = " and id=$msgId ";
        }
        if ($part == "1") {
            $w = " and party_id=0 ";
        }
        return DB::update("update `sc_stu_message` set `status` = $status2, `updated_opper` = $uId where `recive_user` = '$uaId' and `deleted_at` is null and `status` = $status1 and `msgtype` = $msgType $w");
    }

    /** 消息列表
     * @param $page
     * @param $pageLimit
     * @param $msgType
     * @return array
     */
    public function userMessageList($page, $pageLimit, $msgType)
    {
        $uaId = Auth::user()->ua_id;
        $start = ($page - 1) * $pageLimit;
        $s = "";
        $res = DB::select("SELECT id,msg,created_at,memo,party_type+0 party_type,party_id,'' url,action_user,status+0 status FROM `sc_stu_message` where recive_user='$uaId' AND deleted_at is null and msgtype=$msgType $s ORDER BY id desc limit $start,$pageLimit");
        $resCount = DB::selectOne("SELECT count(*) total FROM `sc_stu_message` where recive_user='$uaId' AND deleted_at is null and msgtype=$msgType $s ");
        if ($res != null) {
            foreach ($res as $item) {
                switch ($item->party_type) {
                    case StuMessageModel::PARTY_TYPE_1:
                        $comment = DB::selectOne("SELECT a.talk_id FROM `sc_talk_comments` a where a.id=$item->party_id");
                        $item->url = sprintf(env("PARTY_TYPE_TALK_URL"), $comment->talk_id);
                        break;
                    case StuMessageModel::PARTY_TYPE_2:
                        $comment = DB::selectOne("SELECT a.id,a.talk_id,a.created_opper,a.comment_id FROM `sc_talk_comments` a where a.id=$item->party_id");
                        $item->comment = $this->getCommentDetail($comment->id, '', $comment->comment_id);
                        $item->url = sprintf(env("PARTY_TYPE_TALK_URL"), $comment->talk_id);
                        break;
                    case StuMessageModel::PARTY_TYPE_3:
                    case StuMessageModel::PARTY_TYPE_4:
                        $item->comment = $this->getCommentDetail(0, $item->action_user, $item->party_id);
                        break;
                    default:
                }
            }
        }
        $arr = array();
        $arr['data'] = $res;
        $arr['total'] = $resCount->total;
        return $arr;
    }

    /** 消息这边点赞和评论显示格式和话题评论格式一样，故另写了这个方法
     * @param $id
     * @param $user
     * @param $toCommentId
     * @return mixed|stdClass
     */
    private function getCommentDetail($id, $user, $toCommentId = 0)
    {
        $talkService = new TalkService();
        $uaId = Auth::user()->ua_id;
        $res = DB::selectOne("SELECT a.id,a.created_opper ua_id,a.talk_id,a.cont,a.created_at time,(CASE WHEN a.status+0=1 THEN 1 ELSE 0 END) isExamining,(CASE WHEN a.deleted_at is null THEN 0 ELSE 1 END) isDelete,(SELECT count(1) FROM `sc_talk_comment_actions` c where c.action_user='$uaId' and c.comment_id=a.id and c.like_category=1 and  c.like_status=1 and c.deleted_at is null) isLike,(SELECT count(*) FROM `sc_talk_comment_actions` c where c.comment_id=a.id and c.like_category=1 and c.like_status=1 and c.deleted_at is null) likeNum,`f_getCommentNum`(a.talk_id,a.id,2,'$uaId') commentNum FROM sc_talk_comments a where a.id=$id");
        if ($id > 0) {
            if ($res->isDelete == 1) {
                $res->cont = null;
                $res->imgs = array();
            } else {
                $res->imgs = $talkService->getCommentImgs($id);
            }
            $tua_id = $res->ua_id;
        } else {
            //点赞时
            $res = new stdClass();
            $res->imgs = array();
            $tua_id = $user;
        }
        $res->userInfo = StuMessageService::getUserInfo($tua_id);
        if ($toCommentId > 0) {
            $res->replyContent = $this->getCommentDetail($toCommentId, '');
        }
        return $res;
    }

    /** 获取用户昵称和头像
     * @param $uaID
     * @return array
     */
    public static function getUserInfo($uaID)
    {
        $uaId =optional(Auth::user())->ua_id;
        if ($uaId != $uaID) {
            $isMe = 0;
        } else {
            $isMe = 1;
        }
        $key = "sc::user:info_" . $uaID . '_' . $isMe;
        $obj = Redis::get($key);
        if ($obj != null) {
            return json_decode($obj);
        }
        $defaultSkin = env("DEFAULT_SKIN", 0);
        if ($isMe == 0) {
            //非本人不能看到审核中的信息
            $res = DB::selectOne("SELECT 0 isMe,a.nickname,'' nickname_checking,0 nickname_ischecking, a.avater,(SELECT sf.file_path from sc_file sf where sf.id=a.avater) avater_url,0 avater_checking,'' avater_checking_url,0 avater_ischecking,avater_frame,(SELECT JSON_UNQUOTE(json_extract(ext,'$.path')) FROM `sc_exchange_store` where id=a.avater_frame) avater_frame_url,level,(SELECT sf.file_path FROM `sc_grade` sg,sc_file sf where sg.id=a.level and sg.resource_id=sf.id) level_url
,
(SELECT ((SELECT count(*) as total FROM sc_user_medal aa JOIN sc_medal bb on aa.medal_id = bb.id
JOIN sc_medal_detail cc on cc.medal_id = bb.id
JOIN sc_user_medal_log dd on dd.medal_detail_id = cc.id
WHERE aa.user_id = a.uaid and dd.user_id = a.uaid AND bb.type = 1) +
(SELECT SUM(aa.progress) as total FROM sc_user_medal aa JOIN sc_medal bb on aa.medal_id = bb.id
WHERE aa.user_id = a.uaid and bb.type = 2))) as medal_num
,a.skin,(IF (a.skin=0,(
SELECT JSON_UNQUOTE(json_extract(ext,'$.bannerImg.path')) FROM `sc_exchange_store` WHERE id={$defaultSkin}),(
SELECT JSON_UNQUOTE(json_extract(ext,'$.bannerImg.path')) FROM `sc_exchange_store` WHERE id=a.skin))) avatar_hover_image
FROM `sc_student_info` a where a.uaid='$uaID'");
        } else {
            $res = DB::selectOne("SELECT 1 isMe,a.nickname,a.nickname_checking,(SELECT (CASE WHEN COUNT(1)>0 THEN 1 ELSE 0 END ) FROM `sc_student_resource` b where b.type=4 and b.`status`=2 and b.uaid=a.uaid) nickname_ischecking, a.avater,(SELECT sf.file_path from sc_file sf where sf.id=a.avater) avater_url,a.avater_checking,(SELECT sf.file_path from sc_file sf where sf.id=a.avater_checking) avater_checking_url,(SELECT (CASE WHEN COUNT(1)>0 THEN 1 ELSE 0 END ) FROM `sc_student_resource` b where b.type=1 and b.`status`=2 and b.uaid=a.uaid) avater_ischecking,avater_frame,(SELECT JSON_UNQUOTE(json_extract(ext,'$.path')) FROM `sc_exchange_store` where id=a.avater_frame) avater_frame_url,level,(SELECT sf.file_path FROM `sc_grade` sg,sc_file sf where sg.id=a.level and sg.resource_id=sf.id) level_url
,
(SELECT ((SELECT count(*) as total FROM sc_user_medal aa JOIN sc_medal bb on aa.medal_id = bb.id
JOIN sc_medal_detail cc on cc.medal_id = bb.id
JOIN sc_user_medal_log dd on dd.medal_detail_id = cc.id
WHERE aa.user_id = a.uaid and dd.user_id = a.uaid AND bb.type = 1) +
(SELECT SUM(aa.progress) as total FROM sc_user_medal aa JOIN sc_medal bb on aa.medal_id = bb.id
WHERE aa.user_id = a.uaid AND bb.type = 2))) as medal_num
,a.skin,(IF (a.skin=0,(
SELECT JSON_UNQUOTE(json_extract(ext,'$.bannerImg.path')) FROM `sc_exchange_store` WHERE id={$defaultSkin}),(
SELECT JSON_UNQUOTE(json_extract(ext,'$.bannerImg.path')) FROM `sc_exchange_store` WHERE id=a.skin))) avatar_hover_image
FROM `sc_student_info` a where a.uaid='$uaID'");
        }
        //fixed res is null
        if (!$res) {
            return $res;
        }
        $res->medal_num = $res->medal_num ?:0;
        Redis::setex($key, 5, json_encode($res));
        return $res;
    }

    public function toastr()
    {
        $uaId = Auth::user()->ua_id;
        $sql = "SELECT a.*,e.title,e.icon,e.attr,e.closeable,e.auto_close_time,c.start_time, c.end_time,d.content  FROM sc_xkt_user_message a
    JOIN sc_xkt_user_message_config_relation b on a.id = b.user_message_id
    JOIN sc_xkt_message_time_config c on c.id = b.time_id
    JOIN sc_xkt_message_content_config d on d.entity_id = b.entity_id
    join sc_xkt_message_config e on e.id = a.message_config_id
    WHERE c.start_time <= ? and c.end_time >= ? and a.ua_id in (?, '0') and NOT EXISTS (SELECT 1 FROM sc_xkt_message_record aa WHERE aa.ua_id = ? and aa.message_id = a.id) and e.status = 1 and a.deleted_at is null";
        $time = date("Y-m-d H:i:s", time());
        $list = DB::select($sql, [$time, $time, $uaId, $uaId]);
        $data = [];
        foreach ($list as $item) {
            $attr = $item->attr;
            $buttons = [];
            if ($attr) {
                $attr = json_decode($attr, true);
                $buttons = Arr::get($attr, "button", []);
            }
            $tmp = [
                "autoCloseTime" => $item->auto_close_time,
                "buttons" => $buttons,
                "closeable" => (bool) $item->closeable,
                "content" => $item->content,
                "type" => "immediate",
                "contentType" => "text",
                "icon" => $item->icon,
                "id" => $item->id,
                "messageConfigId" => $item->message_config_id,
                "title" => $item->title,
            ];
            $data[] = $tmp;
        }
        return $data;
    }

    public function toastrRead($id, $messageConfigId)
    {
        if ($id == null) {
            $id = 0;
        }
        if ($messageConfigId == null || $messageConfigId == 0) {
            throw new \Exception("参数错误");
        }
        $uaId = Auth::user()->ua_id;
        $sql = "insert into sc_xkt_message_record (`ua_id`, `message_id`,`message_config_id`) VALUES (?,?,?)";
        DB::insert($sql, [$uaId, $id, $messageConfigId]);
        return true;
    }
}
