<?php

namespace App\Http\Services\Talk;

use App\ConstDir\Medal\MedalConst;
use App\Events\Medal\MedalEvent;
use App\Http\Services\File\FileService;
use App\Http\Services\Message\StuMessageService;
use App\Http\Services\Other\OtherService;
use App\Http\Services\Point\PointService;
use App\Http\Services\Student\StudentService;
use App\Models\File\FileModel;
use App\Models\Message\StuMessageModel;
use App\Models\ScBanner\ScBannerModel;
use App\Models\Student\StudentInfoModel;
use App\Models\Talk\TalkCommentActionsModel;
use App\Models\Talk\TalkCommentModel;
use App\Models\Talk\TalkModel;
use App\Models\Verify\UserData;
use App\Utils\Common;
use App\Utils\QueueUtil;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Storage;
use stdClass;

class TalkService
{
    /** 异步执行审核后的通知
     * @param $notifyData 调用审核时传入的$userData->notifyData
     * @param $retData 审核后返回的结果,例如[ { "pass": false, "content": "我*手", "errorMessage": "", "source": [] }, { "pass": true, "content": "", "errorMessage": "", "source": [] } ]
     * @return void
     */
    public static function checkTalkComment($notifyData, $retData)
    {
        $commentId = $notifyData['id'];
        $talkId = $notifyData['talkId'];
        $imgs = $notifyData['imgs'];
        $ok = true;
        $fileStatus = 1;
        foreach ($retData as $item) {
            if (!$item['pass']) {
                $ok = false;
            }
            if ($item['targetType'] == UserData::TargetType_IMG && !$item['pass']) {
                $fileStatus = 0;
            }
        }
        $talk = TalkModel::find($talkId, 'title');
        if ($talk == null) {
            return false;
        }
        $comment = TalkCommentModel::find($commentId);
        if ($comment == null) {
            return false;
        }
        //已处理的不再处理了
        if ($comment->status != TalkCommentModel::STATUS_1_STR) {
            return false;
        }
        $uaId = $comment->created_opper;
        DB::beginTransaction();
        if ($ok) {
            //1、更新审核状态
            $status = TalkCommentModel::STATUS_2;
            $comment->setAttribute("status", $status);
            $comment->save();
            //2、发送给审核消息
            $msg = "你在话题#$talk->title#下的评论审核已通过";
            $msgObj = StuMessageService::makeUserMsg(StuMessageModel::FROMS_5, $msg, '', $uaId, StuMessageModel::PARTY_TYPE_1, $commentId);
            //3、发送评论消息
            //回复自己的评论时消息中心不会收到评论消息
            if (!empty($comment->comment_id) && $comment->comment_id > 0) {
                $p = TalkCommentModel::find($comment->comment_id, "created_opper");
                if ($p != null && $comment->created_opper != $p->created_opper) {
                    $msg2 = "";
                    $msgObj2 = StuMessageService::makeUserMsg(StuMessageModel::FROMS_12, $msg2, $uaId, $p->created_opper, StuMessageModel::PARTY_TYPE_2, $commentId);
                }
            }
            //4、发送积分
            (new PointService())->commentPoint($comment->created_opper);
            //5、判断是否是社交达人
            Event::dispatch(new MedalEvent(MedalConst::SOCIAL_PEOPLE, [["id" => $uaId, "progress" => 1]]));
        } else {
            $status = TalkCommentModel::STATUS_3;
            $comment->setAttribute("status", $status);
            $comment->save();
            $msg = "你在话题#$talk->title#下的评论审核未通过，该评论已删除";
            $msgObj = StuMessageService::makeUserMsg(StuMessageModel::FROMS_6, $msg, '', $uaId, StuMessageModel::PARTY_TYPE_1, $commentId);
            $comment->delete();
        }
        //更新评论的图片审核状态
        foreach ($imgs as $img_id) {
            DB::update("UPDATE `sc_comment_imgs` SET `status`= $status WHERE `comment_id`= $commentId and img_id=$img_id");
            DB::update("UPDATE `sc_file` SET `status`=1,`check_status`=$fileStatus WHERE id=$img_id");
        }
        DB::commit();
        //审核失败记录下日志，放到事务外防止影响主体业务
        if (!$ok) {
            $json = json_encode(array("action" => 'checkTalkComment', "commentId" => $commentId, "data" => $retData));
            DB::insert("INSERT INTO `sc_logic_log` (`user_id`, `details`) VALUES ('$uaId','$json')");
        }
        return $comment;
    }

    /** 置顶话题
     * @return array
     */
    public function getTopTalk()
    {
        $talk = DB::selectOne("SELECT id,title,cont FROM `sc_talks` where `status`=2 and deleted_at is null and istop=1 order by updated_at desc limit 1");
        if ($talk != null) {
            return $this->getTalkDetailOther($talk);
        }
        return array();
    }

    /** 获取话题相关详情
     * @param $talk
     * @param $onlyDetail
     * @return array
     */
    private function getTalkDetailOther($talk, $onlyDetail = false)
    {
        $talkTags = DB::select("SELECT b.tag text,b.color_ft color,b.color_bg backgroundColor from sc_talk_tags a,sc_tags b where a.tag_id=b.id and a.talk_id=$talk->id and a.deleted_at is null and b.deleted_at is null");
        $talkImgs = DB::select("SELECT st.img_id,sf.file_path img_path,sf.oss_path from sc_talk_imgs st,`sc_file` sf where st.img_id=sf.id and st.talk_id=$talk->id and st.`status`=2 and st.deleted_at is null");
        if ($onlyDetail) {
            return array("talk" => $talk, "tags" => $talkTags, "imgs" => $talkImgs);
        }
        $lastUser = DB::select("SELECT a.created_opper ua_id FROM `sc_talk_comments` a where a.talk_id=$talk->id and a.`status`=2 and a.deleted_at is null GROUP BY created_opper ORDER BY MAX(id) DESC limit 3");
        foreach ($lastUser as $user) {
            $user->userInfo = StuMessageService::getUserInfo($user->ua_id);
        }
        $gather = DB::selectOne("SELECT COUNT(DISTINCT a.created_opper) usercount,MAX(a.created_at) lasttime FROM `sc_talk_comments` a where a.talk_id=$talk->id and a.`status`=2 and a.deleted_at is null");
        return array("talk" => $talk, "tags" => $talkTags, "imgs" => $talkImgs, "users" => $lastUser, "gather" => $gather);
    }

    /** 全部话题
     * @param $page
     * @param $pageLimit
     * @return array
     */
    public function getAllTalks($page, $pageLimit)
    {
        $start = ($page - 1) * $pageLimit;
        $talks = DB::select("SELECT st.id,st.title,st.cont,st.ishigh,st.ishot FROM `sc_talks` st where st.`status`=2 and st.deleted_at is null and st.id!=IFNULL((SELECT a.id FROM `sc_talks` a where a.`status`=2 and a.deleted_at is null and a.istop=1 order by a.updated_at desc limit 1),0) order by st.ishot desc,st.orders desc,st.created_at desc limit $start,$pageLimit ");
        $talkList = array();
        foreach ($talks as $talk) {
            $talkList[] = $this->getTalkDetailOther($talk);
        }
        $total = DB::selectOne("SELECT count(*) total FROM `sc_talks` st where st.`status`=2 and st.deleted_at is null and st.id!=IFNULL((SELECT a.id FROM `sc_talks` a where a.`status`=2 and a.deleted_at is null and a.istop=1 order by a.updated_at desc limit 1),0)");
        return array("data" => $talkList, "total" => $total->total);
    }

    /**
     * 话题V2
     * @param $page
     * @param $pageLimit
     * @return array
     */
    public function getAllTalksV2($page, $pageLimit)
    {
        $start = ($page - 1) * $pageLimit;
        $talks = DB::select("SELECT st.id,st.title,st.cont,st.ishigh,st.ishot FROM `sc_talks` st where st.`status`=2 and st.deleted_at is null and st.id!=IFNULL((SELECT a.id FROM `sc_talks` a where a.`status`=2 and a.deleted_at is null and a.istop=1 order by a.updated_at desc limit 1),0) order by st.ishot desc,st.orders desc,st.created_at desc limit $start,$pageLimit ");
        $talkList = $this->getTalkDetailV2($talks);
        $total = DB::selectOne("SELECT count(*) total FROM `sc_talks` st where st.`status`=2 and st.deleted_at is null and st.id!=IFNULL((SELECT a.id FROM `sc_talks` a where a.`status`=2 and a.deleted_at is null and a.istop=1 order by a.updated_at desc limit 1),0)");
        return array("data" => $talkList, "total" => $total->total);
    }

    private function getTalkDetailV2($talks = []): array
    {
        if (!$talks) {
            return [];
        }
        $talkList = [];
        $talks = collect($talks);
        $talksId = $talks->pluck("id")->toArray();
        $placeholders = implode(',', array_fill(0, count($talksId), '?'));
        $talkTags = DB::select("SELECT a.talk_id, b.tag text,b.color_ft color,b.color_bg backgroundColor from sc_talk_tags a,sc_tags b where a.tag_id=b.id and a.talk_id in ($placeholders) and a.deleted_at is null and b.deleted_at is null", $talksId);
        $talkImgs = DB::select("SELECT st.talk_id, st.img_id,sf.file_path img_path,sf.oss_path from sc_talk_imgs st,`sc_file` sf where st.img_id=sf.id and st.talk_id in ($placeholders) and st.`status`=2 and st.deleted_at is null", $talksId);
        //每个话题拿最小
        $lastUser = DB::select("select DISTINCT a.created_opper as ua_id,a.talk_id from sc_talk_comments a where a.talk_id in ($placeholders) and a.`status`= 2 and a.deleted_at is null and (select count(DISTINCT created_opper) from sc_talk_comments b where b.talk_id = a.talk_id and b.`status`= 2 and b.deleted_at is null and a.id < b.id) < 3 order by a.id desc", $talksId);

        $gather = DB::select("SELECT a.talk_id,  COUNT(DISTINCT a.created_opper) usercount,MAX(a.created_at) lasttime FROM `sc_talk_comments` a where a.talk_id in ($placeholders) and a.`status`=2 and a.deleted_at is null group by a.talk_id", $talksId);

        $talkTags = collect($talkTags)->groupBy(['talk_id']);
        $talkImgs = collect($talkImgs)->groupBy(['talk_id']);

        $uaIds = collect($lastUser)->pluck('ua_id')->toArray();
        $userInfo = (new StudentService())->studentInfoFromTalk($uaIds, optional(Auth::user())->ua_id)->groupBy("uaid");
        foreach ($lastUser as $item) {
                $item->userInfo = $userInfo->has($item->ua_id) ? $userInfo->get($item->ua_id)->first() : null;
        }

        $lastUser = collect($lastUser)->groupBy(['talk_id']);
        $gather = collect($gather)->groupBy(['talk_id']);

        foreach ($talks as $talk) {
            $talkList[] = [
                'talk' => $talk,
                'tags' => $talkTags[$talk->id] ?? [],
                'imgs' => $talkImgs[$talk->id] ?? [],
                'users' => $lastUser[$talk->id] ?? [],
                'gather' => isset($gather[$talk->id]) ? $gather[$talk->id]->first() : []
            ];
        }

        return $talkList;
    }


    /** 我参与的话题
     * @param $page
     * @param $pageLimit
     * @return array
     */
    public function getMyJoinTalks($page, $pageLimit)
    {
        $uaId = Auth::user()->ua_id;
        $start = ($page - 1) * $pageLimit;
        $talks = DB::select("SELECT a.id,a.title,a.cont,a.ishigh,a.ishot FROM `sc_talks` a,`sc_talk_comments` b where b.created_opper='$uaId' and a.id=b.talk_id and a.`status`=2 and a.deleted_at is null and b.`status`=2 and b.deleted_at is null GROUP BY a.id,a.title,a.cont,a.ishigh,a.ishot ORDER BY max(b.created_at) desc limit $start,$pageLimit ");
        $talkList = array();
        foreach ($talks as $talk) {
            $talkList[] = $this->getTalkDetailOther($talk);
        }
        $total = DB::selectOne("SELECT count(DISTINCT a.id) total FROM `sc_talks` a,`sc_talk_comments` b where b.created_opper='$uaId' and a.id=b.talk_id and a.`status`=2 and a.deleted_at is null and b.`status`=2 and b.deleted_at is null");
        return array("data" => $talkList, "total" => $total->total);
    }

    /** 话题详情页面
     * @param $talkId
     * @return array
     */
    public function getTalkDetail($talkId)
    {
        $this->checkTalkStatus($talkId);
        $uaId = Auth::user()->ua_id;
        $talk = DB::selectOne("SELECT DISTINCT a.id,a.title,a.cont,a.ishigh,a.ishot,(CASE WHEN (SELECT count(*) FROM `sc_talk_actions` where action_user='$uaId' and deleted_at is null)>0 THEN 1 ELSE 0 END) closeTip FROM `sc_talks` a where a.id=$talkId and a.`status`=2 and a.deleted_at is null");
        if ($talk == null) {
            return array();
        }
        return $this->getTalkDetailOther($talk, true);
    }

    /** 检测话题状态
     * @param $talkId
     * @return void
     * @throws Exception
     */
    private function checkTalkStatus($talkId)
    {
        if ($talkId <= 0) {
            throw new Exception('操作失败');
        }
        $talk = TalkModel::find($talkId);
        if ($talk == null) {
            throw new Exception('该话题已删除');
        }
        if ($talk->status != TalkModel::STATUS_2_STR) {
            throw new Exception('该话题未上线');
        }
    }

    /**
     * 校验用户每日剩余评论数
     * @param $uaId
     * @return int
     * @throws Exception
     */
    protected function checkCommentPerDay($uaId): int
    {
        $limitComment = -1;
        $configLimit = DB::table("config")->where("type", "=", "学员每日评论次数")->first();
        if ($configLimit) {
            $limitComment = (int) $configLimit->configs;
        }
        $studentInfo = StudentInfoModel::find($uaId);
        if ($studentInfo) {
            if ($studentInfo->comment_per_day != null && $studentInfo->comment_per_day >= -1) {
                $limitComment = $studentInfo->comment_per_day;
            }
        }
        if ($limitComment == -1) {
            return $limitComment;
        }
        $nowTime = time();
        $sTime = date("Y-m-d 00:00:00", $nowTime);
        $eTime = date("Y-m-d 00:00:00", $nowTime + 86400);
        $dayCommentTimes = TalkCommentModel::withTrashed()->where("created_at", ">=", $sTime)->where("created_at", "<", $eTime)->where("created_opper", $uaId)->count();

        if ($limitComment <= $dayCommentTimes) {
            throw new Exception("已达今日评论次数上限");
        }
        return $limitComment - $dayCommentTimes;
    }

    /** 获取话题评论
     * @param $talkId 话题id
     * @param $page
     * @param $pageLimit
     * @param $hot 是否是最热评论
     * @param $new 是否是最新评论
     * @param $me 是否是我评论的
     * @return array
     */
    public function getTalkComments($talkId, $page, $pageLimit, $hot = 0, $new = 0, $me = 0)
    {
        $this->checkTalkStatus($talkId);
        $cPage = 1;
        //第二层的评论默认5条
        $cPageLimit = 5;
        $uaId = Auth::user()->ua_id;
        $start = ($page - 1) * $pageLimit;
        //全部评论,按照评论时间正序
        //用户自己看到自己审核中的评论，出现小标签
        //没有被回复的评论或者回复在被删除了之后页面是不显示的，如果有被回复，这条消息会议“该评论已删除”的形式显示
        $otherSelect = "";
        $w = "";
        $order = ' ORDER BY a.id asc ';
        $all = true;
        //最热评论,按照【点赞】+评论数量从多到少
        //点赞数+回复该条评论的评论数（次楼内他人间的回复不计入主楼的热度）
        if ($hot == 1) {
            //全部讨论和我评论的每页显示30条,最热评论和最新评论只显示10条
            $start = 0;
            $pageLimit = 10;
            $all = false;
            $order = " ORDER BY ((SELECT count(*) FROM `sc_talk_comment_actions` c where c.comment_id=a.id and c.like_category=1 and c.like_status=1 and c.deleted_at is null)+(SELECT count(*) FROM `sc_talk_comments` b where b.comment_id=a.id and b.`status`=2 and b.deleted_at is null)) desc,(SELECT count(*) FROM `sc_talk_comment_actions` c where c.comment_id=a.id and c.like_category=1 and c.like_status=1 and c.deleted_at is null) desc,a.id desc ";
            //热度为 0 的不展示
            $w = $w . " ((SELECT count(*) FROM `sc_talk_comment_actions` c where c.comment_id=a.id and c.like_category=1 and c.like_status=1 and c.deleted_at is null)+(SELECT count(*) FROM `sc_talk_comments` b where b.comment_id=a.id and b.`status`=2 and b.deleted_at is null))>0 and ";
        } //最新评论,按照评论的发布时间倒序
        else if ($new == 1) {
            //全部讨论和我评论的每页显示30条,最热评论和最新评论只显示10条
//            $start = 0;
//            $pageLimit = 20;
            $all = false;
            $order = ' ORDER BY a.id desc ';
        } //我评论的,用户自己发布评论的（审核中的也会展示），排序评论时间倒序；
        else if ($me == 1) {
            $all = false;
            $w = $w . " a.created_opper='$uaId' and ";
            $order = ' ORDER BY a.id desc ';
        }
        if ($all) {
            $otherSelect = "null toua_id,null to_id,";
            $w = $w . " a.talk_id=$talkId and a.comment_id=0 and (a.`status` = 2 OR (a.`status` = 1 and a.created_opper = '$uaId')) and (a.deleted_at IS NULL OR (a.deleted_at IS NOT NULL AND (SELECT count(*) from sc_talk_comments stc where stc.first_comment_id=a.id and (stc.`status`=2 or (stc.`status`=1 and stc.created_opper='$uaId')) and stc.deleted_at is null)>0)) ";
        } else {
            //非全部评论二级评论就显示replyContent
            $otherSelect = "(SELECT d.created_opper FROM `sc_talk_comments` d where d.id=a.comment_id) toua_id,(CASE WHEN a.comment_id>0 THEN a.comment_id ELSE null END) to_id,";
            $w = $w . " a.talk_id=$talkId and (a.`status` = 2 OR (a.`status` = 1 and a.created_opper = '$uaId')) and a.deleted_at IS NULL ";
        }
        //全部讨论时第一层，comment_id=0
        if ($all) {
            $comments = DB::select("SELECT $otherSelect a.id,a.created_opper ua_id,a.cont,a.created_at time,(CASE WHEN a.status+0=1 THEN 1 ELSE 0 END) isExamining,(CASE WHEN a.deleted_at is null THEN 0 ELSE 1 END) isDelete,(SELECT count(1) FROM `sc_talk_comment_actions` c where c.action_user='$uaId' and c.comment_id=a.id and c.like_category=1 and  c.like_status=1 and c.deleted_at is null) isLike,(SELECT count(*) FROM `sc_talk_comment_actions` c where c.comment_id=a.id and c.like_category=1 and c.like_status=1 and c.deleted_at is null) likeNum,`f_getCommentNum`(a.talk_id,a.id,1,'$uaId') commentNum FROM `sc_talk_comments` a where $w $order limit $start,$pageLimit ");
        } else {
            $comments = DB::select("SELECT $otherSelect a.id,a.created_opper ua_id,a.cont,a.created_at time,(CASE WHEN a.status+0=1 THEN 1 ELSE 0 END) isExamining,(CASE WHEN a.deleted_at is null THEN 0 ELSE 1 END) isDelete,(SELECT count(1) FROM `sc_talk_comment_actions` c where c.action_user='$uaId' and c.comment_id=a.id and c.like_category=1 and  c.like_status=1 and c.deleted_at is null) isLike,(SELECT count(*) FROM `sc_talk_comment_actions` c where c.comment_id=a.id and c.like_category=1 and c.like_status=1 and c.deleted_at is null) likeNum,`f_getCommentNum`(a.talk_id,a.id,2,'$uaId') commentNum FROM `sc_talk_comments` a where $w $order limit $start,$pageLimit ");
        }
        $total = DB::selectOne("SELECT count(*) total FROM `sc_talk_comments` a where $w ");
        //最热评论和最新评论只显示10条
        if ($hot == 1) {
            if ($total->total > 10) {
                $total->total = 10;
            }
        }
        foreach ($comments as $comment) {
            $comment->userInfo = StuMessageService::getUserInfo($comment->ua_id);
            $comment->children = array("data" => array(), "total" => 0);
            if ($all) {
                $comment->children = $this->getChildComments($talkId, $comment->id, $cPage, $cPageLimit);
            }
            $comment->replyContent = array();
            if ($comment->to_id != null) {
                $comment->replyContent = $this->getCommentDetail($comment->to_id, $comment->toua_id);
            }
            if ($comment->isDelete == 1) {
                $comment->cont = null;
                $comment->imgs = array();
            } else {
                $comment->imgs = $this->getCommentImgs($comment->id);
            }
        }
        return array("comments" => $comments, "total" => $total->total);
    }

    /** 获取话题子评论
     * @param $talkId 话题id
     * @param $commentId 评论id
     * @param $page
     * @param $pageLimit
     * @return array
     */
    public function getChildComments($talkId, $commentId, $page, $pageLimit, $hasCheck = true)
    {
        if (!$hasCheck) {
            $this->checkTalkStatus($talkId);
        }
        $uaId = Auth::user()->ua_id;
        $start = ($page - 1) * $pageLimit;
        $w = " (a.`isExamining` = 0 OR (a.`isExamining` = 1 AND a.ua_id = '$uaId')) AND a.isDelete=0 ";
        $childComments = DB::select("SELECT * from (SELECT a.id,a.created_opper ua_id,(SELECT d.created_opper FROM `sc_talk_comments` d where d.id=a.comment_id) toua_id,(CASE WHEN (SELECT f.id FROM `sc_talk_comments` f where f.id in(SELECT e.comment_id FROM `sc_talk_comments` e where e.id=a.comment_id))>0 THEN (SELECT e.id FROM `sc_talk_comments` e where e.id=a.comment_id) ELSE '' END) to_id,a.cont,a.created_at time,(CASE WHEN a.status+0=1 THEN 1 ELSE 0 END) isExamining,(CASE WHEN a.deleted_at is null THEN 0 ELSE 1 END) isDelete,(SELECT count(1) FROM `sc_talk_comment_actions` c where c.action_user='$uaId' and c.comment_id=a.id and c.like_category=1 and  c.like_status=1 and c.deleted_at is null) isLike,(SELECT count(*) FROM `sc_talk_comment_actions` c where c.comment_id=a.id and c.like_category=1 and c.like_status=1 and c.deleted_at is null) likeNum,`f_getCommentNum`(a.talk_id,a.id,2,'$uaId') commentNum FROM (SELECT * FROM sc_talk_comments WHERE talk_id=$talkId and comment_id>0) a, (SELECT @pid := '$commentId') pd WHERE FIND_IN_SET(comment_id, @pid) > 0 AND @pid := concat(@pid, ',', id)) a where $w ORDER BY a.id asc limit $start,$pageLimit ");
        foreach ($childComments as $childComment) {
            //发回复消息人的信息
            $childComment->userInfo = StuMessageService::getUserInfo($childComment->ua_id);
            //被回复人的信息
            //第2层评论以后显示对什么评论了及被评论人信息
            $childComment->replyContent = array();
            if ($childComment->to_id != null) {
                $childComment->replyContent = $this->getCommentDetail($childComment->to_id, $childComment->toua_id);
            }
            if ($childComment->isDelete == 1) {
                $childComment->cont = null;
            } else {
                $childComment->imgs = $this->getCommentImgs($childComment->id);
            }
        }
        $total = DB::selectOne("SELECT count(*) total from (SELECT a.id,a.talk_id,a.created_opper ua_id,a.cont,a.created_at,(CASE WHEN a.status+0=1 THEN 1 ELSE 0 END) isExamining,(CASE WHEN a.deleted_at is null THEN 0 ELSE 1 END) isDelete FROM (SELECT * FROM sc_talk_comments WHERE talk_id=$talkId and comment_id>0) a, (SELECT @pid := '$commentId') pd WHERE FIND_IN_SET(comment_id, @pid) > 0 AND @pid := concat(@pid, ',', id)) a where $w");
        return array("data" => $childComments, "total" => $total->total);
    }

    /** 获取单个评论的信息
     * @param $id
     * @param $toua_id
     * @param $first 添加后即返回对象包含未审核的图片及文字
     * @param $toCommentId
     * @param $reply 是否显示replyContent由前端控制，默认显示
     * @return mixed
     */
    public function getCommentDetail($id, $toua_id, $first = false, $toCommentId = 0, $reply = true)
    {
        $uaId = Auth::user()->ua_id;
        $res = DB::selectOne("SELECT a.id,a.talk_id,a.cont,a.created_at time,(CASE WHEN a.status+0=1 THEN 1 ELSE 0 END) isExamining,(CASE WHEN a.deleted_at is null THEN 0 ELSE 1 END) isDelete,(SELECT count(1) FROM `sc_talk_comment_actions` c where c.action_user='$uaId' and c.comment_id=a.id and c.like_category=1 and  c.like_status=1 and c.deleted_at is null) isLike,(SELECT count(*) FROM `sc_talk_comment_actions` c where c.comment_id=a.id and c.like_category=1 and c.like_status=1 and c.deleted_at is null) likeNum,`f_getCommentNum`(a.talk_id,a.id,2,'$uaId') commentNum FROM sc_talk_comments a where a.id=$id");
        if ($res->isDelete == 1) {
            $res->cont = null;
            $res->imgs = array();
        } else {
            $res->imgs = $this->getCommentImgs($id, $first);
        }
        $res->userInfo = StuMessageService::getUserInfo($toua_id);
        if ($toCommentId > 0) {
            $comment = DB::selectOne("SELECT * FROM `sc_talk_comments` a where a.id=$toCommentId");
            //$toCommentId是对应评论的id，若对应评论有对应评论才显示replyContent
            //第2层评论以后显示对什么评论了及被评论人信息
            if ($comment->comment_id > 0 && $reply) {
                $res->replyContent = $this->getCommentDetail($toCommentId, $comment->created_opper);
            }
        }
        return $res;
    }

    /** 获取评论的图片，虽然目前只支持一张，也返回数组
     * @param $commentId
     * @return array
     */
    function getCommentImgs($commentId, $first = false)
    {
        $w = "and st.`status`=2";
        if ($first) {
            $w = "and st.`status` in (1,2)";
        }
        return DB::select("SELECT st.img_id,sf.file_path img_path,sf.oss_path from sc_comment_imgs st,`sc_file` sf where st.img_id=sf.id and st.comment_id=$commentId $w and st.deleted_at is null");
    }

    /** 话题详情中的其他话题
     * @param $id
     * @return array
     */
    public function getRoundOtherTalks($id)
    {
        return DB::select("SELECT c.id,c.title FROM sc_talks c WHERE c.id IN (SELECT b.id from (SELECT a.id FROM sc_talks a where a.`status`=2 and a.deleted_at is null and id!=$id ORDER BY RAND() LIMIT 3) b)");
    }

    /** 保存评论
     * @param $talkId
     * @param $toCommentId
     * @param $file
     * @param $sourceCont
     * @param $filterCont
     * @param $reply
     * @return array
     * @throws Exception
     */
    public function saveTalkComment($talkId, $toCommentId, $file, $sourceCont, $filterCont, $reply)
    {
        DB::beginTransaction();
        $uaId = Auth::user()->ua_id;
        $otherService = new OtherService();
        $black = $otherService->blackUserForTalk($uaId);
        if (is_string($black)) {
            throw new Exception($black);
        }
        $commentPerDay = $this->checkCommentPerDay($uaId);
        $this->checkTalkStatus($talkId);
        if ($toCommentId > 0) {
            $comment = TalkCommentModel::find($toCommentId);
            if ($comment == null) {
                throw new Exception('对应评论已删除');
            }
            if ($comment->status != TalkCommentModel::STATUS_2_STR) {
                throw new Exception('对应评论未审核通过');
            }
            if ($talkId != $comment->talk_id) {
                throw new Exception('操作失败');
            }
            $first_comment_id = DB::selectOne("SELECT t2.id FROM (SELECT @r as _id, (SELECT @r := comment_id FROM sc_talk_comments WHERE id = _id) as pid, @l := @l + 1 as lvl FROM (SELECT @r := '$toCommentId', @l := 0) vars, sc_talk_comments as h WHERE @r <> 0) t1 JOIN sc_talk_comments t2 ON t1._id = t2.id and t2.talk_id=$talkId and t2.comment_id=0 ORDER BY T1.lvl DESC;")->id;
        } else {
            $first_comment_id = 0;
        }
        $talkComment = new TalkCommentModel();
        $talkComment->setAttribute('cont', $sourceCont);
        $talkComment->setAttribute('comment_id', $toCommentId);
        $talkComment->setAttribute('first_comment_id', $first_comment_id);
        $talkComment->setAttribute('talk_id', $talkId);
        $talkComment->setAttribute('status', TalkCommentModel::STATUS_1);
        $talkComment->setAttribute('created_opper', $uaId);
        $talkComment->save();
        $checkData = array();
        $imgId = 0;
        if ($sourceCont != null && $sourceCont != '') {
            $checkData[] = array("targetType" => UserData::TargetType_TEXT, "content" => $filterCont, "only" => env("SENSITIVE_TEXT_ONLY", true));
        }
        if ($file != null) {
            $fileModele = (new FileService())->uploadFile($file, $uaId);
            if ($fileModele instanceof FileModel) {
                $imgId = $fileModele->id;
            } else {
                throw new Exception($fileModele);
            }
            $img = base64_encode($file->get());
            $storage = Storage::disk(FileService::$filesystem);
            $checkData[] = array("targetType" => UserData::TargetType_IMG, "content" => $img, "platform" => env("CHECK_PLATFORM", "ali"), "fileUrl" => $storage->url($fileModele->file_path), "dataId" => "talkComment_" . $talkComment->id);
        }
        if (count($checkData) > 0) {
            $userData = new UserData();
            $userData->classPath = "App\Http\Services\Talk\TalkService";
            $userData->methodName = "checkTalkComment";
            if ($imgId > 0) {
                DB::insert("INSERT INTO `sc_comment_imgs` (`comment_id`, `img_id`, `created_opper`) VALUES ($talkComment->id,$imgId,'$uaId')");
                $imgs = array($imgId);
            } else {
                $imgs = array();
            }
            $userData->notifyData = array("id" => $talkComment->id, "talkId" => $talkId, "imgs" => $imgs);
            //待审核的具体数据
            $userData->data = $checkData;
        }
        DB::commit();
        if (count($checkData) > 0) {
            $ret = QueueUtil::verifyInfoAsync($userData);
        }
        $message = $commentPerDay == -1 ? "发布成功评论审核中" : "今日剩余可发送评论". ($commentPerDay - 1) . "条";
        return [$message, $this->getCommentDetail($talkComment->id, $uaId, true, $toCommentId, $reply)];
    }

    /** 删除评论或回复
     * @param $commentId
     * @return bool|null
     */
    public function delTalkComment($commentId)
    {
        DB::beginTransaction();
        $comment = TalkCommentModel::where('id', $commentId)->first();
        $uaId = Auth::user()->ua_id;
        if ($comment == null) {
            throw new Exception('该评论已删除');
        }
        if ($comment->created_opper != $uaId) {
            throw new Exception('操作失败');
        }
        $res = $comment->delete();
        DB::commit();
        return $res;
    }

    /** 评论或点赞
     * @param $commentId
     * @param $category
     * @return bool|null
     * @throws Exception
     */
    public function actionComment($commentId, $category)
    {
        $uaId = Auth::user()->ua_id;
        DB::beginTransaction();
        $comment = TalkCommentModel::find($commentId);
        if ($comment == null) {
            throw new Exception('该评论已删除');
        }
        if ($comment->status != TalkCommentModel::STATUS_2_STR) {
            throw new Exception('对应评论未审核通过');
        }
        $action = TalkCommentActionsModel::where(['action_user' => $uaId, 'comment_id' => $commentId, 'like_category' => ($category == '1' ? '点赞' : '踩')])->first();
        if ($action == null) {
            $talkCommentActionsModel = new TalkCommentActionsModel();
            $talkCommentActionsModel->setAttribute('action_user', $uaId);
            $talkCommentActionsModel->setAttribute('comment_id', $commentId);
            $talkCommentActionsModel->setAttribute('like_category', $category);
            $talkCommentActionsModel->setAttribute('like_status', 1);
            $talkCommentActionsModel->save();
            $action = $talkCommentActionsModel;
            //点赞的消息，反复点赞取消再次点赞，只发最开始那一次点赞的消息
            if ($category == 1) {
                $msg2 = "";
                $msgObj2 = StuMessageService::makeUserMsg(StuMessageModel::FROMS_12, $msg2, $uaId, $comment->created_opper, StuMessageModel::PARTY_TYPE_4, $commentId);
            }
        } else {
            if ($action->like_status == 1) {
                $action->like_status = 0;
            } else {
                $action->like_status = 1;
            }
            $action->save();
        }
        DB::commit();
        return $action->id > 0;
    }

    /** 动态获取有哪些排行榜
     * @return array
     */
    public function getRankingTypes()
    {
        $now = date("Y-m-d H:i:s", time());
        $ranks = DB::select("SELECT title,type_id,type_name FROM `sc_student_ranking_types` sr where ? >=sr.start_time and ?<=sr.end_time ORDER BY title_orders,type_orders", [$now, $now]);
        $list = array();
        foreach ($ranks as $item) {
            $titles = array_column($list, "title");
            $title = $item->title;
            if (!in_array($title, $titles)) {
                $list[$title] = array('title' => $title, 'types' => array($item));
            } else {
                $arr = array_reduce($list, function ($carry, $it) use ($title) {
                    if ($it['title'] === $title) {
                        return $it;
                    }
                    return $carry;
                });
                $arr['types'][] = $item;
                $list[$title] = $arr;
            }
            unset($item->title);
        }
        return array_values($list);
    }

    /** 获取排行榜
     * @param $rankType 1 周积分榜,2 月积分榜,3 总积分榜,4 月作业数,5 月抽查词数,6 月度模考高分榜（托福）
     * @param $onlyRank 为true，只返回排行榜列表且不携带学生头像等信息
     * @return array
     */
    public function getRankingDetail($rankType, $onlyRank = false)
    {
        switch ($rankType) {
            case 1:
                $select = 'c.user_id, c.week_point point';
                $field = 'week_point';
                break;
            case 2:
                $select = 'c.user_id, c.month_point point';
                $field = 'month_point';
                break;
            case 3:
                $select = 'c.user_id, c.total_point point';
                $field = 'total_point';
                break;
            case 4:
                $select = 'c.user_id, c.month_homework_num point';
                $field = 'month_homework_num';
                break;
            case 5:
                $select = 'c.user_id, c.month_word_num point';
                $field = 'month_word_num';
                break;
            case 6:
                $select = 'c.user_id, c.month_mock_toefl_score point';
                $field = 'month_mock_toefl_score';
                break;
            case 7:
                break;
            default:
                return array("list" => array(), "me" => array());
        }
        //防止排行榜关闭了还能通过接口查到数据
        if (DB::selectOne("SELECT id FROM `sc_student_ranking_types` sr where sr.type_id=$rankType and NOW()>=sr.start_time and NOW()<=sr.end_time") == null) {
            return array("list" => array(), "me" => array());
        }
        $w = '';
        $BLACK_USER_LIST = env("BLACK_USER_LIST", "");
        if (!empty($BLACK_USER_LIST)) {
            $arr = implode("','", explode(',', $BLACK_USER_LIST));
            $w = " and user_id not in('$arr')";
        }
        if ($rankType == 7) {
            //特殊处理
            $r = $this->rankListOfWord($BLACK_USER_LIST);
            $list = $r[0];
            $_me = $r[1];
        } else {
            $list = DB::select("SELECT $select FROM sc_student_ranking AS c WHERE c.$field >= ( SELECT min( b.$field ) AS min_point FROM ( SELECT a.$field FROM sc_student_ranking AS a where a.$field>0 $w ORDER BY a.$field DESC LIMIT 20 ) AS b ) $w ORDER BY c.$field DESC;");
        }
        //排名，并列后下一个排名直接等于已有人数+1
        $num = 0;
        //最小分数，初始值
        $point = -1;
        //人数
        $count = 0;
        foreach ($list as $item) {
            $count++;
            if ($point != $item->point) {
                $num = $count;
            }
            $item->top = $num;
            $point = $item->point;
            if (!$onlyRank) {
                $item->userInfo = StuMessageService::getUserInfo($item->user_id);
            }
        }
        if (!$onlyRank) {
            $uaId = Auth::user()->ua_id;
            $me = array_reduce($list, function ($carry, $item) use ($uaId) {
                if ($item->user_id == $uaId) {
                    return $item;
                }
                return $carry;
            });
            if ($me == null && $rankType == 7) {
                $me = $_me;
                $me->userInfo = StuMessageService::getUserInfo($me->user_id);
            } else if ($me == null) {
                $me = DB::selectOne("SELECT $select FROM sc_student_ranking AS c WHERE c.user_id='$uaId' $w;");
                if ($me == null) {
                    $me = new stdClass();
                    $me->user_id = $uaId;
                    $me->point = 0;
                    $me->top = 0;
                } else {
                    if ($me->point <= 0) {
                        $me->top = 0;
                    } else {
                        //计算我的排名
                        $myNum = DB::selectOne("SELECT count(*)+1 num FROM sc_student_ranking AS c WHERE c.$field >= " . ($me->point + 1) . " $w");
                        $me->top = $myNum->num;
                    }
                }
                if (!$onlyRank) {
                    $me->userInfo = StuMessageService::getUserInfo($me->user_id);
                }
            }
            return array("list" => $list, "me" => $me);
        } else {
            return array("list" => $list);
        }
    }

    /** 话题推广处显示所有已通过未删除的评论
     * @param $talkId
     * @return array
     */
    public function getAllComments($talkId)
    {
        $this->checkTalkStatus($talkId);
        $time = date("Y-m-d H:i:s", time());
        $ok = ScBannerModel::where("resource_id", $talkId)->where("type", 2)->where("online_time", "<=", $time)->where("offline_time", ">", $time)->first();
        if ($ok == null) {
            return array();
        }
        $uaId = Auth::user()->ua_id;
        $allComments = DB::select("SELECT a.id,a.created_opper ua_id,a.cont,(SELECT count(1) FROM `sc_talk_comment_actions` c where c.action_user='$uaId' and c.comment_id=a.id and c.like_category=1 and  c.like_status=1 and c.deleted_at is null) isLike,(SELECT count(*) FROM `sc_talk_comment_actions` c where c.comment_id=a.id and c.like_category=1 and c.like_status=1 and c.deleted_at is null) likeNum FROM sc_talk_comments a WHERE a.talk_id=$talkId and a.`status`=2 and a.deleted_at is null ORDER BY id desc");
        foreach ($allComments as $childComment) {
            //发回复消息人的信息
            $childComment->userInfo = StuMessageService::getUserInfo($childComment->ua_id);
            $childComment->imgs = $this->getCommentImgs($childComment->id);
        }
        return $allComments;
    }

    /** 关闭【每天前3次评论可获得积分哦～】提醒
     * @param $talkId
     * @return void
     */
    public function closeTalkTip($talkId)
    {
        DB::beginTransaction();
        $this->checkTalkStatus($talkId);
        $uaId = Auth::user()->ua_id;
        if (DB::selectOne("SELECT id FROM `sc_talk_actions` where action_user='$uaId' and talk_id=$talkId and deleted_at is null") == null) {
            $res = DB::insert("INSERT INTO `sc_talk_actions`(action_user,talk_id,category,status) VALUES('$uaId',$talkId,1,1)");
            DB::commit();
            return $res;
        }
        DB::commit();
        return false;
    }

    /** 获取话题评论时候需要的Emoji
     * @return array
     */
    public function getEmoji()
    {
        $list = DB::select("SELECT id,name FROM `sc_emoji_category` where deleted_at is null");
        foreach ($list as $item) {
            $item->detail = DB::select("SELECT id,name,type,source FROM `sc_emoji_config` where category_id=$item->id and deleted_at is null ORDER BY sort desc");
        }
        return $list;
    }

    public function rankListOfWord($backList = "", $needUser = true)
    {
        $date = date("Y-m", time());
        $api = env("WORD_API_URL") . "/api/sc/students/monthly/vocabulary";
        $list = Common::getSimpleCurl($api, ['date' => $date]);
        $uaId = $needUser ? Auth::user()->ua_id : "todo";
        $me = new stdClass();
        $me->user_id = $uaId;
        $me->point = 0;
        $me->top = 0;
        if ($list) {
            $list = json_decode($list, true);
            $list = Arr::get($list, "data");
            $backList = explode(",", $backList);
            if ($backList) {
                foreach ($backList as $u) {
                    foreach ($list as $item) {
                        if (Arr::get($item, "ua_id") == $u) {
                            unset($item);
                            break;
                        }
                    }
                }
            }
            //排名，并列后下一个排名直接等于已有人数+1
            $num = 0;
            //最小分数，初始值
            $point = -1;
            //人数
            $count = 0;
            $limit = 20;
            $limitScore = -1;
            $data = [];
            foreach ($list as $item) {
                $count++;
                $_point = Arr::get($item, "total_vocabulary");
                if ($point != $_point) {
                    $num = $count;
                }
                $point = $_point;
                if ($count == $limit) {
                    //第 limit 名 寻找同分的
                    $limitScore = $point;
                }
                if ($limitScore == -1 || $limitScore == $point) {
                    $data[] = [
                        "user_id" => Arr::get($item, "ua_id"),
                        "point" => $point,
                        "top" => $num,
                    ];
                }
                if ($uaId == Arr::get($item, "ua_id")) {
                    $me->point = $point;
                    $me->top = $num;
                }
            }
            $data = json_encode($data, JSON_UNESCAPED_UNICODE);
            $data = json_decode($data);
            return [$data, $me];
        }
        return [[], $me];
    }
}
