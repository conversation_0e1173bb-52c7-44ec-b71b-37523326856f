<?php

namespace App\Http\Services\Survey;

use App\Models\Student\StudentInfoModel;
use App\Models\Survey\StudentSurveyModel;
use App\Models\Survey\SurveyModel;
use Illuminate\Support\Facades\DB;

class SurveyVerifyService
{
    /**
     * 已存在的验证方法
     * @var string[]
     */
    protected $handles = [
        "firstOrder"
    ];


    /**
     * 判别学生是否在推送模版所划定的范围内
     * @param StudentInfoModel $studentInfo
     * @param SurveyModel $survey
     * @return array|false[]|true[]
     */
    public function verifySurveyWithStudent(StudentInfoModel $studentInfo, SurveyModel $survey)
    {
        //判定调研本身是否生效
        $now = date("Y-m-d H:i:s");
        if ($survey->status != SurveyModel::STATUS_ACTIVE || !($survey->start_time <= $now && $survey->end_time >= $now)) return ["success" => false, "data" => "该调研未生效"];

        $handle = $survey->handle;
        switch ($survey->regular_type) {
            case SurveyModel::REGULAR_TYPE_FREE:
                //todo 暂时这类调研相对比较自由，只想到需要存在有效的handle
                if (!in_array($handle, $this->handles)) return ["success" => false, "data" => "survey verify handle not found"];
                break;
            case SurveyModel::REGULAR_TYPE_MONTHLY:
                //月度调研需要判断是否在对应月内
                $regular = $survey->regular;
                $month = date("n");
                if (!$regular || !in_array($month, $regular)) return ["success" => false, "data" => "未在调研周期内"];
                break;
            default:
                return ["success" => false, "data" => "regular_type error"];
        }

        //判定学员是否在范围内
        switch ($survey->target_type) {
            case SurveyModel::TARGET_TYPE_ALL:
                //全员，不用判定
                break;
            case SurveyModel::TARGET_TYPE_STATIC:
                //静态就直接判断学员id是否在目标数组内
                $targets = $survey->target;
                if (!in_array($studentInfo->uaid, $targets)) return ["success" => false, "data" => "学员不在调研范围内"];
                break;
            case SurveyModel::TARGET_TYPE_DYNAMIC:
                //todo 动态判别学员是否需要接受推送的路子以后再说
                if (!in_array($handle, $this->handles)) return ["success" => false, "data" => "survey verify handle not found"];
                $verify = $this->$handle($studentInfo, $survey);
                if (!$verify['success']) return $verify;

                break;
            default:
                return ["success" => false, "data" => "target_type error"];
        }

        //todo 其他判定，未来再加
        //比如某些调研只调研一次什么的

        //新增互斥逻辑
        if ($survey->regular_type == SurveyModel::REGULAR_TYPE_MONTHLY) {
            //fixme 首单调研的id，写死
            $surveyId = 2;

            //获取学生培训状态做不同处理
            $crmInfo = $studentInfo->crm_info;
            switch ($crmInfo->accountstate) {
                case '在读':
                    break;
                case "自修":
                case "暂无需求":
                    $crmId = $crmInfo->accountid;
                $sql = "SELECT count(DISTINCT cl.id) AS num FROM cas_crm.crm_customer_rights ccr JOIN cas_crm.crm_customer_rights_detail ccrd ON ccrd.rightsid=ccr.rightsid AND ccrd.`status`=2 AND ccrd.costtype!=3 JOIN cas_crm.cas_lessoninfo cl ON cl.id=ccrd.instanceitemid WHERE cl.start_time>=DATE_SUB(NOW(),INTERVAL 90 DAY) AND ccr.customerid=?";
                    $num = DB::selectOne($sql, [$crmId]);
                    if ($num->num > 0) {
                        break;
                    } else {
                        return ["success" => false, "data" => "学员指定范围内没有有效课节核销"];
                    }
                default:
                    return ["success" => false, "data" => "学员状态未在指定范围内"];
            }

            //判断是否和首单推送有重叠
//            DB::connection()->enableQueryLog();
            $studentSurvey = StudentSurveyModel::where([
                ["uaid", $studentInfo->uaid],
                ["status", "!=", StudentSurveyModel::STATUS_INACTIVE],
                ["show", "=", StudentSurveyModel::SHOWED],
                ["show_time", ">=", date("Y-m-d H:i:s", strtotime($now . ' -30 day'))],
                ["survey_id", $surveyId],
            ])->first();
//            $aa = DB::getQueryLog();
            if ($studentSurvey) {
                return ["success" => false, "data" => "指定时间内有首单调研"];
            }
        }

        return ["success" => true, "data" => new \stdClass()];
    }

    /**
     * 验证是否可以创建首笔订单对应的调研
     * @param StudentInfoModel $studentInfo
     * @param SurveyModel $survey
     * @return array
     */
    public function firstOrder(StudentInfoModel $studentInfo, SurveyModel $survey)
    {
        $uaId = $studentInfo->uaid;
        $surveyId = $survey->survey_id;
        //先找找看有没有对应的调研
        $studentSurvey = StudentSurveyModel::where([
            ["uaid", $uaId],
            ["status", "!=", StudentSurveyModel::STATUS_INACTIVE],
            ["survey_id", $surveyId],
        ])
            ->first();
        if ($studentSurvey) return ["success" => false, "data" => "已有对应调研"];

        return ["success" => true, "data" => ""];
    }
}
