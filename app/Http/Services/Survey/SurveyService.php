<?php

namespace App\Http\Services\Survey;

use App\Models\Student\StudentInfoModel;
use App\Models\Survey\StudentSurveyDetailModel;
use App\Models\Survey\StudentSurveyModel;
use App\Models\Survey\SurveyModel;
use App\Utils\CacheUtil;

class SurveyService
{

    /**
     * 是否需要给对应学生发送调研推送
     * @param $uaId
     * @return bool
     */
    public function ifSurvey($uaId)
    {
        //todo 目前触发节点仅仅为是否当月首次登陆，后续有新规则了再加
        return true;//不再通过redis判断，全交给业务规则处理 yxc 20241206
//        return CacheUtil::firstLoginThisMonth($uaId);
    }

    /**
     * 创建学员的调研实例
     * @param StudentInfoModel $studentInfo
     * @param SurveyModel $survey
     * @param  $startTime
     * @param array $ext
     * @return array
     */
    public function createStudentSurvey(StudentInfoModel $studentInfo, SurveyModel $survey, $startTime, array $ext = [])
    {
        $end = "2099-12-31 00:00:00";//todo 暂时不知道什么时候结束，先随便定一个
        //验证
        $surveyVerifyService = new SurveyVerifyService();
        $verify = $surveyVerifyService->verifySurveyWithStudent($studentInfo, $survey);
        if (!$verify["success"]) return $verify;

        if ($survey->regular_type == 1) {
            //月度调研的截止日期为当月的最后一天的23:59:59
            $end = date('Y-m-t 23:59:59');
        }

        $studentSurvey = new StudentSurveyModel();
        $studentSurvey->uaid = $studentInfo->uaid;
        $studentSurvey->survey_id = $survey->survey_id;
        $studentSurvey->show = StudentSurveyModel::UNSHOW;
        $studentSurvey->status = StudentSurveyModel::STATUS_ACTIVE;
        $studentSurvey->start_time = $startTime;
        $studentSurvey->end_time = $end;
        $studentSurvey->ext = $ext;
        $studentSurvey->save();
        return ["success" => true, "data" => $studentSurvey];
    }

    /**
     * 展示学员应该收到的调研
     * @param $uaId
     * @param bool $dev
     * @return mixed
     */
    public function getStudentSurvey($uaId, bool $dev = false)
    {

        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        //找不到学员则跳过
        if (!$studentInfo) return ["success" => false, "data" => "未找到学员"];

        $now = date("Y-m-d H:i:s");

        //获取三十天内是否有已经推送的调研
        $existSurvey = StudentSurveyModel::where([
            ["uaid", $studentInfo->uaid],
            ["status", "!=", StudentSurveyModel::STATUS_INACTIVE],
            ["show", "=", StudentSurveyModel::SHOWED],
            ["show_time", ">=", date("Y-m-d H:i:s", strtotime($now . ' -30 day'))],
        ])->first();
        //30天内有调研则不再推送调研
        if ($existSurvey) {
            return null;
        }

        //获取学员已有需要展示的调研
        $studentSurvey = StudentSurveyModel::where([
            ["uaid", $uaId],
            ["show", StudentSurveyModel::UNSHOW],
            ["status", StudentSurveyModel::STATUS_ACTIVE],
            ["start_time", "<=", $now],
            ["end_time", ">=", $now],
        ])->orderby("start_time")
            ->with("survey", "survey.surveyDetails")
            ->first();

        if (!$studentSurvey) {
            //获取不再新调研的调研模版id
            //通过时间范围判断这个调研是否可以继续调研
            $studentSurveys = StudentSurveyModel::where([
                ["uaid", $uaId],
                ["status", "!=", StudentSurveyModel::STATUS_INACTIVE],
                ["start_time", "<=", $now],
                ["end_time", ">=", $now],
            ])->get();
            $skipArr = [];
            foreach ($studentSurveys as $i) {
                $skipArr[] = $i->survey_id;
            }
            $skipArr = array_unique($skipArr);

            $surveys = SurveyModel::where([
                ['status', SurveyModel::STATUS_ACTIVE],
                ["start_time", "<=", $now],
                ["end_time", ">=", $now],
                ["regular_type", "!=", SurveyModel::REGULAR_TYPE_FREE],
            ])->whereNotIn("survey_id", $skipArr)
                ->orderby("rank", "DESC")//权重排序
                ->orderby("survey_id", "ASC")
                ->get();

            foreach ($surveys as $survey) {
                //验证
                $surveyVerifyService = new SurveyVerifyService();
                $verify = $surveyVerifyService->verifySurveyWithStudent($studentInfo, $survey);
                if (!$verify["success"]) continue;
                $create = $this->createStudentSurvey($studentInfo, $survey, $now);
                if ($create['success']) {
                    $studentSurvey = StudentSurveyModel::where([
                        ["uaid", $uaId],
                        ["show", StudentSurveyModel::UNSHOW],
                        ["status", StudentSurveyModel::STATUS_ACTIVE],
                        ["start_time", "<=", $now],
                        ["end_time", ">=", $now],
                    ])->orderby("start_time")
                        ->with("survey", "survey.surveyDetails")
                        ->first();
                    //todo 单次登陆目前只获取一次调研，以后的变动以后再说
                    break;
                }
            }
        }

        if ($studentSurvey && !$dev) $studentSurvey->show();//掉去过就算显示过了
        return $studentSurvey;
    }

    /**
     * 保存调研内容
     * @param StudentSurveyModel $studentSurvey
     * @param $options
     * @return mixed
     */
    public function saveStudentSurveyDetail(StudentSurveyModel $studentSurvey, $options)
    {
        $studentSurveyId = $studentSurvey->student_survey_id;
        foreach ($options as $option) {
            $option['student_survey_id'] = $studentSurveyId;
            $detail = new StudentSurveyDetailModel($option);
            $detail->save();
        }
        $studentSurvey->complete();

        return StudentSurveyModel::where("student_survey_id", $studentSurveyId)->with("survey", "survey.surveyDetails", "studentSurveyDetail")->first();
    }

    /**
     * 生成学员首笔订单对应的调研
     * @param $uaId
     * @param $effectTime
     * @param array $ext
     * @return array
     */
    public function firstOrderSurvey($uaId, $effectTime, $ext = [])
    {
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        if (!$studentInfo) return ["success" => false, "data" => "no student found"];
        $now = date("Y-m-d H:i:s");
        $targetTime = date("Y-m-d H:i:s", strtotime($effectTime . ' +15 day'));
        $survey = SurveyModel::where([
            ['status', SurveyModel::STATUS_ACTIVE],
            ["start_time", "<=", $now],
            ["end_time", ">=", $now],
            ["target_type", SurveyModel::TARGET_TYPE_DYNAMIC],
            ["handle", "firstOrder"],
        ])->orderby("survey_id", "ASC")
            ->first();
        if (!$survey) return ["success" => false, "data" => "no survey found"];

        //验证
        $surveyVerifyService = new SurveyVerifyService();
        $verify = $surveyVerifyService->verifySurveyWithStudent($studentInfo, $survey);
        if (!$verify["success"]) return $verify;

        return $this->createStudentSurvey($studentInfo, $survey, $targetTime, $ext);
    }

}
