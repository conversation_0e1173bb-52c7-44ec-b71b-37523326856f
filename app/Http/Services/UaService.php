<?php

namespace App\Http\Services;

use GuzzleHttp\Client;

class UaService
{

    const LOGIN_TYPE_USER_CODE = 2;
    const LOGIN_TYPE_PHONE = 3;
    const LOGIN_SOURCE_NAME = 'lms';
    const LOGIN_USER_TYPE_TEACHER = 1;

    private $strUaUrl = '';

    public function __construct()
    {
        $this->strUaUrl = config('ua.url');
    }

    public function teachertLogin($arrPostData)
    {
        $arrRequestData['type'] = $this->_getLoginType($arrPostData['loginname']);
        $arrRequestData['name'] = $arrPostData['loginname'];
        $arrRequestData['code'] = $arrPostData['password'];
        $arrRequestData['source_name'] = self::LOGIN_SOURCE_NAME;
        $arrRequestData['user_type_id'] = self::LOGIN_USER_TYPE_TEACHER;

        return $this->_uaLogin($arrRequestData);
    }

    public function studentLogin($arrPostData)
    {
        $arrRequestData['type'] = $this->_getLoginType($arrPostData['loginname']);
        $arrRequestData['name'] = $arrPostData['loginname'];
        $arrRequestData['code'] = $arrPostData['password'];
        $arrRequestData['source_name'] = self::LOGIN_SOURCE_NAME;
        $arrRequestData['user_type_id'] = $arrPostData['user_type_id'];

        return $this->_uaLogin($arrRequestData);
    }

    private function _uaLogin($arrRequestData)
    {
        $client = new Client(['verify' => false]);
        $response = $client->post($this->strUaUrl . '/api/auth/login', [
            'form_params' => $arrRequestData,
        ]);
        return json_decode($response->getBody()->getContents());
    }

    private function _getLoginType($strLoginName)
    {
        if (strlen($strLoginName) == 11) {
            return self::LOGIN_TYPE_PHONE;
        } else {
            return self::LOGIN_TYPE_USER_CODE;
        }
    }

    private function _getUASecret()
    {
        $ua_key = "-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArji56j5sIL/76u3N/MUC
IzwncDF52CNBU6O3LFBwPcRTArdRJJ4gAxvmYgLMNhSStySSXiyb0UtTqU+lBuBk
V6TjkPrbiDdT/5MGiWdZ0ICtIu4u1Tls/MHxdPM/9amJvpqcMTRm7YT7+isN6VDT
WtaqH9XWJgVS+1BnzxAcKMTYznye4NH5OsaKCwexqlzDU8ve3WIvzltZyUpB8pAy
zP4Uy3waEpC1RbnWvG/duBBzA7FbAXPXcGEbzfTxBsyqLrkQ/j+Yt6+JcRbfJNhm
joKh/xdxp0MY3E8e9b08NvFRLU8x1WDhWnYvVtByrCToCNbB4DKUxmKL6bwYPaoF
vQIDAQAB
-----END PUBLIC KEY-----";
        //公钥加密
        $publicKey = openssl_pkey_get_public($ua_key);
        if (!$publicKey) {
            return false;
        }
        //加密内容, 部分接口不限内容只取明文参数
        $jsonStr = 'qwerty';
        $des = openssl_public_encrypt($jsonStr, $crypted, $publicKey);
        if (!$des) {
            return false;
        }
        $data = base64_encode($crypted);

        return $data;
    }

    public function verifyToken($Token, $ua_sig_key)
    {
        $tokens = explode('.', $Token);
        if (count($tokens) != 3)
            return false;

        list($base64header, $base64payload, $sign) = $tokens;

        //获取jwt算法
        $base64decodeheader = json_decode($this->base64UrlDecode($base64header), JSON_OBJECT_AS_ARRAY);
        if (empty($base64decodeheader['alg']))
            return false;

        //签名验证
        if ($this->signature($base64header . '.' . $base64payload, $ua_sig_key, $base64decodeheader['alg']) !== $sign)
            return false;

        $payload = json_decode($this->base64UrlDecode($base64payload), JSON_OBJECT_AS_ARRAY);

        //签发时间大于当前服务器时间验证失败
        if (isset($payload['iat']) && $payload['iat'] > time())
            return false;

        //过期时间小于当前服务器时间验证失败
        if (isset($payload['exp']) && $payload['exp'] < time())
            return false;

        //该nbf时间之前不接收处理该Token
        if (isset($payload['nbf']) && $payload['nbf'] > time())
            return false;

        return $payload;
    }

    private function base64UrlEncode($input)
    {
        return str_replace('=', '', strtr(base64_encode($input), '+/', '-_'));
    }

    private function base64UrlDecode($input)
    {
        $remainder = strlen($input) % 4;
        if ($remainder) {
            $addlen = 4 - $remainder;
            $input .= str_repeat('=', $addlen);
        }
        return base64_decode(strtr($input, '-_', '+/'));
    }

    private function signature($input, $key, $alg = 'HS256')
    {
        $alg_config = array(
            'HS256' => 'sha256'
        );
        return $this->base64UrlEncode(hash_hmac($alg_config[$alg], $input, $key, true));
    }
}
