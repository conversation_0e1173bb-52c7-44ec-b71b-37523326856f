<?php


namespace App\Http\Services\File;


use App\ConstDir\File\FileConst;
use App\Models\File\FileModel;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class FileService
{
    public static $filesystem = 'sc';

    /**
     * 上传文件
     * @param UploadedFile $file
     * @param string $userId
     * @param int $status
     * @param int $checkStatus
     * @param int $maxSize
     * @param string[] $allowExtension
     * @param string $dir
     * @param string $path
     * @return string|true
     */
    public function uploadFile(UploadedFile $file, $userId = "", $status = FileConst::STATUS_NOT_IN_USE, $checkStatus = FileConst::CHECK_STATUS_ING, $maxSize = FileConst::MAX_FILE_SIZE, $allowExtension = FileConst::IMAGE_ALLOW_EXT, $dir = "/sc/image", $path = "")
    {
        if (($check = $this->checkFile($file, $allowExtension, $maxSize)) !== true) {
            return $check;
        }
        $path = $this->saveFile($file, $dir, $path);
        if (!$path) {
            return FileConst::FILE_UPLOAD_ERROR;
        }
        $create = [
            "file_path" => $path,
            "file_name" => $file->getClientOriginalName(),
            "status" => $status,
            "check_status" => $checkStatus,
            "user_id" => $userId
        ];
        return FileModel::create($create);
    }

    public function saveFile($fileObj, $dir = '/sc/image', $path = '')
    {
        Storage::disk(self::$filesystem)->makeDirectory($dir);
        $name = Str::random(40) . '.' . $fileObj->getClientOriginalExtension();
        return Storage::disk(self::$filesystem)->putFileAs($dir . $path, $fileObj, $name);
    }


    public function uploadImage(UploadedFile $file, $userId, $status = FileConst::STATUS_NOT_IN_USE, $checkStatus = FileConst::CHECK_STATUS_ING, $path = "")
    {
        return $this->uploadFile($file, $userId, $status, $checkStatus, FileConst::MAX_FILE_SIZE, FileConst::IMAGE_ALLOW_EXT, "/sc/image", $path);
    }

    public function checkFile($fileObj, $allow = [], $fileSize = FileConst::MAX_FILE_SIZE)
    {
        if (!is_array($fileObj)) {
            $_fileObj = [$fileObj];
        } else {
            $_fileObj = $fileObj;
        }
        foreach ($_fileObj as $fileObj) {
            if (!$fileObj || !$fileObj->isValid()) {
                return FileConst::FILE_UPLOAD_ERROR;
            }
            $extension = $this->getFileExtension($fileObj);
            if (!in_array(strtolower($extension), $allow)) {
                return FileConst::FILE_NOT_ALLOW;
            }
            //校验大小
            $size = $fileObj->getSize();
            if ($size > $fileSize * 1024 * 1024) {
                return sprintf(FileConst::FILE_SIZE_TO_LARGE, $fileSize);
            }
        }
        return true;
    }

    /**
     * 获取拓展名
     */
    public function getFileExtension($fileObj)
    {
        if (!$fileObj->isValid()) {
            return '';
        }
        return $fileObj->getClientOriginalExtension();
    }

    public function updateCheckStatus($fileId, $checkStatus)
    {
        return FileModel::where('id', $fileId)->update(['check_status' => $checkStatus]);
    }
}
