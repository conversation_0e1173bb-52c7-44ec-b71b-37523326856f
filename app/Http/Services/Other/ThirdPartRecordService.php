<?php


namespace App\Http\Services\Other;


use App\ConstDir\Medal\MedalConst;
use App\ConstDir\ThirdPart\ThirdPartMessageConst;
use App\Events\Medal\MedalEvent;
use App\Http\Services\Point\PointService;
use App\Models\Student\StudentStudyTimeModel;
use App\Models\ThirdPartRecordModel;
use App\Utils\Common;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;

class ThirdPartRecordService
{
    public const SOURCE_TYPE = [
        'finish.homework',
        'finish.test',
        'open.book',
        'finish.homeworkPackage',
    ];

    //勋章触发
    const MEDAL_TYPE = "reward.medal";

    public function dealMessage($data)
    {
        $insertData = [
            "user_id" => Arr::get($data, "user_id"),
            "platform" => Arr::get($data, "platform"),
            "source_type" => Arr::get($data, "type", ""),
            "source_id" => Arr::get($data, "sourceId", 0),
            "record_time" => Arr::get($data, "recordTime", date("Y-m-d H:i:s", time())),
            "record_date" => date("Ymd", strtotime(Arr::get($data, "recordTime", date("Y-m-d H:i:s", time())))),
            "ext" => Arr::get($data, "ext"),

        ];
        $insertData = $this->dealTime($insertData);
        $model = ThirdPartRecordModel::create($insertData);
        $this->rewardScore($model);
        $this->calcSelectTime($model);
        $this->dealMedal($model, $data);
        $this->updateRank($model);
    }

    public function dealTime($data = []) {
        $data['record_time'] = date("Y-m-d H:i:s", strtotime($data['record_time']));
        return $data;
    }

    /**
     * @param $model ThirdPartRecordModel 对应的记录模型
     */
    public function rewardScore($model)
    {
        $point = new PointService();
        if ($model->platform == ThirdPartMessageConst::MILLION_WORD && in_array($model->source_type, [ThirdPartMessageConst::MILLION_PRACTICE, ThirdPartMessageConst::MILLION_WORD_REMEMBER])) {
            $type = 4;
            if ($model->source_type == ThirdPartMessageConst::MILLION_WORD_REMEMBER) {
                $type = Arr::get([0, 1, 2, 3], Arr::get($model->ext, "aural.type", 0));
            }
            $point->wordPoint($model->user_id, $type);
        }
        if ($model->platform == ThirdPartMessageConst::LMS && in_array($model->source_type, [ThirdPartMessageConst::LMS_FINISH_TEST, ThirdPartMessageConst::LMS_FINISH_HOMEWORK, ThirdPartMessageConst::LMS_FINISH_HOMEWORK_PACKAGE])) {
            if ($model->source_type == ThirdPartMessageConst::LMS_FINISH_TEST || $model->source_type == ThirdPartMessageConst::LMS_FINISH_HOMEWORK) {
                //重做不获取积分
                $isRedo = Arr::get($model->ext, "isRedo", false);
                if (!$isRedo || $model->source_type == ThirdPartMessageConst::LMS_FINISH_TEST) {
                    $point->homeworkPoint($model->user_id, 1);
                }
            }
            //作业包完成 作业数量 要>1
            if ($model->source_type == ThirdPartMessageConst::LMS_FINISH_HOMEWORK_PACKAGE && Arr::get($model->ext, "questNum", 1) >= 2) {
                $point->homeworkPoint($model->user_id, 2);
            }
        }
    }

    public function calcSelectTime($model)
    {
        if ($model->platform == ThirdPartMessageConst::MILLION_WORD && in_array($model->source_type, [ThirdPartMessageConst::MILLION_PRACTICE, ThirdPartMessageConst::MILLION_WORD_REMEMBER])) {
            $time = Arr::get($model->ext, "time", 0);
            $day = date("Y-m-d", strtotime($model->record_time));
            $userRecord = StudentStudyTimeModel::where("user_id", $model->user_id)->first();
            if ($userRecord) {
                $userDate = $userRecord->record;
            } else {
                $data = [];
                $start = date("Y-m-d", time() - 86400);
                for ($i = 0; $i < 30; $i++) {
                    $_day = date("Y-m-d", strtotime($start) - (30 - $i - 1) * 86400);
                    $data[$_day] = 0;
                }
                $userDate = [
                    "total" => $data,
                ];
            }
            if (!Arr::has($userDate, "total." . $day)) {
                $userDate["total"][$day] = (int)$time;
            } else {
                $userDate["total"][$day] += $time;
            }
            StudentStudyTimeModel::updateOrCreate(["user_id" => $model->user_id], [
                "record" => $userDate,
            ]);
        }
    }

    public function dealMedal($model, $_data = [])
    {
        //进行勋章逻辑处理
        //能触发勋章的有 学习坚守者 阅读之星 社交达人
        $data = [];
        if ($model->platform == ThirdPartMessageConst::LMS && in_array($model->source_type, [ThirdPartMessageConst::LMS_FINISH_HOMEWORK, ThirdPartMessageConst::LMS_FINISH_TEST, ThirdPartMessageConst::LMS_FINISH_HOMEWORK_PACKAGE, ThirdPartMessageConst::LMS_OPEN_BOOK])) {
            $data[] = [
                'id' => $model->user_id,
                'progress' => 1,
            ];
            //事件分发
            $medalType = "";
            if (in_array($model->source_type, [ThirdPartMessageConst::LMS_FINISH_HOMEWORK, ThirdPartMessageConst::LMS_FINISH_TEST])) {
                $medalType = MedalConst::STUDY_INSIST;
            } else if (in_array($model->source_type, [ThirdPartMessageConst::LMS_OPEN_BOOK])) {
                $cmsFileId = (int) Arr::get($model->ext, "cmsFileId", 0);
                $find = DB::select('SELECT id FROM sc_third_part_record WHERE user_id = ? AND platform = "lms" AND source_type = "open.book" and ext->"$.cmsFileId" = ? and id != ?', [$model->user_id, $cmsFileId, $model->id]);
                if ($find) {
                    //不重复获得
                    return;
                }
                $medalType = MedalConst::READ_STAR;
            }
            Event::dispatch(new MedalEvent($medalType, $data));
        }
        //获取勋章
        if ($model->platform == ThirdPartMessageConst::MILLION_WORD && $model->source_type == ThirdPartMessageConst::MILLION_REWARD_MEDAL) {
            Event::dispatch(new MedalEvent(ThirdPartMessageConst::MILLION_WORD, $_data));
        }

    }

    public function uaLinks()
    {
        $url = env("UA_URL", "") . "/api/sso";
        $result = Common::getSimpleCurl($url, ["redirect" => false], ["cookie" => Common::getCookieStr()]);
        try {
            $_result = [];
            $result = json_decode($result, true);
            $result = Arr::get($result, "data.platform_info", []);
            foreach ($result as $item) {
                $data = Arr::get($item, "data", []);
                foreach ($data as $_data) {
                    $_result[] = $_data;
                }
            }
            $result = $_result;
        } catch (\Exception $e) {
            $result = [];
        }
        return $result;
    }

    public function uaTarget($id)
    {
        $url = env("UA_URL", "") . "/api/sso/getURLInfo";
        $result = Common::getSimpleCurl($url, ["source_id" => $id], ["cookie" => Common::getCookieStr()]);
        try {
            $result = json_decode($result, true);
            $result = Arr::get($result, "data", []);
        } catch (\Exception $e) {
            $result = [];
        }
        return $result;
    }

    /**
     * 其它平台消息
     * @param string $type 类型
     */
    public function messageList($type = "lms", $page = 1, $pageNum = 10)
    {
        if ($type == "lms") {
            return $this->lmsMessageList($page, $pageNum);
        }
        return $this->wordMessageList($page, $pageNum);
    }

    /**
     * 其它平台未读消息
     * @param string $type
     * @return array
     */
    public function unReadMessage($type = "")
    {
        $data = [
            "lms" => 0,
            "word" => 0
        ];
        if (!$type) {
            $data['lms'] = (int) Cache::remember("sc:lms:unread:" . Auth::user()->ua_id, 60, function () {
               return $this->unReadLms();
            });
            $data['word'] = (int) Cache::remember("sc:word:unread:" . Auth::user()->ua_id, 60, function () {
               return $this->unReadWord();
            });
        } else if ($type == 'lms') {
            $data['lms'] = (int) Cache::remember("sc:lms:unread:" . Auth::user()->ua_id, 60, function () {
               return $this->unReadLms();
            });
        } else {
            $data['word'] = (int) Cache::remember("sc:word:unread:" . Auth::user()->ua_id, 60, function () {
              return $this->unReadWord();
            });
        }
        $data['lms'] = !$data['lms'] ? 0 : $data['lms'];
        $data['word'] = !$data['word'] ? 0 : $data['word'];
        return $data;
    }

    public function unReadLms()
    {
        $url = env("LMS_URL") . "/api/sc/message/list";
        $result = Common::getSimpleCurl($url, [], ["cookie" => Common::getCookieStr()]);
        $result = json_decode($result, true);
        return Arr::get($result, 'data.unRead', 0);
    }

    public function unReadWord()
    {
        $url = env("WORD_API_URL") . "/api/message/unread/number";
        $result = Common::getSimpleCurl($url, [], ["cookie" => Common::getCookieStr()]);
        $result = json_decode($result, true);
        return Arr::get($result, 'data', 0);
    }

    public function lmsMessageList($page = 1, $pageNum = 10)
    {
        $url = env("LMS_URL") . "/api/sc/message/list";
        $result = Common::getSimpleCurl($url, ["page" => $page, "pageNum" => $pageNum], ["cookie" => Common::getCookieStr()]);
        $result = json_decode($result, true);
        return Arr::get($result, 'data', new \stdClass());
    }

    public function wordMessageList($page = 1, $pageNum = 10)
    {
        $url = env("WORD_API_URL") . "/api/message";
        $result = Common::postSimpleCurl($url, ["page" => $page, "pageSize" => $pageNum], ["cookie" => Common::getCookieStr()]);
        $result = json_decode($result, true);
        return Arr::get($result, 'data', new \stdClass());
    }

    public function readWordMessage($id = 0) {
        $url = env("WORD_API_URL") . "/api/message/read";
        Common::postSimpleCurl($url, ['clear' => !$id ? 1 : 0, 'message_id' => $id], ["cookie" => Common::getCookieStr()]);
        return true;
    }

    public function readLmsMessage($id = 0) {
        $url = env("LMS_URL") . "/api/sc/message/read";
        Common::postSimpleCurl($url, ["allRead" => !$id, 'messageId' => $id], ["cookie" => Common::getCookieStr()]);
        return true;
    }

    public function updateRank($model) {
        //涉及到更新排行榜的
        $point = new PointService();
        if (in_array($model->source_type, [ThirdPartMessageConst::LMS_FINISH_HOMEWORK, ThirdPartMessageConst::LMS_FINISH_TEST, ThirdPartMessageConst::MILLION_WORD_REMEMBER, ThirdPartMessageConst::MILLION_PRACTICE])) {
            $point->updateStudentRanking($model->user_id, $model->record_time, 1, 1);
            //单词抽查
            if (in_array($model->source_type, [ThirdPartMessageConst::MILLION_PRACTICE])) {
                $point->updateStudentRanking($model->user_id, $model->record_time, 2, (int) Arr::get($model->ext, "nums", 1));
            }
        }
        //
        //lms 出分 托福 全科
        if (in_array($model->source_type, [ThirdPartMessageConst::LMS_OVER_CORRECT_TEST]) && Arr::get($model->ext, "examPaperType", "") == ThirdPartMessageConst::LMS_TOEFL_EXAM_TYPE && count((array) Arr::get($model->ext, "sectionScore", [])) == 4 && (int)Arr::get($model->ext, "version", 1) == 2) {
            $point->updateStudentRanking($model->user_id, $model->record_time, 3, (int) Arr::get($model->ext, "score", 0));
        }
    }
}
