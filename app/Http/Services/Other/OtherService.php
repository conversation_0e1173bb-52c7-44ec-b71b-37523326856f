<?php


namespace App\Http\Services\Other;


use App\Http\Services\File\FileService;
use App\Models\BlackUserConfigModel;
use App\Models\FastModel\FastQueryModel;
use App\Models\File\FileModel;
use App\Models\MenuModel;
use App\Models\Student\StudentInfoModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class OtherService
{
    public function menu() {
        return MenuModel::where("status", 1)->orderBy("sort")->get();
    }

    public function ossCallback($data) {
        //todo
        $data = is_string($data) ? json_decode($data, true) : $data;
        $id = Arr::get($data, "id", 0);
        $success = Arr::has($data, "url");
        $model = FileModel::find($id);
        if ($model) {
            $model->oss_path = $data;
            $model->oss_status = $success ? FileModel::OSS_SUCCESS : FileModel::OSS_CALLBACK_ERROR;
            $model->save();
            if ($success) {
                $path = $model->file_path;
                $storage = Storage::disk(FileService::$filesystem);
                if ($storage->exists($path)) {
                    $mv = preg_replace('/(\.\w+)$/', '_bak$1', $path);
                    $storage->move($path, $mv);
                }
            }
        }
        return true;
    }

    public function blackUserForTalk($uaId = "") {
        return $this->blackUser(1, $uaId);
    }

    public function blackUser($type = 1, $uaId = "") {
        $time = date("Y-m-d H:i:s", time());
        $model = BlackUserConfigModel::where("type", $type)->where("start_time", "<=", $time)->where("open_time", ">=", $time)->where("user_id", $uaId)->first();
        if ($model) {
            //糟糕，被禁言了--。解禁时间：xx年xx月xx日00:00分
            $str = "糟糕，被禁言了--。解禁时间：Y年m月d日H:i分";
            return date($str, strtotime($model->open_time));
        }
        return false;
    }

    public function studentInfo($uaIds)
    {
        if (!$uaIds) {
            return new \stdClass();
        }
        $temp = explode(",", $uaIds);
        $studentList = StudentInfoModel::whereIn("uaid", $temp)->get();
        $list = [];
        foreach ($studentList as $studentInfo) {
            $studentInfo->avaterInfo();
            $studentInfo->skinInfo();
            $list[$studentInfo->uaid] = [
                "avater" => $studentInfo->avater_url,
                "avater_frame" => $studentInfo->avater_frame_info ? Arr::get($studentInfo->avater_frame_info,"path", "") : "",
                "skin_info" => $studentInfo->skin_info,
            ];
        }
        if (!$list) {
            $list = new \stdClass();
        }
        return $list;
    }

    public function navigation()
    {
        return FastQueryModel::navigationConfig()->where("status", 1)->orderByDesc("sort")->get();
    }
}
