<?php

namespace App\Http\Services;

use App\ConstDir\Medal\MedalConst;
use App\Events\Medal\MedalEvent;
use App\Http\Services\Point\PointService;
use App\Models\ScBanner\ScBannerModel;
use App\Models\UaBanner\BannerApplyModel;
use App\Models\UaBanner\BannerClickModel;
use App\Models\UaBanner\BannerModel;
use GuzzleHttp\Client;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Event;

class BannerService
{
    public function list()
    {
        $objWorkshopList = BannerModel::where([
            'position' => BannerModel::POSITION_2,
            'is_show' => 1
        ])->orderBy('sort', 'desc')->get();
        foreach ($objWorkshopList as &$item) {
            $item['can_reserve'] = $item['extension_json']['can_reserve'] ?? false;
        }
        return [
            'competition' => BannerModel::where([
                'position' => BannerModel::POSITION_1,
                'is_show' => 1
            ])->orderBy('sort', 'desc')->get(),
            'workshop' => $objWorkshopList,
        ];
    }

    public function classInfo($iBannerId)
    {
        $objBanner = BannerModel::findOrFail($iBannerId);
        $arrExtension = $objBanner['extension_json'];
        $arrExtension['class_title'] = $objBanner->title;
        return $arrExtension;
    }

    public function reserve($strToken, $strClassCode, $bannerId, $userId)
    {
        $strApi = env('CRM_URL') . '/api/index.php/ua/reserve';
        $objClient = new Client();
        $data = [
            'form_params' => [
                'access_token' => $strToken,
                'classnum' => $strClassCode,
                'pre_check' => true,
            ]
        ];
        $response = $objClient->post($strApi, $data);
        $response = json_decode($response->getBody()->getContents(), true);
        if ($response['code'] == 100) {
            //判断是否已经预约过
            $find = BannerApplyModel::where("user_id", $userId)->where("banner_id", $bannerId)->first();
            if ($find) {
                unset($data['form_params']['pre_check']);
                $objClient->post($strApi, $data);
                return '恭喜，已帮你预留名额。名额仅预留1天，快联系督导报名吧';
            }
            $objBanner = BannerModel::findOrFail($bannerId);
            $r = (new PointService())->signUpWorkshop($userId, $objBanner->title);
            if ($r['success'] == 1) {
                unset($data['form_params']['pre_check']);
                $objClient->post($strApi, $data);
                $this->afterReserve($bannerId, $userId);
                return '恭喜，已帮你预留名额。名额仅预留1天，快联系督导报名吧';
            }
            return $r['msg'];
        } else {
            return $response['msg'];
        }
    }

    public function afterReserve($bannerId, $userId) {
        BannerApplyModel::create([
            "user_id" => $userId,
            "banner_id" => $bannerId
        ]);
        Event::dispatch(new MedalEvent(MedalConst::WORKSHOP, [[
            "id" => $userId,
            "progress" => 1
        ]]));
    }

    public function click($iBannerId, $iStudentId, $iPosition = 0)
    {
        return BannerClickModel::create([
            'banner_id' => $iBannerId,
            'user_id' => $iStudentId,
            'position' => $iPosition
        ]);
    }

    public function scList() {
        $time = date("Y-m-d H:i:s", time());
        $list = ScBannerModel::where("online_time", "<=", $time)->where("offline_time", ">", $time)->orderBy("sort")->get();
        $_list = collect();
        foreach ($list as $item) {
            $item->resource_title = "";
            $item->resource_sub_title = "";
            if (in_array($item->type, ScBannerModel::UA_BANNER)) {
                $item->resource = $item->uaBanner()->first();
                $item->resource_title = Arr::get($item->resource, "title", "");

                if ($item->type == 1) {
                    $item->resource_sub_title = Arr::get($item->resource, "extension_json.class_begin_time", "");
                }
            } else if (in_array($item->type, ScBannerModel::SC_TALK)) {
                $item->resource = $item->talk()->where("status", "已上线")->first();
                if (!$item->resource) {
                    continue;
                }
                $item->resource_title = Arr::get($item->resource, "title", "");
            } else {
                $item->resource = new \stdClass();
            }
            if (!$item->resource) {
                $item->resource = new \stdClass();
            }
            $_list->add($item);
        }
        return $_list;
    }
}
