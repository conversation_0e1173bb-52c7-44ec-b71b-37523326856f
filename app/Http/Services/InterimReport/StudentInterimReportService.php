<?php

namespace App\Http\Services\InterimReport;

use App\Models\InterimReport\StudentInterimReportInfoModel;
use App\Models\InterimReport\StudentInterimReportModel;
use App\Models\InterimReport\StudentInterimReportTeacherModel;
use App\Models\Student\StudentInfoModel;
use App\Utils\Common;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use SebastianBergmann\Diff\Exception;

class StudentInterimReportService
{
    public const WORD_SUMMARY = "word.summary";
    public const WORD_BOOK = "word.book";
    public const LMS_HOMEWORK = "lms.homework";
    public const LMS_TEST = "lms.test";
    public const CAS_LESSON = "cas.lesson";

    public const SUPERVISOR_FEEDBACK = "supervisor.feedback";
    public const TEACHER_FEEDBACK = "teacher.feedback";

    public const CAN_DO_SOURCE = [self::WORD_SUMMARY, self::WORD_BOOK, self::LMS_HOMEWORK, self::LMS_TEST, self::CAS_LESSON];

    public function createInterimReport($data)
    {
        $uaId = Arr::get($data, "uaId", "");
        $startDate = Arr::get($data, "startDate", "");
        $endDate = Arr::get($data, "endDate", "");
        $teachers = Arr::get($data, "teachers", "");
        $source = Arr::get($data, "source", "");
        $createUser = optional(Auth::user())->ua_id ?: "";
        $isMp = Arr::get($data, "isMp", false) == 'true';

        $student = StudentInfoModel::find($uaId);
        if (!$student) {
            return false;
        }
        $find = $this->supervisorStudent($isMp, "", false, $uaId);
        if ($find->count() == 0) {
            return false;
        }

        $teachers = explode(",", $teachers);
        $canDoTeacher = $this->timeOfStudentClassTeacher($uaId, $startDate, $endDate, true);
        if ($canDoTeacher->count() == 0) {
            return false;
        }
        $_teachers = [];
        collect($teachers)->filter(function ($item) use ($canDoTeacher, &$_teachers) {
            foreach ($canDoTeacher as $teacher) {
                $md5 = md5($teacher->ua_id . $teacher->instanceid . $teacher->subject_id);
                if ($md5 == $item) {
                    $_teachers[] = $teacher->ua_id . "|" . $teacher->instance_name . " " . $teacher->instance_code . " " . $teacher->subject_name . " " . $teacher->teacher_name;
                    return true;
                }
            }
            return "";
        });
        if (!$_teachers) {
            return false;
        }
        $teachers = collect($_teachers)->unique()->toArray();
        $sourceArr = explode(",", $source);
        $sourceArr = collect($sourceArr)->filter(function ($item) {
            return in_array($item, StudentInterimReportService::CAN_DO_SOURCE);
        });
        $reportName = "%s %s~%s 学习报告";
        $reportName = sprintf($reportName, $student->name, $startDate, $endDate);
        $insert = [
            "report_name" => $reportName,
            "student_id" => $uaId,
            "create_user" => $createUser,
            "status" => 0,
            "report_start" => $startDate,
            "report_end" => $endDate,
            "source" => $sourceArr,
        ];
        DB::beginTransaction();
        $model = StudentInterimReportModel::create($insert);
        //创建教师关联
        $insertTeacher = [];
        foreach ($teachers as $teacher) {
            $teacher = explode("|", $teacher);
            $insertTeacher[] = [
                "report_id" => $model->id,
                "teacher_id" => Arr::get($teacher, 0, ""),
                "relation" => Arr::get($teacher, 1, ""),
                "status" => 0,
            ];
        }
        if ($insertTeacher) {
            StudentInterimReportTeacherModel::insert($insertTeacher);
        }
        DB::commit();
        $this->flashReport($model->id);
    }

    public function updateReport($data)
    {
        $isSupervisor = Arr::get($data, "isSupervisor", false) == 'true';
        //更新数据
        $rId = Arr::get($data, "id", 0);
        $report = StudentInterimReportModel::find($rId);
        if (!$report) {
            return "报告已被督导删除";
        }
        //教师反馈 可能是督导反馈
        $teacherFeedBack = Arr::get($data, "teacherFeedBack", []);
        //["id" => 1, "feedBack" => "123455"]
        $teacherFeedBack = collect($teacherFeedBack);
        $r = false;
        if ($isSupervisor) {
            $r = $this->supervisorUpdateReport($report, $teacherFeedBack, $data);
        } else {
            $r = $this->teacherUpdateReport($report, $teacherFeedBack, $data);
        }
        if (!$r) {
            return false;
        }

        $this->reportFinish($report->id);
        return StudentInterimReportModel::find($rId);
    }

    public function teacherUpdateReport(StudentInterimReportModel $report, Collection $teacherFeedBack, $data = []): bool
    {
        $id = $teacherFeedBack->pluck("id");
        $user = optional(Auth::user())->ua_id ?: "";
        $teacherFeedBackList = StudentInterimReportTeacherModel::whereIn("id", $id)->where("report_id", $report->id)->where("status", 0)->get();
        $needTeacherFill = $report->teacherFeedback()->get();
        $needTeacherFill = collect($needTeacherFill)->filter(function ($item) use ($user) {
            return $item->teacher_id == $user;
        });
        if ($teacherFeedBackList->count() != $needTeacherFill->count()) {
            return false;
        }
        $teacherFill = [];
        foreach ($teacherFeedBackList as $item) {
            foreach ($teacherFeedBack as $feedback) {
                if ($item->id == Arr::get($feedback, "id", 0)) {
                    $teacherFill[] = [
                        "report_id" => $report->id,
                        "create_user" => $user,
                        "update_user" => "",
                        "report_type" => self::TEACHER_FEEDBACK,
                        "report_info" => ["relation_id" => $item->id, "relation" => $item->relation, "feedback" => Arr::get($feedback, "feedback", "")],
                    ];
                }
            }
        }
        DB::beginTransaction();
        StudentInterimReportTeacherModel::whereIn("id", $needTeacherFill->pluck("id"))->update(["status" => 1]);
        if ($teacherFill) {
            collect($teacherFill)->map(function ($item) {
                StudentInterimReportInfoModel::create($item);
            });
        }
        DB::commit();
        return true;
    }

    public function supervisorUpdateReport(StudentInterimReportModel $report, $teacherFeedBack = [], $data = []): bool
    {
        $title = Arr::get($data, "title", "");
        $supervisor = Arr::get($data, "supervisorFeedback", "");
        $user = optional(Auth::user())->ua_id ?: "";
        if ($user != $report->create_user) {
            return false;
        }
        $updateFill = [];
        $alreadyFills = $report->infos()->where("report_type", self::TEACHER_FEEDBACK)->get();
        foreach ($alreadyFills as $alreadyFill) {
            $id = Arr::get($alreadyFill->report_info, "relation_id", 0);
            foreach ($teacherFeedBack as $feedback) {
                if ($id == Arr::get($feedback, "id", 0)) {
                    $_tmp = $alreadyFill->report_info;
                    $_tmp["feedback"] = Arr::get($feedback, "feedback", "");
                    $alreadyFill->report_info = $_tmp;
                    $alreadyFill->update_user = $user;
                    $updateFill[] = $alreadyFill;
                }
            }
        }
        DB::beginTransaction();
        if ($title) {
            $report->report_name = $title;
            $report->save();
        }
        if ($supervisor) {
            $find = StudentInterimReportInfoModel::where("report_id", $report->id)->where("report_type", self::SUPERVISOR_FEEDBACK)->first();
            if ($find) {
                $find->report_info = ["feedback" => $supervisor];
                $find->save();
            } else {
                StudentInterimReportInfoModel::create([
                    "report_id" => $report->id,
                    "report_type" => self::SUPERVISOR_FEEDBACK,
                    "create_user" => $user,
                    "update_user" => "",
                    "report_info" => ["feedback" => $supervisor],
                ]);
            }
        }
        if ($updateFill) {
            foreach ($updateFill as $model) {
                $model->save();
            }
        }
        DB::commit();
        return true;
    }

    public function flashReport($id)
    {
        $report = StudentInterimReportModel::find($id);
        if (!$report) {
            return;
        }
        $user = optional(Auth::user())->ua_id ?: "";
        $this->flashReportOfWord($report, $user);
        $this->flashReportOfCas($report, $user);
        $this->flashReportOfLms($report, $user);
    }

    public function flashReportOfLms(StudentInterimReportModel $report, $user = "")
    {
        if (!in_array(self::LMS_HOMEWORK, $report->source) && !in_array(self::LMS_TEST, $report->source)) {
            return;
        }
        $url = env("LMS_URL", "") . "/api/top/phaseReport?uaId=%s&begin=%s&end=%s";
        $url = sprintf($url, $report->student_id, $report->report_start, $report->report_end);
        $result = Common::getSimpleCurl($url);
        $result = json_decode($result, true);
        $data = Arr::get($result, "data", []);
        $insert = [
            "report_id" => $report->id,
            "create_user" => $user,
            "update_user" => "",
            "report_type" => "",
            "report_info" => [],
        ];
        DB::beginTransaction();
        if (in_array(self::LMS_HOMEWORK, $report->source)) {
            $insert["report_type"] = self::LMS_HOMEWORK;
            $insert["report_info"] = Arr::get($data, "homework", new \stdClass());
            StudentInterimReportInfoModel::where("report_id", $report->id)->where("report_type", self::LMS_HOMEWORK)->delete();
            StudentInterimReportInfoModel::create($insert);
        }
        if (in_array(self::LMS_TEST, $report->source)) {
            $insert["report_type"] = self::LMS_TEST;
            $insert["report_info"] = Arr::get($data, "test", []);
            StudentInterimReportInfoModel::where("report_id", $report->id)->where("report_type", self::LMS_TEST)->delete();
            StudentInterimReportInfoModel::create($insert);
        }
        DB::commit();

    }

    public function flashReportOfCas(StudentInterimReportModel $report, $user = "")
    {
        if (!in_array(self::CAS_LESSON, $report->source)) {
            return;
        }
        $sql = "SELECT
    count(*) as lesson_count
FROM
	cas_crm.crm_customer_rights ccr
	JOIN cas_crm.crm_customer_rights_detail ccrd ON ccrd.rightsid = ccr.rightsid
	AND ccrd.`status` = 2 AND ccrd.costtype != 3
	JOIN cas_crm.crm_instance ci ON ci.instanceid = ccr.instanceid
	JOIN cas_crm.crm_product_specification cps ON cps.specificationid = ci.specificationid
	JOIN cas_crm.crm_product_level cpl ON cpl.levelid = cps.levelid
	JOIN cas_crm.crm_product_specification_detail cpsd ON cpsd.specificationid = cps.specificationid
	AND cpsd.pricingfactorid = 2
	JOIN cas_crm.crm_pricingfactor_value cpv ON cpv.valueid = cpsd.valueid
	JOIN cas_crm.cas_lessoninfo cl ON cl.id = ccrd.instanceitemid
	JOIN cas_crm.vtiger_users vu ON vu.id = cl.staff_id
    JOIN cas_crm.xkt_user_ids xui on xui.crmid = vu.id
	JOIN cas_crm.crm_item cit ON cit.itemid = cl.subject_id
	JOIN cas_crm.vtiger_account va ON va.accountid = ccr.customerid
WHERE
	va.ua_id = ?
	AND DATE ( cl.start_time ) >= ?
    AND DATE ( cl.start_time ) <= ?";
        $total = DB::selectOne($sql, [$report->student_id, $report->report_start, $report->report_end]);
        $sql = "SELECT
	ci.`name` AS class_name,
	vu.last_name AS staff_name,
	cit.name_cn AS subject_name
FROM
	cas_crm.crm_customer_rights ccr
	JOIN cas_crm.crm_customer_rights_detail ccrd ON ccrd.rightsid = ccr.rightsid
	AND ccrd.`status` = 2 AND ccrd.costtype != 3
	JOIN cas_crm.crm_instance ci ON ci.instanceid = ccr.instanceid
	JOIN cas_crm.crm_product_specification cps ON cps.specificationid = ci.specificationid
	JOIN cas_crm.crm_product_level cpl ON cpl.levelid = cps.levelid
	JOIN cas_crm.crm_product_specification_detail cpsd ON cpsd.specificationid = cps.specificationid
	AND cpsd.pricingfactorid = 2
	JOIN cas_crm.crm_pricingfactor_value cpv ON cpv.valueid = cpsd.valueid
	JOIN cas_crm.cas_lessoninfo cl ON cl.id = ccrd.instanceitemid
	JOIN cas_crm.vtiger_users vu ON vu.id = cl.staff_id
    JOIN cas_crm.xkt_user_ids xui on xui.crmid = vu.id
	JOIN cas_crm.crm_item cit ON cit.itemid = cl.subject_id
	JOIN cas_crm.vtiger_account va ON va.accountid = ccr.customerid
WHERE
	va.ua_id = ?
	AND DATE ( cl.start_time ) >= ?
    AND DATE ( cl.start_time ) <= ?";
        $list = DB::select($sql, [$report->student_id, $report->report_start, $report->report_end]);
        $list = collect($list)->map(function ($item) {
            return $item->class_name . " " . $item->subject_name . " " . " " . $item->staff_name;
        })->unique()->values()->sort();
        $insert = [
            "report_id" => $report->id,
            "create_user" => $user,
            "update_user" => "",
            "report_type" => self::CAS_LESSON,
            "report_info" => [
                "lesson" => $total ? $total->lesson_count : 0,
                "teacher" => $list->values(),
            ],
        ];
        DB::beginTransaction();
        StudentInterimReportInfoModel::where("report_id", $report->id)->where("report_type", self::CAS_LESSON)->delete();
        StudentInterimReportInfoModel::create($insert);
        DB::commit();;
    }

    public function flashReportOfWord(StudentInterimReportModel $report, $user = "")
    {
        if (!in_array(self::WORD_SUMMARY, $report->source) && !in_array(self::WORD_BOOK, $report->source)) {
            return;
        }
        $student = $report->student;
        $data = [
            "smp_id" => $student->code,
            "start_date" => $report->report_start,
            "end_date" => $report->report_end,
        ];
        $url = env("WORD_API_URL") . "/api/service/student/phase/data";
        $result = Common::getSimpleCurl($url, $data);
        $result = json_decode($result, true);
        $data = Arr::get($result, "data", []) ?: [];
        $insert = [
            "report_id" => $report->id,
            "create_user" => $user,
            "update_user" => "",
            "report_type" => "",
            "report_info" => [],
        ];
        DB::beginTransaction();
        if (in_array(self::WORD_SUMMARY, $report->source)) {
            StudentInterimReportInfoModel::where("report_id", $report->id)->where("report_type", self::WORD_SUMMARY)->delete();
            $insert["report_type"] = self::WORD_SUMMARY;
            $insert["report_info"] = Arr::get($data, "summary", []) ?: [];
            StudentInterimReportInfoModel::create($insert);
        }
        if (in_array(self::WORD_BOOK, $report->source)) {
            StudentInterimReportInfoModel::where("report_id", $report->id)->where("report_type", self::WORD_BOOK)->delete();
            $insert["report_type"] = self::WORD_BOOK;
            $insert["report_info"] = Arr::get($data, "books", []) ?: [];
            StudentInterimReportInfoModel::create($insert);
        }
        DB::commit();
    }

    public function reportFinish($id)
    {
        //报告完成判断
        $report = StudentInterimReportModel::find($id);
        if (!$report) {
            return;
        }
        if ($report->status == 1) {
            return;
        }
        $supervisorFeedback = $report->infos()->where("report_type", self::SUPERVISOR_FEEDBACK)->first();
        $teacher = $report->teacherFeedback()->get();
        $teacherFinish = collect($teacher)->filter(function ($item) {
            return $item->status == 1;
        });
        DB::beginTransaction();
        if ($supervisorFeedback && $teacherFinish->count() == $teacher->count()) {
            $report->status = 1;
            $report->save();
        }
        DB::commit();


    }

    public function deleteReport($id)
    {
        $report = StudentInterimReportModel::find($id);
        $user = optional(Auth::user())->ua_id;
        if (!$report || $user != $report->create_user) {
            return false;
        }
        return $report->delete();
    }

    public function supervisorList($page, $pageNum, $search, $status, $isMp, $cUsers = ""): array
    {
        $page = ((int)$page) <= 0 ? 1 : (int)$page;
        $pageNum = ((int)$pageNum) <= 0 ? 10 : (int)$pageNum;
        $result = [
            "total" => 0,
            "page" => $page,
            "pageNum" => $pageNum,
            "data" => [],
        ];
        $list = StudentInterimReportModel::when($status, function ($q) use ($status) {
            $q->where("status", $status);
        })
            ->when($cUsers, function ($q) use ($cUsers) {
                $q->whereIn("create_user", explode(",", $cUsers));
            })
            ->whereIn("student_id", $this->supervisorStudent($isMp, $search, true))->with("student")->orderBy("created_at", "DESC");
        $total = $list->count();
        if ($total <= 0) {
            return $result;
        }
        $result["total"] = $total;
        $result["data"] = $list->limit($pageNum)->offset(($page - 1) * $pageNum)->get()->toArray();
        foreach ($result["data"] as &$item) {
            $item["student_name"] = Arr::get($item, "student.name", "");
            unset($item["student"]);
        }
        return $result;
    }

    public function supervisorStudent($isMap = false, $search = "", $onlyBuilder = false, $studentUaId = "")
    {
        $uaId = optional(Auth::user())->ua_id ?: "";
        $isMap = $isMap == 'true';
        if (!$isMap) {
            $build = DB::table(DB::raw("lms.lms_crm_guide_student"))
                ->join(DB::raw("lms.lms_user"), DB::raw(("lms_crm_guide_student.student_uaid")), "=", DB::raw("lms_user.ua_id"))
                ->where("teacher_uaid", $uaId)
                ->whereIn("relation", ["Owner", "Server"]);
        } else {
            $teamBuild = DB::table(DB::raw("cas_crm.xkt_user_ids"))->selectRaw("xkt_user_ids.uaid")
                ->join(DB::raw("cas_crm.vtiger_user2team"), DB::raw("vtiger_user2team.userid"), "=", DB::raw("xkt_user_ids.crmid"))
                ->whereIn(DB::raw("vtiger_user2team.teamid"), DB::table(DB::raw("cas_crm.xkt_user_ids"))->selectRaw("teamid")
                    ->join(DB::raw("cas_crm.vtiger_user2team"), DB::raw("vtiger_user2team.userid"), "=", DB::raw("xkt_user_ids.crmid"))->where(DB::raw("xkt_user_ids.uaid"), "=", $uaId));
            $build = DB::table(DB::raw("lms.lms_crm_guide_student"))
                ->join(DB::raw("lms.lms_user"), DB::raw("lms_crm_guide_student.student_uaid"), "=", DB::raw("lms_user.ua_id"))
                ->whereIn(DB::raw("lms_crm_guide_student.teacher_uaid"), $teamBuild)
                ->whereIn("relation", ["Owner", "Server"]);
        }
        $build
            ->when($studentUaId, function ($q) use ($studentUaId) {
                $q->where(DB::raw("lms_user.ua_id"), $studentUaId);
            })
            ->when($search, function ($q) use ($search) {
            $q->where(function ($q) use ($search) {
                $q->where(DB::raw("lms_user.name"), "like", "%$search%");
                $q->orWhere(DB::raw("lms_user.user_code"), "like", "%$search%");
            });

        });
        if ($onlyBuilder) {
            $build->selectRaw("distinct lms_user.ua_id");
            return $build;
        }
        $build->selectRaw("distinct ua_id,`name`");
        return $build->get();
    }

    public function timeOfStudentClassTeacher($uaId, $start, $end, $forCheck = false)
    {
        if (!$uaId || !$start || !$end) {
            return collect();
        }
        $sql = "SELECT
    ccr.rightsid,
	ccr.instanceid,
	ci.`name` AS instance_name,
	ci.`code` AS instance_code,
	vu.last_name AS teacher_name,
	cit.name_cn AS subject_name,
	xui.uaid as ua_id,
	cl.subject_id
FROM
	cas_crm.crm_customer_rights ccr
	JOIN cas_crm.crm_customer_rights_detail ccrd ON ccrd.rightsid = ccr.rightsid
	AND ccrd.`status` = 2 AND ccrd.costtype != 3
	JOIN cas_crm.crm_instance ci ON ci.instanceid = ccr.instanceid
	JOIN cas_crm.crm_product_specification cps ON cps.specificationid = ci.specificationid
	JOIN cas_crm.crm_product_level cpl ON cpl.levelid = cps.levelid
	JOIN cas_crm.crm_product_specification_detail cpsd ON cpsd.specificationid = cps.specificationid
	AND cpsd.pricingfactorid = 2
	JOIN cas_crm.crm_pricingfactor_value cpv ON cpv.valueid = cpsd.valueid
	JOIN cas_crm.cas_lessoninfo cl ON cl.id = ccrd.instanceitemid
	JOIN cas_crm.vtiger_users vu ON vu.id = cl.staff_id
    JOIN cas_crm.xkt_user_ids xui on xui.crmid = vu.id
	JOIN cas_crm.crm_item cit ON cit.itemid = cl.subject_id
	JOIN cas_crm.vtiger_account va ON va.accountid = ccr.customerid
WHERE
	va.ua_id = ?
	AND DATE ( cl.start_time ) >= ?
    AND DATE ( cl.start_time ) <= ?
GROUP BY
	ccr.instanceid,
	cl.staff_id,
	cl.subject_id";

        $result = [];
        $record = collect(DB::select($sql, [$uaId, $start, $end]));
        if ($forCheck) {
            return $record;
        }
        $record = $record->groupBy("instanceid");
        foreach ($record as $key => $item) {
            if (!isset($result[$key])) {
                $result[$key] = [
                    "key" => $key,
                    "value" => optional($item->first())->instance_name,
                    "children" => []
                ];
            }
            $item = $item->groupBy("subject_name");

            foreach ($item as $subject => $teachers) {
                $tmp = [
                    "key" => $subject,
                    "value" => $subject,
                    "children" => []
                ];
                $teachers = collect($teachers)->unique("teacher_name");
                foreach ($teachers as $teacher) {
                    $_key = md5($teacher->ua_id . $teacher->instanceid . $teacher->subject_id);
                    $tmp["children"][] = [
                        "key" => $_key,
                        "value" => $teacher->teacher_name,
                        "children" => [],
                    ];
                }
                $result[$key]["children"][] = $tmp;
            }
        }
        return array_values($result);
    }


    public function reportInfo($id, $isSupervisor, $refresh = false)
    {
        $isSupervisor = $isSupervisor === 'true';
        $refresh = $refresh === 'true';
        $report = StudentInterimReportModel::find($id);
        if (!$report) {
            return new \stdClass();
        }
        //拿关系
        $report->infos;
        $report->teacherFeedback;
        //优化一下区块 方便前端调用
        $result = [
            "reportId" => $report->id,
            "reportName" => $report->report_name,
            "createUser" => $report->create_user,
            "time" => $report->report_start . "~" . $report->report_end,
            "status" => $report->status,
            "common" => "",
            "supervisorFeedback" => "",
            "teacherFeedback" => [],
            "others" => new \stdClass(),
        ];
        $user = optional(Auth::user())->ua_id;

        return $isSupervisor ? $this->reportInfoOfSupervisor($report, $result, $user, $refresh) : $this->reportInfoOfTeacher($report, $result, $user);
    }

    public function reportInfoOfTeacher(StudentInterimReportModel $report, $result = [], $user = "")
    {
        $targetTeacher = collect($report->teacherFeedback)->filter(function ($item) use ($user) {
            return $user && $user == $item->teacher_id;
        });
        $finish = !($targetTeacher->count() == 0) && $targetTeacher->filter(function ($item) {
                return $item->status == 0;
            })->count() == 0;
        $result["status"] = $finish ? 1 : 0;
        $infos = collect($report->infos)->filter(function ($item) use ($user) {
            return $user && $user == $item->create_user && $item->report_type == StudentInterimReportService::TEACHER_FEEDBACK;
        });
        $result["teacherFeedback"] = $this->_convertTeacherFeedBack($targetTeacher, $infos);

        if ($report->create_user) {
           $_user = DB::selectOne("select name from top.v_crm_user where ua_id = ? ", [$report->create_user]);
           if ($_user) {
               $result["common"] = $_user->name;
           }
        }
        return $result;
    }

    private function _convertTeacherFeedBack(Collection $feedbackTeachers, Collection $infos): array
    {
        $teacherFeedbackConvert = [];
        foreach ($feedbackTeachers as $feedbackTeacher) {
            $tmp = $feedbackTeacher->toArray();
            $tmp["feedback"] = [];
            foreach ($infos as $info) {
                $reportInfo = $info->report_info;
                $relationId = Arr::get($reportInfo, "relation_id", 0);
                if ($relationId == $feedbackTeacher->id) {
                    $tmp["feedback"] = Arr::get($reportInfo, "feedback", []);
                    break;
                }
            }
            $teacherFeedbackConvert[] = $tmp;
        }
        return $teacherFeedbackConvert;
    }

    public function reportInfoOfSupervisor(StudentInterimReportModel $report, $result = [], $user = "", $refresh = false)
    {
        if ($refresh) {
            $this->flashReport($report->id);
            //重新拿数据
            $report->infos = $report->infos()->get();
            $report->teacherFeedback = $report->teacherFeedback()->get();
        }

        $teacherFeedbackInfos = collect($report->infos)->filter(function ($item) use ($user) {
            return $item->report_type == StudentInterimReportService::TEACHER_FEEDBACK;
        });
        $supervisorFeedbackInfos = collect($report->infos)->filter(function ($item) use ($user) {
            return $item->report_type == StudentInterimReportService::SUPERVISOR_FEEDBACK;
        })->first();
        if ($supervisorFeedbackInfos) {
            $result["supervisorFeedback"] = Arr::get($supervisorFeedbackInfos->report_info, "feedback", "");
        }
        $result["common"] = collect($report->teacherFeedback)->pluck("relation")->implode("、 ");
        $result["teacherFeedback"] = $this->_convertTeacherFeedBack(collect($report->teacherFeedback), $teacherFeedbackInfos);
        $source = $report->source;
        $others = [];
        if ($source) {
            foreach (collect($source) as $item) {
                $others[$item] = null;
                foreach (collect($report->infos) as $info) {
                    if ($item == $info->report_type) {
                        $others[$item] = $info->report_info;
                    }
                }
            }
        }
        $result["others"] = $others ?: $result["others"];
        return $result;
    }

    public function teacherList($page, $pageNum, $search, $status)
    {
        $page = ((int)$page) <= 0 ? 1 : (int)$page;
        $pageNum = ((int)$pageNum) <= 0 ? 10 : (int)$pageNum;
        $result = [
            "total" => 0,
            "page" => $page,
            "pageNum" => $pageNum,
            "data" => [],
        ];
        $user = optional(Auth::user())->ua_id;
        $list = StudentInterimReportModel::whereHas("teacherFeedback", function ($q) use ($user, $status) {
            $q->where("teacher_id", $user);
            $q->when($status, function ($q) use ($status) {
                $q->where("status", $status);
            });
        })
            ->when($search, function (Builder $q) use ($search) {
                $q->whereHas("student", function ($q) use ($search) {
                    $q->where("name", "LIKE", "%$search%");
                    $q->orWhere("code", "LIKE", "%$search%");
                });
            });
        $total = $list->count();
        if ($total == 0) {
            return $result;
        }
           $list = $list->limit($pageNum)->offset(($page - 1) * $pageNum)->orderBy("created_at", "DESC")->with(["student", "teacherFeedback" => function ($q) use ($user) {
                $q->where("teacher_id", $user);
            }])->get();
        foreach ($list as $item) {
            $find = collect($item->teacherFeedback)->filter(function ($item) {
                return $item->status == 0;
            });
            $item->status = 1;
            if ($find->count() > 0) {
                $item->status = 0;
            }
            $item->student_name = $item->student->name;
            unset($item->student, $item->teacherFeedback, $item->source);
        }
        $result["total"] = $total;
        $result["data"] = $list->toArray();
        return $result;
    }

    public function createUser($isMp = false)
    {
        $list = StudentInterimReportModel::select(["create_user"])->whereIn("student_id", $this->supervisorStudent($isMp, "", true))->groupBy("create_user")->get();
        return $list->pluck("create_user");

    }
}
