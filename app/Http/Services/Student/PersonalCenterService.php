<?php

namespace App\Http\Services\Student;

use App\Http\Services\Evaluate\EvaluateService;
use App\Http\Services\Point\PointService;
use App\Http\Services\Survey\SurveyService;
use App\Models\Student\StudentInfoModel;
use App\Models\Student\StudentResourceModel;
use App\Utils\Common;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PersonalCenterService
{
    /**
     * 学员默认配置
     * @var int[]
     */
    static $defaultValues = [
        "avatar_0" => 123,
        "avatar_1" => 321,
        "skin" => 0,
    ];

    public function __construct()
    {
        self::$defaultValues["avatar_0"] = env("DEFAULT_AVATAR_1");
        self::$defaultValues["avatar_1"] = env("DEFAULT_AVATAR_2");
        self::$defaultValues["skin"] = env("DEFAULT_SKIN");
    }


    /**
     * 学习规划列表
     * @param $cookie
     * @param $page
     * @param $pageLimit
     * @param $sort
     * @param $sortOrder
     * @return array|string
     */
    public function learningPlanList($cookie, $page = 1, $pageLimit = 20, $sort = "modifiedtime", $sortOrder = 'DESC')
    {
        $url = env("CRM_URL", "https://crm-kf.thinktown.com:8443/index.php") . "?module=Api&action=Student&mode=getLearningPlan";

        $data = [
            "page" => $page,
            "pageLimit" => $pageLimit,
            "sort" => $sort,
            "sortOrder" => $sortOrder,
        ];
        return Common::postSimpleCurl($url, $data, ["cookie" => $cookie]);
    }

    /**
     * 学习规划详情
     * @param $cookie
     * @param $planId
     * @return array|string
     */
    public function learningPlanDetail($cookie, $planId)
    {
        $url = env("CRM_URL", "https://crm-kf.thinktown.com:8443/index.php") . "?module=Api&action=Student&mode=previewLearningPlan";

        $data = [
            "plan_id" => $planId,
        ];
        return Common::postSimpleCurl($url, $data, ["cookie" => $cookie]);
    }

    /**
     * 获取当前学员详情，如果没有则新建
     * @return StudentInfoModel
     */
    public function studentDetail($withLevel = false)
    {
        $user = Auth::user();
        $ua_id = $user->ua_id;
        $studentInfo = StudentInfoModel::where('uaid', $ua_id)->first();
        if ($studentInfo == null) {
            if ($user->user_type_id != 2) return null;
            $studentInfo = new StudentInfoModel();
            $studentInfo->uaid = $ua_id;
            $studentInfo->code = $user->user_code;
            $studentInfo->gender = $user->gender;
            $studentInfo->level = 1;
            $studentInfo->name = $user->name;
            $studentInfo->nickname = $user->name;
            $studentInfo->avater = self::$defaultValues["avatar_" . $user->gender];
            $studentInfo->skin = self::$defaultValues["skin"];
            $studentInfo->created_at = $user->created_at;
            $studentInfo->save();
        }
        $studentInfo = $this->initialStudent($ua_id, $studentInfo);
        //后续需要增加头像等图片的转换操作，带上链接
        $studentInfo->avaterInfo()->skinInfo()->studentLevel($withLevel);

        $studentInfo->if_survey = false;
        $evaluate = new EvaluateService();
        $ifEvaluate = $evaluate->ifTeacherEval($ua_id);
        $showEvaluate = $evaluate->ifShowTeacherEvalButton($ua_id);
        $studentInfo->if_evaluate = $ifEvaluate;
        $studentInfo->show_evaluate = $showEvaluate;
        $studentInfo->show_evaluate_tips = $showEvaluate && $evaluate->showEvaluateTips($ua_id);
        if (!$ifEvaluate) {
            //判断是否需要给学生推送调研
            $surveyService = new SurveyService();
            $studentInfo->if_survey = $surveyService->ifSurvey($ua_id);
        }

        return $studentInfo;
    }


    /**
     * 修改目标
     * @param $newTarget
     * @return mixed
     */
    public function changeTarget($newTarget)
    {
        //文字类审核目前不做异步处理，不需加入审核通过后的处理
        $uaId = Auth::user()->ua_id;
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        $studentInfo->target = $newTarget;
        $studentInfo->save();
        return $studentInfo;
    }

    /**
     * 修改目标/回调调用
     * @param $notifyData
     * @param $retData
     * @return mixed
     */
    public static function changeTargetAsynchronous($notifyData, $retData)
    {
        $target = $notifyData['target'];
        $uaId = $notifyData['uaId'];
        if ($retData[0]["pass"]) {
            $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
            $studentInfo->target = $target;
            $studentInfo->save();
            return $studentInfo;
        }
        return null;
    }

    /**
     * 更新目标学员的等级
     * @param $uaId
     * @return mixed
     */
    public function changeLevel($uaId)
    {
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        if (!$studentInfo) return $studentInfo;
        $historyPoint = $studentInfo->history_point;
        $grade = DB::table("grade")->where([
            ['lower_score', '<=', $historyPoint],
            ['upper_score', '>=', $historyPoint],
        ])->first();
        if (!$grade) return $studentInfo;
        $studentInfo->level = $grade->id;
        $studentInfo->save();
        return $studentInfo;
    }

    /**
     * 更换背景
     * @param $newSkin
     * @param null $uaId
     * @return mixed
     */
    public function changeSkin($newSkin, $uaId)
    {
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        $studentInfo->skin = $newSkin;
        $studentInfo->save();
        return $studentInfo;
    }


    /**
     * 更换头像框
     * @param $newFrame
     * @param $uaId
     * @return mixed
     */
    public function changeAvaterFrame($newFrame, $uaId)
    {
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        $studentInfo->avater_frame = $newFrame;
        $studentInfo->save();
        return $studentInfo;
    }


    /**
     * 头像生效
     * @param $uaId
     * @param $resourceId
     * @param bool $rollback
     * @return mixed
     */
    public function applyAvater($uaId, $resourceId, $rollback = false)
    {
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        //如果不存在资源id，则初始化为默认头像
        if (!$resourceId) $resourceId = self::$defaultValues["avatar_" . $studentInfo->gender];
        //如果传入回滚要求，则回滚头像
        if ($rollback) $resourceId = $studentInfo->avater;
        $studentInfo->avater = $resourceId;
        $studentInfo->avater_checking = null;
        $studentInfo->save();
        return $studentInfo;
    }

    /**
     * 记录审核中的头像
     * @param $uaId
     * @param $resourceId
     * @return mixed
     */
    public function saveCheckingAvater($uaId, $resourceId)
    {
        $resourceService = new ResourceService();
        $resource = $resourceService->newResource($uaId, $resourceId, StudentResourceModel::TYPE_AVATER);
        if ($resource->id > 0) {
            $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
            $studentInfo->avater_checking = $resourceId;
            $studentInfo->save();
            return $studentInfo;
        } else {
            return null;
        }

    }

    /**
     * 审核通过的昵称生效/主动调用
     * @param $uaId
     * @param $nickName
     * @return mixed
     */
    public function applyNickname($uaId, $nickName)
    {
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        $studentInfo->nickname = $nickName;
        $studentInfo->nickname_checking = "";
        $studentInfo->save();
        return $studentInfo;
    }


    /**
     * 审核通过的昵称生效/回调调用
     * @param $notifyData
     * @param $retData
     * @return null
     */
    public static function applyNicknameAsynchronous($notifyData, $retData)
    {
        $nickName = $notifyData['nickName'];
        $uaId = $notifyData['uaId'];
        $resourceService = new ResourceService();
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        $checkId = $retData[0]["source"]["result"]["response"]["dataId"] ?? "";
        if ($retData[0]["pass"]) {
            //加入昵称的变换记录，以后万一要用昵称历史就能用了
            StudentResourceModel::where('uaid', $uaId)->where('type', StudentResourceModel::TYPE_NICKNAME)->update(['status' => StudentResourceModel::STATUS_INACTIVE]);
            $resource = $resourceService->newResource($uaId, 0, StudentResourceModel::TYPE_NICKNAME, $checkId, ["nickname" => $nickName], StudentResourceModel::STATUS_ACTIVE);
            $studentInfo->nickname = $nickName;
            $studentInfo->nickname_checking = "";
            $studentInfo->nickname_change += 1;
            $studentInfo->save();
            return $studentInfo;
        } else {
            //登记审核不通过记录
            //回滚积分
            $pointService = new PointService();
            if ($studentInfo->nickname_change > 0) {
                $pointService->changeNickname($uaId, 2);
            } else {
                $pointService->changeNickname($uaId, 2, 0);
            }
            $resource = $resourceService->newResource($uaId, 0, StudentResourceModel::TYPE_NICKNAME, $checkId, ["nickname" => $nickName], StudentResourceModel::STATUS_CHECKING_FAILED);
            //清除待审核昵称
            $studentInfo->nickname_checking = "";
            $studentInfo->save();
        }
        return null;
    }


    /**
     * 获取学员相关的cas下拉框数据
     * @return array[]
     */
    public function getCasPickLists()
    {
        $uaId = Auth::user()->ua_id;
        //获取crmid
        $crmInfo = DB::select(DB::raw("select va.accountid from cas_crm.vtiger_account va where va.ua_id = ?"), [$uaId]);
        foreach ($crmInfo as $item) {
            $crmId = $item->accountid;
        }
        $classList = [];
        $teacherList = [];
        if (!$crmId) return ["classList" => $classList, "teacherList" => $teacherList];

        //获取学员相关班级信息
        $url = env("CAS_URL", "https://cas-kf.thinktown.com:8443") . "/api/studentcenterweb/getClassesByStudentId";
        $classResult = Common::postSimpleCurlByRawData($url, ["studentId" => $crmId,], ['Content-Type' => 'application/json']);
        $classResultObj = json_decode($classResult);
        if ($classResultObj) {
            if ($classResultObj->code == 1) {
                $classList = $classResultObj->result;
            }
        }

        //获取学员相关教师信息
        $url = env("CAS_URL", "https://cas-kf.thinktown.com:8443") . "/api/studentcenterweb/teacherOfStudent";
        $teacherResult = Common::getSimpleCurl($url, ["studentId" => $crmId,]);
        $teacherResultObj = json_decode($teacherResult);
        if ($teacherResultObj) {
            if ($teacherResultObj->code == 1) {
                $teacherList = $teacherResultObj->result;
            }
        }

        return ["classList" => $classList, "teacherList" => $teacherList];
    }

    /**
     * 获取cas课表信息
     * @param $params
     * @return array|null
     */
    public function getCasLessons($params)
    {
        $uaId = Auth::user()->ua_id;
        //获取crmid
        $crmInfo = DB::select(DB::raw("select va.accountid from cas_crm.vtiger_account va where va.ua_id = ?"), [$uaId]);
        foreach ($crmInfo as $item) {
            $crmId = $item->accountid;
        }
        $lessonList = null;
        if (!$crmId) return $lessonList;

        //获取学员相关班级信息
        $url = env("CAS_URL", "https://cas-kf.thinktown.com:8443") . "/api/studentcenterweb/studentTimetable";
        $params ["studentId"] = $crmId;
        $result = Common::postSimpleCurlByRawData($url, $params, ['Content-Type' => 'application/json']);
        $resultObj = json_decode($result);
        if ($resultObj) {
            if ($resultObj->code == 1) {
                foreach ($resultObj->result as $item) {
                    $lessonList[] = $item;
                }
            }
        }
        return $lessonList;
    }


    /**
     * 初始化无头像无皮肤的学员
     * @param $uaId
     * @param StudentInfoModel|null $studentInfo
     * @return StudentInfoModel|null
     */
    public function initialStudent($uaId, StudentInfoModel $studentInfo = null)
    {
        if ($studentInfo == null) {
            $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        }
        $updateFlag = false;
        if ($studentInfo == null) return $studentInfo;
        //塞默认头像
        if ($studentInfo->avater == 0 || is_null($studentInfo->avater)) {
            $studentInfo->avater = self::$defaultValues["avatar_" . $studentInfo->gender];
            $updateFlag = true;
        }
        //塞默认皮肤
        if ($studentInfo->skin == 0 || is_null($studentInfo->skin)) {
            $studentInfo->skin = self::$defaultValues["skin"];
            $updateFlag = true;
        }
        if ($updateFlag) $studentInfo->update();
        return $studentInfo;
    }
}
