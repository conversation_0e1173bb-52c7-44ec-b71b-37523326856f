<?php

namespace App\Http\Services\Student;

use App\ConstDir\Cache\CacheConst;
use App\ConstDir\File\FileConst;
use App\Http\Services\ExchangeStore\ExchangeStoreService;
use App\Http\Services\File\FileService;
use App\Http\Services\Message\StuMessageService;
use App\Http\Services\Point\PointService;
use App\Models\ExchangeStore\ExchangeStoreModel;
use App\Models\Message\StuMessageModel;
use App\Models\Student\StudentInfoModel;
use App\Models\Student\StudentResourceModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ResourceService
{
    const SYS_RESOURCE_TYPE = [
        StudentResourceModel::TYPE_AVATER_FRAME => 1,
        StudentResourceModel::TYPE_SKIN => 2,
    ];

    /**
     * 获取学员所拥有的资源
     * @param $uaId
     * @param $resourceType
     * @return mixed
     */
    public function getResources($uaId, $resourceType = 0)
    {
        //获取用户信息判断资源使用情况
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        switch ($resourceType) {
            case StudentResourceModel::TYPE_AVATER:
                $usingResourceId = $studentInfo->avater;
                break;
            case StudentResourceModel::TYPE_AVATER_FRAME:
                $usingResourceId = $studentInfo->avater_frame;
                break;
            case StudentResourceModel::TYPE_SKIN:
                $usingResourceId = $studentInfo->skin;
                break;
            default:
                $usingResourceId = 0;
        }

        //获取当前用户有的资源
        $resourceQuery = StudentResourceModel::where([
            ["uaid", '=', $uaId, 'or'],
            ["uaid", '=', '0', 'or'],
        ])->where("status", "<>", 0);
        if ($resourceType > 0) $resourceQuery->where("type", $resourceType);
        //非系统兑换类的，返回附带文件信息
        if (!in_array($resourceType, array_keys(self::SYS_RESOURCE_TYPE))) {
            $resourceQuery->with("file");
        }
        $resources = $resourceQuery->get();

        $res = [];
        //系统兑换类的资源循环拼接构建汇总
        if (in_array($resourceType, array_keys(self::SYS_RESOURCE_TYPE))) {
            //获取全局配置
            $exchangeStoreService = new ExchangeStoreService();
            $systemList = $exchangeStoreService->exchangeList(self::SYS_RESOURCE_TYPE[$resourceType]);//里面的排序不管
            foreach ($systemList as $key => $value) {
                $$key = [];//初始化，不然会出引用错误
                foreach ($value as $item) {
                    //循环标记用户是否拥有该资源
                    $having = false;
                    foreach ($resources as $resource) {
                        if ($resource->resource_id == $item->id) {
                            $having = true;
                            break;
                        }
                    }
                    $item->user_have = $having;
                    //标记用户是否正在使用该资源
                    $item->user_using = $item->id == $usingResourceId;
                    //当前用户没有且已经下架的资源舍弃
                    if (!($item->status == 0 && !$having)) $$key[] = $item;
                }
                $res[$key] = $$key;
            }
        } else {
            $res = $resources;
        }
        return $res;
    }


    /**
     * 关联新的资源
     * @param $uaId
     * @param $resourceId
     * @param $resourceType
     * @param string $check_id
     * @param array $ext
     * @param int $resourceStatus
     * @param null $startTime
     * @param string $endTime
     * @return mixed
     */
    public function newResource($uaId, $resourceId, $resourceType, $check_id = "", $ext = [], $resourceStatus = StudentResourceModel::STATUS_CHECKING, $startTime = null, $endTime = StudentResourceModel::ENDTIME)
    {
        if (!$startTime) $startTime = date("Y-m-d H:i:s");
        $create = [
            'uaid' => $uaId,
            'type' => $resourceType,
            'resource_id' => $resourceId,
            'status' => $resourceStatus,
            'ext' => $ext ?? [],
            'start_time' => $startTime,
            'end_time' => $endTime,
            "check_id" => $check_id,
        ];
        return StudentResourceModel::create($create);
    }

    /**
     * 资源审核后动作
     * @param $notifyData
     * @param $retData
     * @return mixed|null
     */
    public static function getResourceChecked($notifyData, $retData)
    {
        $studentInfo = null;
        $resourceId = $notifyData['resourceId'];
        $uaId = $notifyData['uaId'];
        $resourceType = $notifyData['resourceType'];
        $resource = StudentResourceModel::where([
            ['uaid', $uaId],
            ["resource_id", $resourceId],
            ["type", $resourceType]
        ])->first();

        //如果没找到对应资源记录，直接不干了
        if (!$resource) return $studentInfo;
        $personalCenterService = new PersonalCenterService();
        $fileService = new FileService();
        $checkId = $retData[0]["source"]["result"]["response"]["dataId"] ?? "";
        $resource->check_id = $checkId;
        if ($retData[0]['pass']) {
            $resource->status = StudentResourceModel::STATUS_ACTIVE;
            $resource->save();
            switch ($resourceType) {
                case StudentResourceModel::TYPE_AVATER:
                    $studentInfo = $personalCenterService->applyAvater($uaId, $resourceId);
                    $fileService->updateCheckStatus($resourceId, FileConst::CHECK_STATUS_SUCCESS);
                    StuMessageService::makeUserMsg(StuMessageModel::FROMS_8, "你上传的新头像已审核通过", '', $uaId);
                    break;
            }
        } else {
            //审核不通过
            $resource->status = StudentResourceModel::STATUS_CHECKING_FAILED;
            $resource->save();
            switch ($resourceType) {
                case StudentResourceModel::TYPE_AVATER:
                    $studentInfo = $personalCenterService->applyAvater($uaId, $resourceId, true);
                    $fileService->updateCheckStatus($resourceId, FileConst::CHECK_STATUS_FAILED);
                    StuMessageService::makeUserMsg(StuMessageModel::FROMS_9, "你上传的新头像未审核通过，请重新上传头像", '', $uaId);
                    break;
            }
        }

        return $studentInfo;
    }


    /**
     * 获取学员单个资源信息
     * @param $uaId
     * @param $resourceId
     * @param $resourceType
     * @return mixed
     */
    public function getActiveSingleResource($uaId, $resourceId, $resourceType)
    {
        return StudentResourceModel::where([
                ["uaid", '=', $uaId, 'or'],
                ["uaid", '=', '0', 'or'],
            ]
        )
            ->where([
                ["status", StudentResourceModel::STATUS_ACTIVE],
                ["resource_id", $resourceId],
                ["type", $resourceType]
            ])->first();
    }

    /**
     * @param $uaId
     * @param $resourceType
     * @return Collection
     */
    public function resourceHistory($uaId, $resourceType)
    {
        $list = (new PointService())->list($uaId, self::SYS_RESOURCE_TYPE[$resourceType], 1, 100000);
        foreach ($list as $item) {
            if ($item->ext) $item->ext = json_decode($item->ext);
        }
        return $list;
    }


    /**
     * 默认皮肤资源获取
     * @return mixed
     */
    public static function defaultSkin()
    {
        $defaultSkin = (int) env("DEFAULT_SKIN", 0);
        return Cache::rememberForever(CacheConst::RESOURCE_OF_DEFAULT_SKIN, function () use ($defaultSkin) {
            return ExchangeStoreModel::query()->find($defaultSkin, ['ext']);
        });
    }

    /**
     * 获取默认资源路径
     * @param $path
     * @return mixed
     */
    public static function defaultSkinOfTargetPath($path)
    {
        $skin = self::defaultSkin();
        if (!$skin) {
            return null;
        }
        return Arr::get(collect($skin->ext), $path);
    }


}
