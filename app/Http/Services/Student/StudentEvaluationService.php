<?php

namespace App\Http\Services\Student;

use App\Models\Student\StudentEvaluationConfigModel;
use App\Models\Student\StudentEvaluationModel;
use Illuminate\Support\Facades\Auth;

class StudentEvaluationService
{
    public function config()
    {
        return StudentEvaluationConfigModel::where("status", 1)->orderBy("sort", "desc")->get();
    }

    public function submitEvaluation($data = [])
    {
        /**
         * @var $configId
         * @var $content
         */
        extract($data);
        $model = new StudentEvaluationModel();
        $model->config_id = $configId;
        $model->content = $content;
        $model->ua_id = optional(Auth::user())->ua_id ?? "";
        return $model->save();
    }
}
