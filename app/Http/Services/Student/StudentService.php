<?php

namespace App\Http\Services\Student;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * 学生服务
 */
class StudentService
{
    /*
     * 话题下的用户详情
     * @param array $uaId
     * @param string $loginUaId
     */
    public function studentInfoFromTalk($uaId = [], $loginUaId = ""): Collection
    {
        //逻辑上还是要查询两次，第一次是非我 第二次是本我

        $uaIdIsMine = collect($uaId)->filter(function ($item) use ($loginUaId) {
            return $item == $loginUaId;
        })->unique();
        $uaIdNotMine = collect($uaId)->filter(function ($item) use ($loginUaId) {
            return $item != $loginUaId;
        })->unique();

        $defaultImageBanner = ResourceService::defaultSkinOfTargetPath("bannerImg.path");

        $medalSelectSql = ",(SELECT ((SELECT count(*) AS total FROM sc_user_medal aa JOIN sc_medal bb ON aa.medal_id=bb.id JOIN sc_medal_detail cc ON cc.medal_id=bb.id JOIN sc_user_medal_log dd ON dd.medal_detail_id=cc.id WHERE aa.user_id=a.uaid AND dd.user_id=a.uaid AND bb.type=1)+(SELECT SUM(aa.progress) AS total FROM sc_user_medal aa JOIN sc_medal bb ON aa.medal_id=bb.id WHERE aa.user_id=a.uaid AND bb.type=2))) AS medal_num";
        $skinSelectSql = ",a.skin,(IF (a.skin=0,'$defaultImageBanner',(SELECT JSON_UNQUOTE(json_extract(ext,'$.bannerImg.path')) avatar_hover_image FROM `sc_exchange_store` WHERE id=a.skin))) avatar_hover_image";
        $list = collect();
        if ($uaIdNotMine->count() > 0) {
            $list = DB::table(DB::raw("sc_student_info as a"))->whereIn(DB::raw("a.uaid"), $uaIdNotMine)->selectRaw("a.uaid, 0 isMe,a.nickname,'' nickname_checking,0 nickname_ischecking, a.avater,(SELECT sf.file_path from sc_file sf where sf.id=a.avater) avater_url,0 avater_checking,'' avater_checking_url,0 avater_ischecking,avater_frame,(SELECT JSON_UNQUOTE(json_extract(ext,'$.path')) FROM `sc_exchange_store` where id=a.avater_frame) avater_frame_url,level,(SELECT sf.file_path FROM `sc_grade` sg,sc_file sf where sg.id=a.level and sg.resource_id=sf.id) level_url" . $medalSelectSql . $skinSelectSql)->get();
        }
        $list2 = collect();
        if ($uaIdIsMine->count() > 0) {
            $list2 = DB::table(DB::raw("sc_student_info as a"))->whereIn(DB::raw("a.uaid"), $uaIdIsMine)->selectRaw("a.uaid, 1 isMe,a.nickname,a.nickname_checking,(SELECT (CASE WHEN COUNT(1)>0 THEN 1 ELSE 0 END ) FROM `sc_student_resource` b where b.type=4 and b.`status`=2 and b.uaid=a.uaid) nickname_ischecking, a.avater,(SELECT sf.file_path from sc_file sf where sf.id=a.avater) avater_url,a.avater_checking,(SELECT sf.file_path from sc_file sf where sf.id=a.avater_checking) avater_checking_url,(SELECT (CASE WHEN COUNT(1)>0 THEN 1 ELSE 0 END ) FROM `sc_student_resource` b where b.type=1 and b.`status`=2 and b.uaid=a.uaid) avater_ischecking,avater_frame,(SELECT JSON_UNQUOTE(json_extract(ext,'$.path')) FROM `sc_exchange_store` where id=a.avater_frame) avater_frame_url,level,(SELECT sf.file_path FROM `sc_grade` sg,sc_file sf where sg.id=a.level and sg.resource_id=sf.id) level_url" . $medalSelectSql . $skinSelectSql)->get();
        }
        return $list->merge($list2);
    }
}
