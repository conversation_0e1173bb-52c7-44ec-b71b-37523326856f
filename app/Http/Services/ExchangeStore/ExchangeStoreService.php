<?php

namespace App\Http\Services\ExchangeStore;

use App\Models\ExchangeStore\ExchangeStoreModel;

class ExchangeStoreService
{

    const TAG_DURATION_DAYS = 30;

    public function exchangeList($type)
    {
        $res = [
            'new' => [],
            'old' => []
        ];
        $exchangeStores = ExchangeStoreModel::where('type', $type)->with('file')->orderBy('sort', 'desc')->get();
        foreach ($exchangeStores as $exchange) {
            if (($exchange['ext']['isNew'] ?? false) && $exchange['status'] == 1) {
                //30天内
                $res['new'][] = $exchange;
            } else {
                $res['old'][] = $exchange;
            }
        }
        return $res;
    }

}
