<?php

namespace App\Http\Services\Point;

use App\ConstDir\Medal\MedalConst;
use App\Events\Medal\MedalEvent;
use App\Http\Services\Medal\MedalService;
use App\Http\Services\Message\StuMessageService;
use App\Http\Services\Student\PersonalCenterService;
use App\Http\Services\Talk\TalkService;
use App\Models\Message\StuMessageModel;
use App\Utils\DateUtil;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;

class PointService
{
    const LOGIN_POINT = 1;
    const COMMENT_POINT = 2;
    const DAILY_PLAN_POINT = 3;
    const HOMEWORK_POINT = 4;
    const WORD_POINT = 5;
    const MEDAL_POINT = 6;
    const RANKING_POINT = 7;
    const EXCHANGE_POINT = 8;
    const SURVEY_POINT = 9;
    const TEACHER_EVALUATE_POINT = 10;

    const POINT_INCREASE = 1;
    const POINT_DECREASE = 2;
    const POINT_RETURN = 3;

    const POINT_TYPE_1 = '登录积分';
    const POINT_TYPE_2 = '评论积分';
    const POINT_TYPE_3 = '计划积分';
    const POINT_TYPE_4 = '作业积分';
    const POINT_TYPE_5 = '单词积分';
    const POINT_TYPE_6 = '勋章积分';
    const POINT_TYPE_7 = '排行榜积分';
    const POINT_TYPE_8 = '积分消耗';
    const POINT_TYPE_9 = '评价积分';

    //排行榜名次对应积分
    private $rankingPoint = [0, 30, 20, 10, 5, 5, 5, 5, 5, 5, 5, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3];

    /**
     * 兑换记录
     * @param $userId
     * @param $type 1头像挂件；2背景皮肤
     * @param $page
     * @param $pageSize
     * @return Collection
     */
    public function list($userId, $type, $page = 1, $pageSize = 20)
    {
//        $reason = $type == 1 ? self::REASON_AVATAR_PENDANT : self::REASON_BACKGROUND_SKIN;
        $offset = ($page - 1) * $pageSize;
        $res = DB::table('point_record')
            ->select('point_record.id', 'point_record.point', 'point_record.type', 'point_record.created_at', 'exchange_store.title', 'exchange_store.ext')
            ->join('exchange_store', 'point_record.resource_id', '=', 'exchange_store.id')
//            ->join('file', 'exchange_store.resource_id', '=', 'file.id')
            ->where([
                'point_record.user_id' => $userId,
//                'point_record.reason' => $reason,
                'point_record.type' => self::EXCHANGE_POINT,
                'exchange_store.type' => $type
            ])
            ->offset($offset)
            ->limit($pageSize)
            ->orderBy('point_record.created_at', 'desc')
            ->get();
        return $res;
    }

    /**
     * 学员总积分
     * @param int $userId 学员id
     * @return int
     */
    public function getStudentPoint($userId)
    {
        return DB::table('student_info')->where('uaid', '=', $userId)->value('point');
    }

    /**
     * 更新学员积分
     * @param $userId
     * @param $point
     */
    public function updateStudentPoint($userId, $point)
    {
        DB::table('student_info')
            ->where('uaid', $userId)
            ->update(['point' => $point]);
    }

    /**
     * 增加学员积分
     * @param $userId
     * @param $point
     * @return void
     */
    public function increaseStudentPoint($userId, $point, $eventTrigger = true, $isUpdateRanking = true)
    {
        DB::table('student_info')
            ->where('uaid', $userId)
            ->update(['point' => DB::raw('point + ' . $point), 'history_point' => DB::raw('history_point + ' . $point)]);
        if ($isUpdateRanking) {
            DB::table('student_ranking')
                ->updateOrInsert(['user_id' => $userId], ['week_point' => DB::raw('week_point + ' . $point), 'month_point' => DB::raw('month_point + ' . $point), 'total_point' => DB::raw('total_point + ' . $point)]);
        } else {
            DB::table('student_ranking')
                ->updateOrInsert(['user_id' => $userId], ['total_point' => DB::raw('total_point + ' . $point)]);
        }
        //触发事件
        if ($eventTrigger) {
            //触发积分名人堂事件
            Event::dispatch(new MedalEvent(MedalConst::POINT_HALL_OF_FAME));
        }
    }

    /**
     * 更新榜单
     * @param $userId
     * @param $recordTime '记录时间'
     * @param int $type 1月作业数,2月抽查单词数,3月度模考高分榜（托福）
     * @param int $value 增加或更新的数值
     * @return void
     */
    public function updateStudentRanking($userId, $recordTime, int $type = 1, int $value = 1)
    {
        $thisMonth = DateUtil::getBeginOfMonth();
        if (strtotime($recordTime) > strtotime($thisMonth)) {
            if ($type == 1) {
                DB::table('student_ranking')
                    ->updateOrInsert(['user_id' => $userId], ['month_homework_num' => DB::raw('month_homework_num' . ' + ' . $value)]);
            } elseif ($type == 2) {
                DB::table('student_ranking')
                    ->updateOrInsert(['user_id' => $userId], ['month_word_num' => DB::raw('month_word_num' . ' + ' . $value)]);
            } elseif ($type == 3) {
                $mock = DB::table('student_ranking')->where('user_id', '=', $userId)->first();
                if ($mock == null) {
                    DB::table('student_ranking')
                        ->insert(['user_id' => $userId, 'month_mock_toefl_score' => $value]);
                } elseif ($mock->month_mock_toefl_score < $value) {
                    DB::table('student_ranking')
                        ->where('user_id', '=', $userId)
                        ->update(['month_mock_toefl_score' => $value]);
                }
            }
        }
    }

    /**
     * 兑换资源信息
     * @param $storeId
     * @return object|null
     */
    public function resourceInfo($storeId)
    {
        return DB::table('exchange_store')->where(['status' => 1, 'id' => $storeId])->first();
    }

    /**
     * 添加单条积分记录
     * @param int $userId 学员id
     * @param int $type 1:登录积分;2:评论积分;3:计划积分;4:作业积分;5:单词积分;6:勋章积分;7:排行榜积分;8:积分消耗
     * @param string $reason 变动原因
     * @param int $changeType 1:增加;2:减少;3:返还
     * @param int $point 积分值
     * @param int $resourceId 资源id
     * @return int 自增id
     */
    public function createOne($userId, $type, $reason, $changeType, $point, $resourceId = 0)
    {
        return DB::table('point_record')->insertGetId([
            'type' => $type,
            'reason' => $reason,
            'change_type' => $changeType,
            'point' => $point,
            'resource_id' => $resourceId,
            'user_id' => $userId
        ]);
    }

    /**
     * 添加多条积分记录
     * @param $data
     * @return void
     */
    public function createMultiple($data)
    {
        DB::table('point_record')->insert($data);
    }

    /**
     * 添加多条积分消息
     * @param $data
     * @return void
     */
    public function createPointMessageMultiple($data)
    {
        DB::table('stu_message')->insert($data);
    }

    /**
     * 登录积分
     * @param int $userId 学员id
     * @return void
     */
    public function loginPoint($userId)
    {
        $reason = '登录学员中心系统';
        $objPoint = DB::table('point_record')
            ->where(['type' => self::LOGIN_POINT, 'reason' => $reason, 'user_id' => $userId])
            ->where('created_at', '>', DateUtil::getToday())
            ->first();
        if ($objPoint == null) {
            $this->increaseStudentPoint($userId, 1);
            $this->createOne($userId, self::LOGIN_POINT, $reason, self::POINT_INCREASE, 1);
            $message = $this->msgStr(self::POINT_TYPE_1, $reason);
            StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+1');
            //是否连续周登录
            $lastContinuousPoint = DB::table('point_record')
                ->where(['type' => self::LOGIN_POINT, 'user_id' => $userId])
                ->where('reason', 'like', '%连续%')
                ->where('created_at', '>', DateUtil::getLastBeginOfMonth())
                ->orderBy('created_at', 'desc')
                ->first();
            $lastWeekPoint = DB::table('point_record')
                ->where(['type' => self::LOGIN_POINT, 'reason' => $reason, 'user_id' => $userId])
                ->where('created_at', '>', DateUtil::getLastMonday())
                ->where('created_at', '<', DateUtil::getThisMonday())
                ->first();
            if ($lastContinuousPoint == null) {
                if ($lastWeekPoint != null) {
                    $reason = '连续2周，每周登录学员中心系统';
                    $this->increaseStudentPoint($userId, 3);
                    $this->createOne($userId, self::LOGIN_POINT, $reason, self::POINT_INCREASE, 3);
                    $message = $this->msgStr(self::POINT_TYPE_1, $reason);
                    StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+3');
                }
            } else {
                $number = $this->getNumerics($lastContinuousPoint->reason);
                if ($lastContinuousPoint->created_at < DateUtil::getLastMonday()) {
                    if ($lastWeekPoint != null) {
                        $reason = '连续2周，每周登录学员中心系统';
                        $this->increaseStudentPoint($userId, 3);
                        $this->createOne($userId, self::LOGIN_POINT, $reason, self::POINT_INCREASE, 3);
                        $message = $this->msgStr(self::POINT_TYPE_1, $reason);
                        StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+3');
                    }
                } elseif ($lastContinuousPoint->created_at < DateUtil::getThisMonday()) {
                    if ($number == 2) {
                        $point = 5;
                    } elseif ($number == 3) {
                        $point = 8;
                    } else {
                        $point = 8;
                    }
                    $reason = '连续' . ++$number . '周，每周登录学员中心系统';
                    $this->increaseStudentPoint($userId, $point);
                    $this->createOne($userId, self::LOGIN_POINT, $reason, self::POINT_INCREASE, $point);
                    $message = $this->msgStr(self::POINT_TYPE_1, $reason);
                    StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+' . $point);
                }
            }
            (new PersonalCenterService)->changeLevel($userId);
        }
    }

    /**
     * 评论积分
     * @param int $userId 学员id
     * @return void
     */
    public function commentPoint($userId)
    {
        $reason = '参与评论互动';
        $total = DB::table('point_record')
            ->where(['user_id' => $userId, 'type' => self::COMMENT_POINT, 'reason' => $reason])
            ->where('created_at', '>', DateUtil::getToday())
            ->sum('point');
        if ($total < 3) {
            $this->increaseStudentPoint($userId, 1);
            (new PersonalCenterService)->changeLevel($userId);
            $this->createOne($userId, self::COMMENT_POINT, $reason, self::POINT_INCREASE, 1);
            $message = $this->msgStr(self::POINT_TYPE_2, $reason);
            StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+1');
        }
    }

    /**
     * 计划积分
     * @param int $userId 学员id
     * @param string $taskDate 任务日期 Y-m-d
     * @return void
     */
    public function dailyPlanPoint($userId, $taskDate)
    {
        $taskDateStr = DateUtil::dateStr('Y年m月d日', strtotime($taskDate));
        $reason = '完成' . $taskDateStr . '计划里的所有任务';
        $res = DB::table('point_record')
            ->where(['user_id' => $userId, 'type' => self::DAILY_PLAN_POINT, 'reason' => $reason])
            ->first();
        if ($res == null) {
            $this->increaseStudentPoint($userId, 5);
            (new PersonalCenterService)->changeLevel($userId);
            $this->createOne($userId, self::DAILY_PLAN_POINT, $reason, self::POINT_INCREASE, 5);
            $message = $this->msgStr(self::POINT_TYPE_3, $reason);
            StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+5');
        }
    }

    /**
     * 作业积分
     * @param int $userId 学员id
     * @param int $homeworkType 1任务/2作业包
     * @return void
     * @throws \Exception
     */
    public function homeworkPoint($userId, $homeworkType)
    {
        if (!in_array($homeworkType, [1, 2])) {
            throw new \Exception('parameter error');
        }
        $reason = 'LMS完成一个' . ($homeworkType == 1 ? '任务' : '作业包');
        $point = $homeworkType == 1 ? 2 : 3;
        $this->increaseStudentPoint($userId, $point);
        (new PersonalCenterService)->changeLevel($userId);
        $this->createOne($userId, self::HOMEWORK_POINT, $reason, self::POINT_INCREASE, $point);
        $message = $this->msgStr(self::POINT_TYPE_4, $reason);
        StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+' . $point);
    }

    /**
     * 单词积分
     * @param int $userId 学员id
     * @param string $taskType 1单词记忆/2错词回顾/3收藏夹回顾/4练习/5学习计划
     * @return void
     * @throws \Exception
     */
    public function wordPoint($userId, $taskType)
    {
        if (!in_array($taskType, [1, 2, 3, 4, 5])) {
            throw new \Exception('parameter error');
        }
        $tasks = [
            1 => ['reason' => '百万单词完成一次单词记忆', 'point' => 2],
            2 => ['reason' => '百万单词完成一次错词回顾', 'point' => 2],
            3 => ['reason' => '百万单词完成一次收藏夹回顾', 'point' => 2],
            4 => ['reason' => '百万单词完成一次练习', 'point' => 2],
            5 => ['reason' => '百万单词完成一次学习计划', 'point' => 5],
        ];
        $total = DB::table('point_record')
            ->where([['user_id', '=', $userId], ['created_at', '>', DateUtil::getToday()]])
            ->where(function ($query) {
                $query->where('type', '=', self::WORD_POINT)
                    ->orWhereRaw("(type=" . self::MEDAL_POINT . " and (reason like '%获得一个《反射弧之星%' or reason like '%获得一个《百分百战士%'))");
            })
            ->sum('point');
        if ($total < 50) {
            $point = $tasks[$taskType]['point'];
            if (($total + $point) > 50) {
                $point = 50 - $total;
            }
            $this->increaseStudentPoint($userId, $point);
            (new PersonalCenterService)->changeLevel($userId);
            $this->createOne($userId, self::WORD_POINT, $tasks[$taskType]['reason'], self::POINT_INCREASE, $point);
            $message = $this->msgStr(self::POINT_TYPE_5, $tasks[$taskType]['reason']);
            StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+' . $point);
        }
    }

    /**
     * 勋章积分
     * @param int $userId 学员id
     * @param string $medalName 作业达人/作业王者/作业专家/学习坚守者/阅读之星/社交达人/Workshop宣传大使/学霸之星/积分名人堂/万词王者/熟能生巧/灰烬审判者/反射弧之星/百分百战士/攻关达人
     * @param int $point 积分值
     * @param string $medalLevel 铜/银/金
     * @param bool $eventTrigger
     * @return void
     */
    public function medalPoint($userId, $medalName, $point, $medalLevel = '', $eventTrigger = true)
    {
        $reason = '获得一个《' . ($medalLevel == '' ? $medalName : $medalName . '·' . $medalLevel) . '》勋章';
        //单词积分上限判断
        if (in_array($medalName, ['反射弧之星', '百分百战士'])) {
            $total = DB::table('point_record')
                ->where([['user_id', '=', $userId], ['created_at', '>', DateUtil::getToday()]])
                ->where(function ($query) {
                    $query->where('type', '=', self::WORD_POINT)
                        ->orWhereRaw("(type=" . self::MEDAL_POINT . " and (reason like '%获得一个《反射弧之星%' or reason like '%获得一个《百分百战士%'))");
                })
                ->sum('point');
            if ($total < 50) {
                if (($total + $point) > 50) {
                    $point = 50 - $total;
                }
                //每个勋章限制总分为 500
                $total2 = DB::table('point_record')
                    ->where([['user_id', '=', $userId]])
                    ->where(function ($query) use ($medalName) {
                        $query->where('type', '=', self::WORD_POINT)
                            ->orWhereRaw("(type=" . self::MEDAL_POINT . " and (reason like '%获得一个《" . $medalName . "%' or reason like '历史获得《" . $medalName . "%' ))");
                    })
                    ->sum('point');
                if ($total2 >= 500) {
                    return;
                }
                if (($total2 + $point) > 500) {
                    $point = 500 - $total2;
                }
                $this->increaseStudentPoint($userId, $point, $eventTrigger);
                (new PersonalCenterService)->changeLevel($userId);
                $this->createOne($userId, self::MEDAL_POINT, $reason, self::POINT_INCREASE, $point);
                $message = $this->msgStr(self::POINT_TYPE_6, $reason);
                StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+' . $point);
            }
        } else {
            $isUpdateRanking = !($medalName == MedalConst::STUDY_STAR);
            $this->increaseStudentPoint($userId, $point, $eventTrigger, $isUpdateRanking);
            (new PersonalCenterService)->changeLevel($userId);
            $this->createOne($userId, self::MEDAL_POINT, $reason, self::POINT_INCREASE, $point);
            $message = $this->msgStr(self::POINT_TYPE_6, $reason);
            StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+' . $point);
        }
    }

    //历史勋章积分
    public function historyMedalPoint($userId, $medalName, $point, $medalLevel = '', $eventTrigger = true)
    {
        $reason = '历史获得《' . ($medalLevel == '' ? $medalName : $medalName . '·' . $medalLevel) . '》勋章';
        if (in_array($medalName, ['万词王者', '熟能生巧', '灰烬审判者', '反射弧之星', '百分百战士', '攻关达人'])) {
            $total = DB::table('point_record')
                ->where([['user_id', '=', $userId], ['type', '=', self::MEDAL_POINT], ['reason', 'like', '%历史获得《' . $medalName . '%']])
                ->sum('point');
            if ($total < 50) {
                if (($total + $point) > 50) {
                    $point = 50 - $total;
                }
                $this->increaseStudentPoint($userId, $point, $eventTrigger);
                (new PersonalCenterService)->changeLevel($userId);
                $this->createOne($userId, self::MEDAL_POINT, $reason, self::POINT_INCREASE, $point);
                $message = $this->msgStr(self::POINT_TYPE_6, $reason);
                StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+' . $point);
            }
        } else {
            $this->increaseStudentPoint($userId, $point, $eventTrigger);
            (new PersonalCenterService)->changeLevel($userId);
            $this->createOne($userId, self::MEDAL_POINT, $reason, self::POINT_INCREASE, $point);
            $message = $this->msgStr(self::POINT_TYPE_6, $reason);
            StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+' . $point);
        }
    }

    /**
     * 修改昵称
     * @param int $userId
     * @param int $changeType 1:兑换;2:返还
     * @param int $point 积分值
     * @return array|int[]
     */
    public function changeNickname($userId, $changeType, $point = 5)
    {
        DB::beginTransaction();
        try {
            if (!in_array($changeType, [1, 2])) {
                throw new \Exception('parameter error');
            }
//            $point = 5;
            $stuPoint = $this->getStudentPoint($userId);
            if ($changeType == 1 && $stuPoint < $point) {
                throw new \Exception('积分不足');
            } else {
                $reason = $changeType == 1 ? '修改昵称' : '修改昵称失败';
                $pointChangeType = $changeType == 1 ? self::POINT_DECREASE : self::POINT_RETURN;
                $changePoint = $changeType == 1 ? -$point : $point;
                $memo = $changeType == 1 ? '-' . $point : '+' . $point;
                $this->updateStudentPoint($userId, $stuPoint + $changePoint);
                $this->createOne($userId, self::EXCHANGE_POINT, $reason, $pointChangeType, $point);
                $message = $this->msgStr(self::POINT_TYPE_8, $reason);
                StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, $memo);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return ["success" => 0, "msg" => $e->getMessage()];
        }
        return ["success" => 1];
    }

    /**
     * 报名workshop
     * @param $userId
     * @param $workshopName
     * @return array
     */
    public function signUpWorkshop($userId, $workshopName)
    {
        DB::beginTransaction();
        try {
            $reason = '预约研讨会《' . $workshopName . '》';
            $point = 3;
            $stuPoint = $this->getStudentPoint($userId);
            if ($stuPoint < $point) {
                throw new \Exception('积分不足');
            } else {
                $this->updateStudentPoint($userId, $stuPoint - $point);
                $this->createOne($userId, self::EXCHANGE_POINT, $reason, self::POINT_DECREASE, $point);
                $message = $this->msgStr(self::POINT_TYPE_8, $reason);
                StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '-' . $point);
            }
            DB::commit();
            return ["success" => 1, "msg" => '预约研讨会成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            return ["success" => 0, "msg" => $e->getMessage()];
        }
    }

    /**
     * 兑换商品
     * @param int $userId
     * @param int $exchangeType 1解锁头像框/2兑换皮肤
     * @param int $storeId
     * @return array
     * @throws \Exception
     */
    public function exchange($userId, $exchangeType, $storeId)
    {
        DB::beginTransaction();
        try {
            $resourceInfo = $this->resourceInfo($storeId);
            if ($resourceInfo == null) {
                throw new \Exception('兑换资源不存在');
            }
            if ($resourceInfo->type != $exchangeType) {
                throw new \Exception('兑换资源类型错误');
            }
            $reason = '兑换一个' . ($exchangeType == 1 ? '头像挂件' : '背景皮肤') . '《' . $resourceInfo->title . '》';
            $point = $resourceInfo->score;
            $stuPoint = $this->getStudentPoint($userId);
            //免费的不再验证用户积分
            if ($point > 0 && $stuPoint < $point) {
                throw new \Exception('积分不足');
            } else {
                $this->updateStudentPoint($userId, $stuPoint - $point);
                $this->createOne($userId, self::EXCHANGE_POINT, $reason, self::POINT_DECREASE, $point, $storeId);
                $message = $this->msgStr(self::POINT_TYPE_8, $reason);
                StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '-' . $point);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return ['success' => false, "msg" => $e->getMessage()];
        }
        return ['success' => true, "msg" => ""];
    }

    /**
     * 榜单是否开放
     * @param $type
     * @param $time
     * @return bool
     */
    public static function isShow($type, $time)
    {
        $studentRankings = DB::table('student_ranking_types')
            ->where([
                ['type_id', '=', $type],
                ['start_time', '<=', $time],
                ['end_time', '>=', $time]
            ])->first();
        return (bool)$studentRankings;
    }

    /**
     * 周排行榜结算
     * @return void
     */
    public function weeklyRank()
    {
        $today = DateUtil::getToday();
        DB::table('logic_log')->insert(['details' => json_encode(['scheduleTask' => 'WeeklyRank', 'result' => 'start', 'msg' => ''])]);
        DB::beginTransaction();
        try {
            $weekPointTopThree = [];
            if (self::isShow(1, $today)) {
                $blackList = env('BLACK_USER_LIST');
                $blackListArray = empty($blackList) ? [] : explode(',', $blackList);
                $blackListString = "'" . implode("','", $blackListArray) . "'";
                //2.结算上周积分前20名
//                $lastMonday = DateUtil::getLastMonday();
//                $thisMonday = DateUtil::getThisMonday();
//                $weekPointData = DB::table('point_record')
//                    ->select('user_id', DB::raw('sum(point) as week_point'))
//                    ->where('change_type', '=', 1)
//                    ->where('created_at', '>', $lastMonday)
//                    ->where('created_at', '<', $thisMonday)
//                    ->groupBy('user_id')
//                    ->orderBy('week_point', 'desc')
//                    ->get()
//                    ->toArray();
                $weekPointData = DB::table('student_ranking')
                    ->select('user_id', 'week_point')
                    ->whereNotIn('user_id', $blackListArray)
                    ->where('week_point', '>=', function ($query) use ($blackListString) {
                        $query->select(DB::raw('min(b.week_point) from (select a.week_point from sc_student_ranking as a where a.week_point > 0 and a.user_id not in(' . $blackListString . ') order by a.week_point desc limit 20) as b'));
                    })
                    ->orderBy('week_point', 'desc')
                    ->get()
                    ->toArray();
                //1.周积分榜清零
                DB::table('student_ranking')->update(['week_point' => 0]);
                if (!empty($weekPointData)) {
                    $lastMondayStr = DateUtil::dateStr('Y年m月d日', strtotime('last week Monday'));
                    $lastSundayStr = DateUtil::dateStr('Y年m月d日', strtotime('last week Sunday'));
                    $addPointMessageData = [];
                    $addPointRecordData = [];
                    $i = 1;
                    foreach ($weekPointData as $k => $v) {
                        if ($k > 0 && $v->week_point < $weekPointData[$k - 1]->week_point) {
                            $i = $k + 1;
                        }
                        $userId = $v->user_id;
                        $point = $this->rankingPoint[$i];
                        $reason = '获得积分排行榜' . $lastMondayStr . '~' . $lastSundayStr . '第' . $i . '名';
                        $message = $this->msgStr(self::POINT_TYPE_7, $reason);
                        $addPointMessageData[] = [
                            'msgtype' => '积分消息',
                            'froms' => '积分消息',
                            'recive_user' => $userId,
                            'msg' => $message,
                            'memo' => '+' . $point
                        ];
                        $addPointRecordData[] = [
                            'type' => self::RANKING_POINT,
                            'reason' => $reason,
                            'point' => $point,
                            'user_id' => $userId
                        ];
                        if ($i < 4) {
                            $weekPointTopThree[] = ['id' => $userId];
                        }
                        $this->increaseStudentPoint($userId, $point, true, false);
                        (new PersonalCenterService)->changeLevel($userId);
//                        if ($i > 19) {
//                            break;
//                        }
                    }
                    if (!empty($addPointRecordData)) {
                        $this->createMultiple($addPointRecordData);
                    }
                    if (!empty($addPointMessageData)) {
                        $this->createPointMessageMultiple($addPointMessageData);
                    }
                    if (!empty($weekPointTopThree)) {
                        echo "Week Point Top Three User : " . json_encode($weekPointTopThree) . PHP_EOL;
                        (new MedalService())->topMedal($weekPointTopThree, MedalConst::STUDY_STAR);
//                        Event::dispatch(new MedalEvent(MedalConst::STUDY_STAR, $weekPointTopThree));
                    }
                }
            }
            DB::table('logic_log')->insert(['details' => json_encode(['scheduleTask' => 'WeeklyRank', 'result' => 'end', 'msg' => ''])]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            DB::table('logic_log')->insert(['details' => json_encode(['scheduleTask' => 'WeeklyRank', 'result' => 'error', 'msg' => $e->getMessage()])]);
        }
    }

    /**
     * 月排行榜结算
     * @return void
     */
    public function monthlyRank()
    {
        $today = DateUtil::getToday();
        DB::table('logic_log')->insert(['details' => json_encode(['scheduleTask' => 'monthlyRank', 'result' => 'start', 'msg' => ''])]);
        DB::beginTransaction();
        try {
            //月积分榜
//            $lastMonth = DateUtil::getLastBeginOfMonth();
//            $thisMonth = DateUtil::getBeginOfMonth();
            $blackList = env('BLACK_USER_LIST');
            $blackListArray = empty($blackList) ? [] : explode(',', $blackList);
            $blackListString = "'" . implode("','", $blackListArray) . "'";
            $lastMonthStr = DateUtil::dateStr('Y年m月', strtotime('-1 month'));
            $addPointMessageData = [];
            $addPointRecordData = [];
            $monthPointTopThree = [];
            $monthHomeworkNumTopThree = [];
            $monthWordNumTopThree = [];
            $monthMockToeflScoreTopThree = [];
            $monthRightTopThree = [];
            if (self::isShow(2, $today)) {
                $monthPointData = DB::table('student_ranking')
                    ->select('user_id', 'month_point')
                    ->whereNotIn('user_id', $blackListArray)
                    ->where('month_point', '>=', function ($query) use ($blackListString) {
                        $query->select(DB::raw('min(b.month_point) from (select a.month_point from sc_student_ranking as a where a.month_point > 0 and a.user_id not in(' . $blackListString . ') order by a.month_point desc limit 20) as b'));
                    })
                    ->orderBy('month_point', 'desc')
                    ->get()
                    ->toArray();
                //1.月积分榜清零
                DB::table('student_ranking')->update(['month_point' => 0]);
                if (!empty($monthPointData)) {
                    $i = 1;
                    foreach ($monthPointData as $k1 => $v1) {
                        if ($k1 > 0 && $v1->month_point < $monthPointData[$k1 - 1]->month_point) {
                            $i = $k1 + 1;
                        }
                        $userId = $v1->user_id;
                        $point = $this->rankingPoint[$i];
                        $reason = '获得积分排行榜' . $lastMonthStr . '第' . $i . '名';
                        $message = $this->msgStr(self::POINT_TYPE_7, $reason);
                        $addPointMessageData[] = [
                            'msgtype' => '积分消息',
                            'froms' => '积分消息',
                            'recive_user' => $userId,
                            'msg' => $message,
                            'memo' => '+' . $point
                        ];
                        $addPointRecordData[] = [
                            'type' => self::RANKING_POINT,
                            'reason' => $reason,
                            'point' => $point,
                            'user_id' => $userId
                        ];
                        if ($i < 4) {
                            $monthPointTopThree[] = ['id' => $userId];
                        }
                        $this->increaseStudentPoint($userId, $point, true, false);
                        (new PersonalCenterService)->changeLevel($userId);
                    }
                }
            }
            //月作业数
            if (self::isShow(4, $today)) {
                $monthHomeworkNumData = DB::table('student_ranking')
                    ->select('user_id', 'month_homework_num')
                    ->whereNotIn('user_id', $blackListArray)
                    ->where('month_homework_num', '>=', function ($query) use ($blackListString) {
                        $query->select(DB::raw('min(b.month_homework_num) from (select a.month_homework_num from sc_student_ranking as a where a.month_homework_num > 0 and a.user_id not in(' . $blackListString . ') order by a.month_homework_num desc limit 20) as b'));
                    })
                    ->orderBy('month_homework_num', 'desc')
                    ->get()
                    ->toArray();
                DB::table('student_ranking')->update(['month_homework_num' => 0]);
                if (!empty($monthHomeworkNumData)) {
                    $i = 1;
                    foreach ($monthHomeworkNumData as $k2 => $v2) {
                        if ($k2 > 0 && $v2->month_homework_num < $monthHomeworkNumData[$k2 - 1]->month_homework_num) {
                            $i = $k2 + 1;
                        }
                        $userId = $v2->user_id;
                        $point = $this->rankingPoint[$i];
                        $reason = '获得任务排行榜' . $lastMonthStr . '作业数第' . $i . '名';
                        $message = $this->msgStr(self::POINT_TYPE_7, $reason);
                        $addPointMessageData[] = [
                            'msgtype' => '积分消息',
                            'froms' => '积分消息',
                            'recive_user' => $userId,
                            'msg' => $message,
                            'memo' => '+' . $point
                        ];
                        $addPointRecordData[] = [
                            'type' => self::RANKING_POINT,
                            'reason' => $reason,
                            'point' => $point,
                            'user_id' => $userId
                        ];
                        if ($i < 4) {
                            $monthHomeworkNumTopThree[] = ['id' => $userId];
                        }
                        $this->increaseStudentPoint($userId, $point, true, false);
                        (new PersonalCenterService)->changeLevel($userId);
                    }
                }
            }
            //月抽查单词数
            if (self::isShow(5, $today)) {
                $monthWordNumData = DB::table('student_ranking')
                    ->select('user_id', 'month_word_num')
                    ->whereNotIn('user_id', $blackListArray)
                    ->where('month_word_num', '>=', function ($query) use ($blackListString) {
                        $query->select(DB::raw('min(b.month_word_num) from (select a.month_word_num from sc_student_ranking as a where a.month_word_num > 0 and a.user_id not in(' . $blackListString . ') order by a.month_word_num desc limit 20) as b'));
                    })
                    ->orderBy('month_word_num', 'desc')
                    ->get()
                    ->toArray();
                DB::table('student_ranking')->update(['month_word_num' => 0]);
                if (!empty($monthWordNumData)) {
                    $i = 1;
                    foreach ($monthWordNumData as $k3 => $v3) {
                        if ($k3 > 0 && $v3->month_word_num < $monthWordNumData[$k3 - 1]->month_word_num) {
                            $i = $k3 + 1;
                        }
                        $userId = $v3->user_id;
                        $point = $this->rankingPoint[$i];
                        $reason = '获得任务排行榜' . $lastMonthStr . '抽查单词数第' . $i . '名';
                        $message = $this->msgStr(self::POINT_TYPE_7, $reason);
                        $addPointMessageData[] = [
                            'msgtype' => '积分消息',
                            'froms' => '积分消息',
                            'recive_user' => $userId,
                            'msg' => $message,
                            'memo' => '+' . $point
                        ];
                        $addPointRecordData[] = [
                            'type' => self::RANKING_POINT,
                            'reason' => $reason,
                            'point' => $point,
                            'user_id' => $userId
                        ];
                        if ($i < 4) {
                            $monthWordNumTopThree[] = ['id' => $userId];
                        }
                        $this->increaseStudentPoint($userId, $point, true, false);
                        (new PersonalCenterService)->changeLevel($userId);
                    }
                }
            }
            //月度托福模考高分榜
            if (self::isShow(6, $today)) {
                $monthMockToeflScoreData = DB::table('student_ranking')
                    ->select('user_id', 'month_mock_toefl_score')
                    ->whereNotIn('user_id', $blackListArray)
                    ->where('month_mock_toefl_score', '>=', function ($query) use ($blackListString) {
                        $query->select(DB::raw('min(b.month_mock_toefl_score) from (select a.month_mock_toefl_score from sc_student_ranking as a where a.month_mock_toefl_score > 0 and a.user_id not in(' . $blackListString . ') order by a.month_mock_toefl_score desc limit 20) as b'));
                    })
                    ->orderBy('month_mock_toefl_score', 'desc')
                    ->get()
                    ->toArray();
                DB::table('student_ranking')->update(['month_mock_toefl_score' => 0]);
                if (!empty($monthMockToeflScoreData)) {
                    $i = 1;
                    foreach ($monthMockToeflScoreData as $k4 => $v4) {
                        if ($k4 > 0 && $v4->month_mock_toefl_score < $monthMockToeflScoreData[$k4 - 1]->month_mock_toefl_score) {
                            $i = $k4 + 1;
                        }
                        $userId = $v4->user_id;
                        $point = $this->rankingPoint[$i];
                        $reason = '获得模考排行榜' . $lastMonthStr . '托福第' . $i . '名';
                        $message = $this->msgStr(self::POINT_TYPE_7, $reason);
                        $addPointMessageData[] = [
                            'msgtype' => '积分消息',
                            'froms' => '积分消息',
                            'recive_user' => $userId,
                            'msg' => $message,
                            'memo' => '+' . $point
                        ];
                        $addPointRecordData[] = [
                            'type' => self::RANKING_POINT,
                            'reason' => $reason,
                            'point' => $point,
                            'user_id' => $userId
                        ];
                        if ($i < 4) {
                            $monthMockToeflScoreTopThree[] = ['id' => $userId];
                        }
                        $this->increaseStudentPoint($userId, $point, true, false);
                        (new PersonalCenterService)->changeLevel($userId);
                    }
                }
            }
            //月度抽查正确词排行榜
            if (self::isShow(7, $today)) {
                $rightMonth = (new TalkService())->rankListOfWord($blackList, false);
                $rightMonth = $rightMonth[0];
                if (!empty($rightMonth)) {
                    $i = 1;
                    foreach ($rightMonth as $k3 => $v3) {
                        if ($k3 > 0 && $v3->point < $rightMonth[$k3 - 1]->point) {
                            $i = $k3 + 1;
                        }
                        $userId = $v3->user_id;
                        $point = $this->rankingPoint[$i];
                        $reason = '获得任务排行榜' . $lastMonthStr . '抽查正确词数第' . $i . '名';
                        $addPointMessageData[] = [
                            'msgtype' => '积分消息',
                            'froms' => '积分消息',
                            'recive_user' => $userId,
                            'msg' => $this->msgStr(self::POINT_TYPE_7, $reason),
                            'memo' => '+' . $point
                        ];
                        $addPointRecordData[] = [
                            'type' => self::RANKING_POINT,
                            'reason' => $reason,
                            'point' => $point,
                            'user_id' => $userId
                        ];
                        if ($i < 4) {
                            $monthRightTopThree[] = ['id' => $userId];
                        }
                        $this->increaseStudentPoint($userId, $point, true, false);
                        (new PersonalCenterService)->changeLevel($userId);
                    }
                }
            }
            if (!empty($addPointRecordData)) {
                $this->createMultiple($addPointRecordData);
            }
            if (!empty($addPointMessageData)) {
                $this->createPointMessageMultiple($addPointMessageData);
            }
            if (!empty($monthPointTopThree)) {
                echo "Month Point Top Three User : " . json_encode($monthPointTopThree) . PHP_EOL;
                (new MedalService())->topMedal($monthPointTopThree, MedalConst::STUDY_STAR);
            }
            if (!empty($monthHomeworkNumTopThree)) {
                echo "Month Homework Number Top Three User : " . json_encode($monthHomeworkNumTopThree) . PHP_EOL;
                (new MedalService())->topMedal($monthHomeworkNumTopThree, MedalConst::STUDY_STAR);
            }
            if (!empty($monthWordNumTopThree)) {
                echo "Month Word Number Top Three User : " . json_encode($monthWordNumTopThree) . PHP_EOL;
                (new MedalService())->topMedal($monthWordNumTopThree, MedalConst::STUDY_STAR);
            }
            if (!empty($monthMockToeflScoreTopThree)) {
                echo "Month Mock Toefl Score Top Three User : " . json_encode($monthMockToeflScoreTopThree) . PHP_EOL;
                (new MedalService())->topMedal($monthMockToeflScoreTopThree, MedalConst::STUDY_STAR);
            }
            if (!empty($monthRightTopThree)) {
                echo "Month Right Top Three User : " . json_encode($monthRightTopThree) . PHP_EOL;
                (new MedalService())->topMedal($monthRightTopThree, MedalConst::STUDY_STAR);
            }
            DB::table('logic_log')->insert(['details' => json_encode(['scheduleTask' => 'monthlyRank', 'result' => 'end', 'msg' => ''])]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            DB::table('logic_log')->insert(['details' => json_encode(['scheduleTask' => 'monthlyRank', 'result' => 'error', 'msg' => $e->getMessage()])]);
        }
    }

    /**
     * 消息内容
     * @param $pointType
     * @param $content
     * @return string
     */
    private function msgStr($pointType, $content)
    {
        return '【' . $pointType . '】 ' . $content;
    }

    public function getNumerics($str)
    {
        return preg_replace('/\D/', '', $str);
    }

    public function batchInsertOrUpdateSql($data, $insertColumns, $updateColumns)
    {
        $sql = '';
        if (!empty($data) && !empty($insertColumns) && !empty($updateColumns)) {
            $sql = "insert into sc_student_point_ranking(";
            foreach ($insertColumns as $insertColumn) {
                $sql .= $insertColumn . ",";
            }
            $sql = trim($sql, ",");
            $sql .= ") values";
            foreach ($data as $value) {
                $sql .= "(";
                foreach ($insertColumns as $column) {
                    $sql .= (($column == 'user_id') ? ("'" . $value[$column] . "'") : $value[$column]) . ",";
                }
                $sql = trim($sql, ",");
                $sql .= "),";
            }
            $sql = trim($sql, ",");
            $sql .= " on duplicate key update ";
            foreach ($updateColumns as $updateColumn) {
                $sql .= $updateColumn . " = values(" . $updateColumn . "),";
            }
            $sql = trim($sql, ",");
            $sql .= ";";
        }
        return $sql;
    }

    public function fixUserPointMedal()
    {
        $message = "因反射弧之星、百分百战士徽章增加积分上限，你的可用积分及总积分被扣除%s";
        $sql = "SELECT user_id,reason, SUM(point) as total FROM sc_point_record WHERE type = 6 AND
(reason like '获得一个《反射弧之星%' or reason like '获得一个《百分百战士%' or reason LIKE '因反射弧之星、百分百战士徽章增加积分上限%' or reason like '历史获得《百分百战士%' or reason like '历史获得《反射弧之星%')
GROUP BY user_id,reason";
        $list = DB::select($sql);
        $data = [];
        foreach ($list as $item) {
            if (!isset($data[$item->user_id])) {
                $data[$item->user_id] = [];
            }
            if (Str::startsWith($item->reason, "获得一个《百分百战士》勋章") || Str::startsWith($item->reason, "历史获得《百分百战士")) {
                $data[$item->user_id][0] = $item->total;
            } else if (Str::startsWith($item->reason, "获得一个《反射弧之星》勋章") || Str::startsWith($item->reason, "历史获得《反射弧之星")) {
                $data[$item->user_id][1] = $item->total;
            } else if (Str::startsWith($item->reason, "因反射弧之星、百分百战士徽章")) {
                $data[$item->user_id][2] = $item->total;
            }
        }
        $limitPoint = 500;
        $point = [];
        foreach ($data as $user => $item) {
            if (isset($item[2])) {
                //已经扣过分数的不扣了
                continue;
            }
            $_point = 0;
            if (isset($item[0]) && $item[0] > $limitPoint) {
                $_point += $item[0] - $limitPoint;
            }
            if (isset($item[1]) && $item[1] > $limitPoint) {
                $_point += $item[1] - $limitPoint;
            }
            if ($_point > 0) {
                $point[$user] = $_point;
            }
        }
        if ($point) {
            //进行扣分
            foreach ($point as $userId => $_point) {
                $msg = sprintf($message, $_point);
                $_point = $_point * -1;
                $this->increaseStudentPoint($userId, $_point, false, false);
                (new PersonalCenterService)->changeLevel($userId);
                $this->createOne($userId, self::MEDAL_POINT, $msg, self::POINT_INCREASE, $_point);
                StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $msg, '', $userId, 0, 0, $_point);
                echo sprintf("Fix User : %s \n", $userId);
            }
        }
    }

    /**
     * 调研积分
     * @param $userId
     * @param int $point
     * @return void
     */
    public function surveyPoint($userId, int $point = 0)
    {
        $reason = '完成新课堂整体评价';
        $this->increaseStudentPoint($userId, $point);
        (new PersonalCenterService)->changeLevel($userId);
        $this->createOne($userId, self::SURVEY_POINT, $reason, self::POINT_INCREASE, $point);
        $message = $this->msgStr(self::POINT_TYPE_9, $reason);
        StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+' . $point);
    }

    public function teacherEvaluatePoint($userId, int $point = 0)
    {
        $reason = '参与师评反馈';
        $this->increaseStudentPoint($userId, $point);
        (new PersonalCenterService)->changeLevel($userId);
        $this->createOne($userId, self::TEACHER_EVALUATE_POINT, $reason, self::POINT_INCREASE, $point);
        $message = $this->msgStr(self::POINT_TYPE_9, $reason);
        StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '+' . $point);
    }


    /**
     * 通用消耗积分
     * @param $userId
     * @param int $point
     * @param $reason
     * @return bool
     */
    public function consumePoint($userId, int $point = 0, $reason = '')
    {
        DB::beginTransaction();
        try {
            $stuPoint = $this->getStudentPoint($userId);
            if ($point > 0 && $stuPoint < $point) {
                throw new \Exception('积分不足');
            }
            $this->updateStudentPoint($userId, $stuPoint - $point);
            $this->createOne($userId, self::EXCHANGE_POINT, $reason, self::POINT_DECREASE, $point);
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
        }

        return false;

    }

    public function sendGift($userId, int $point = 0, $reason = '')
    {
        $r = $this->consumePoint($userId, $point, $reason);
        if (!$r) {
            return false;
        }
        $message = $this->msgStr(self::POINT_TYPE_8, $reason);
        StuMessageService::makeUserMsg(StuMessageModel::FROMS_11, $message, '', $userId, 0, 0, '-' . $point);
        return true;
    }

}
