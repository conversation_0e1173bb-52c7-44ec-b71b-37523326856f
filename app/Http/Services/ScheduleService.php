<?php

namespace App\Http\Services;

use App\Http\Services\Point\PointService;
use App\Models\RelationSystem\RelationOfCasCrmModel;
use App\Models\Schedule;
use App\Models\ScheduleDetail;
use App\User;
use App\Utils\Common;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ScheduleService
{
    public function index($objUser, $strStudentUaId, $date)
    {
        $arrList = Schedule::TIME_LIME;
        $arrScheduleList = Schedule::with('detail')->with('creator')
            ->where([
                'schedule_date' => $date,
                'owner_uaid' => $strStudentUaId,
            ])
            ->get();
        $arrLessonList = Cache::remember("sc:student:lesson:" . $strStudentUaId . ":" . $date, 60, function () use ($strStudentUaId, $date) {
           return $this->lesson($strStudentUaId, $date);
        });
        foreach ($arrList as &$range) {
            $begin = new Carbon($date . ' ' . $range['startTime']);
            $end = new Carbon($date . ' ' . $range['endTime']);
            $range['content'] = [];
            foreach ($arrLessonList as $i => $objLesson) {
                $current = new Carbon($objLesson->start_time);
                if ($begin->timestamp <= $current->timestamp && $current->timestamp <= $end->timestamp) {
                    $range['content'][] = [
                        'id' => 0,
                        'allDayChecked' => 0,
                        'startTime' => Carbon::create($objLesson->start_time)->format('G:i'),
                        'endTime' => Carbon::create($objLesson->end_time)->format('G:i'),
                        'from' => 'cas',
                        'type' => 'class',
                        'planName' => '',
                        'remark' => null,
                        'isChecked' => null,
                        'finish' => null,
                        'total' => null,
                        'feedBack' => null,
                        'campus' => $objLesson->campus,
                        'room' => $objLesson->room_name,
                        'subject' => $objLesson->subject_name,
                        'teacherName' => $objLesson->teacher_name,
                        'isStu' => null,
                        'editable' => false,
                    ];
                }
            }
            foreach ($arrScheduleList as $item) {
                $isEditable = false;
                if ($objUser->user_type_id == User::USER_TYPE_ID_TEACHER) {
                    $isEditable = true;
                } else {
                    if ($item['owner_uaid'] == $item['creator_uaid'] && !$item->detail->is_freeze) {
                        $isEditable = true;
                    }
                }
                $current = new Carbon($item['schedule_date'] . ' ' . $item['begin']);
                if ($begin->timestamp <= $current->timestamp && $current->timestamp <= $end->timestamp) {
                    $range['content'][] = [
                        'id' => $item->id,
                        'allDayChecked' => $item->all_day,
                        'startTime' => Carbon::create($item->begin)->format('G:i'),
                        'endTime' => Carbon::create($item->end)->format('G:i'),
                        'from' => ScheduleDetail::FORM_MAP[$item->detail->from],
                        'type' => ScheduleDetail::TYPE_MAP[$item->detail->type],
                        'planName' => $item->detail->name,
                        'remark' => $item->detail->remark,
                        'isChecked' => $item->detail->is_checked,
                        'finish' => $item->detail->finish,
                        'total' => $item->detail->total,
                        'feedBack' => $item->detail->feedBack,
                        'campus' => $item->detail->campus,
                        'room' => $item->detail->room,
                        'subject' => $item->detail->subject,
                        'isStu' => $item->creator->user_type_id == User::USER_TYPE_ID_STUDENT,
                        'editable' => $isEditable,
                        'teacherName' => $item->detail->teacher_name,
                    ];
                }
            }
        }
        return $arrList;
    }

    public function create($objUser, $arrPostData)
    {
        $arrExtension = null;
        if ($arrPostData['finish'] !== null) {
            $arrExtension['finish'] = $arrPostData['finish'];
        }
        if ($arrPostData['total'] !== null) {
            $arrExtension['total'] = $arrPostData['total'];
        }
        if ($arrPostData['feedBack']) {
            $arrExtension['feedBack'] = $arrPostData['feedBack'];
        }
        if ($arrPostData['campus']) {
            $arrExtension['campus'] = $arrPostData['campus'];
        }
        if ($arrPostData['room']) {
            $arrExtension['room'] = $arrPostData['room'];
        }
        if ($arrPostData['subject']) {
            $arrExtension['subject'] = $arrPostData['subject'];
        }
        if ($arrPostData['teacherName']) {
            $arrExtension['teacherName'] = $arrPostData['teacherName'];
        }
        if ($arrPostData['planType']) {
            $arrExtension['planType'] = $arrPostData['planType'];
        }
        if ($arrPostData['description']) {
            $arrExtension['description'] = $arrPostData['description'];
        }
        DB::beginTransaction();
        foreach ($arrPostData['time'] as $arrTime) {
            $objSchedule = Schedule::create([
                'schedule_date' => $arrPostData['date'],
                'begin' => $arrTime['startTime'],
                'end' => $arrTime['endTime'],
                'all_day' => $arrPostData['allDayChecked'],
                'owner_uaid' => $arrPostData['studentId'],
                'creator_uaid' => $objUser->ua_id,
            ]);
            $objSchedule->detail()->create([
                'name' => $arrPostData['planName'],
                'remark' => $arrPostData['remark'] ?? '',
                'from' => array_flip(ScheduleDetail::FORM_MAP)[$arrPostData['from']],
                'type' => array_flip(ScheduleDetail::TYPE_MAP)[$arrPostData['type']],
                'is_freeze' => $objUser->user_type_id == User::USER_TYPE_ID_TEACHER ? ScheduleDetail::FREEZE : ScheduleDetail::UN_FREEZE,
                'extension' => is_null($arrExtension) ? null : json_encode($arrExtension),
            ]);
        }
        DB::commit();
    }

    public function edit($objUser, $arrPostData)
    {
        $objSchedule = Schedule::where([
            'id' => $arrPostData['id']
        ])->with('detail')
            ->first();
        if (is_null($objSchedule)) {
            throw new \Exception('计划有改动 编辑失败');
        }
        if ($objUser->user_type_id == User::USER_TYPE_ID_STUDENT) {
            #学生
            if ($objSchedule->creator_uaid != $objUser->ua_id) {
                throw new \Exception('学员只能编辑自己创建的日程');
            }
            if ($objSchedule->detail->is_freeze == ScheduleDetail::FREEZE) {
                throw new \Exception('计划有改动 编辑失败');
            }
        }
        $arrExtension = null;
        if ($arrPostData['finish'] !== null) {
            $arrExtension['finish'] = $arrPostData['finish'];
        }
        if ($arrPostData['total'] !== null) {
            $arrExtension['total'] = $arrPostData['total'];
        }
        if ($arrPostData['feedBack']) {
            $arrExtension['feedBack'] = $arrPostData['feedBack'];
        }
        if ($arrPostData['campus']) {
            $arrExtension['campus'] = $arrPostData['campus'];
        }
        if ($arrPostData['room']) {
            $arrExtension['room'] = $arrPostData['room'];
        }
        if ($arrPostData['subject']) {
            $arrExtension['subject'] = $arrPostData['subject'];
        }
        if ($arrPostData['teacherName']) {
            $arrExtension['teacherName'] = $arrPostData['teacherName'];
        }
        if ($arrPostData['planType']) {
            $arrExtension['planType'] = $arrPostData['planType'];
        }
        if ($arrPostData['description']) {
            $arrExtension['description'] = $arrPostData['description'];
        }
        $objSchedule->detail()->delete();
        $objSchedule->delete();

        foreach ($arrPostData['time'] as $arrTime) {
            $objSchedule = Schedule::create([
                'schedule_date' => $arrPostData['date'],
                'begin' => $arrTime['startTime'],
                'end' => $arrTime['endTime'],
                'all_day' => $arrPostData['allDayChecked'],
                'owner_uaid' => $arrPostData['studentId'],
                'creator_uaid' => $objUser->ua_id,
            ]);
            $objSchedule->detail()->create([
                'name' => $arrPostData['planName'],
                'remark' => $arrPostData['remark'] ?? '',
                'from' => array_flip(ScheduleDetail::FORM_MAP)[$arrPostData['from']],
                'type' => array_flip(ScheduleDetail::TYPE_MAP)[$arrPostData['type']],
                'is_freeze' => $objUser->user_type_id == User::USER_TYPE_ID_TEACHER ? ScheduleDetail::FREEZE : ScheduleDetail::UN_FREEZE,
                'extension' => is_null($arrExtension) ? null : json_encode($arrExtension),
            ]);
        }
    }

    public function finish($objUser, $iScheduleId)
    {
        $objSchedule = Schedule::where([
            'id' => $iScheduleId
        ])->with('detail')
            ->first();
        if (is_null($objSchedule)) {
            throw new \Exception('计划有改动 编辑失败');
        }
        if ($objUser->user_type_id == User::USER_TYPE_ID_STUDENT) {
            #学生
            if ($objSchedule->creator_uaid != $objUser->ua_id) {
                throw new \Exception('学员只能编辑自己创建的日程');
            }
            if ($objSchedule->detail->is_freeze == ScheduleDetail::FREEZE) {
                throw new \Exception('计划有改动 编辑失败');
            }
        }
        $isFinish = $objSchedule->detail->is_checked ? 0 : 1;
        $isFreeze = $objUser->user_type_id == User::USER_TYPE_ID_TEACHER && $isFinish ? 1 : 0;

        $objSchedule->detail()->update(['is_checked' => $isFinish, 'is_freeze' => $isFreeze]);
        $this->isFinishDayTask($objSchedule);
    }


    public function isFinishDayTask(Schedule $schedule) {
        $list = Schedule::where("schedule_date", $schedule->schedule_date)->where("owner_uaid", $schedule->owner_uaid)->with("detail")->get();
        $has = collect($list)->first(function (Schedule $schedule) {
            $detail = $schedule->detail;
            return $detail && $detail->is_checked == 0;
        }, null);
        if (!$has && $list && count($list) > 0) {
            //全部都完成了
            (new PointService())->dailyPlanPoint($schedule->owner_uaid, $schedule->schedule_date);
        }
    }

    public function delete($objUser, $iScheduleId)
    {
        $objSchedule = Schedule::where([
            'id' => $iScheduleId,
        ])->with('detail')
            ->first();

        if (is_null($objSchedule)) {
            throw new \Exception('计划有改动 删除失败');
        }
        if ($objUser->user_type_id == User::USER_TYPE_ID_STUDENT) {
            #学生
            if ($objSchedule->creator_uaid != $objUser->ua_id) {
                throw new \Exception('学员只能删除自己创建的日程');
            }
            if ($objSchedule->detail->is_freeze == ScheduleDetail::FREEZE) {
                throw new \Exception('计划有改动 删除失败');
            }
        }
        Schedule::where([
            'id' => $iScheduleId
        ])->delete();
        ScheduleDetail::where([
            'schedule_id' => $iScheduleId
        ])->delete();
        $this->isFinishDayTask($objSchedule);
    }

    /**
     * 学生最近已经填写过授课反馈的课节
     * @param String $uaId 学号
     * @param int $hours 有效时间
     */
    public function studentOverLesson(string $uaId, int $hours = 48)
    {
        $now = time();
        $time = date("Y-m-d H:i:s", $now - $hours * 3600);
        $result = DB::connection("cas_crm")
            ->table("new_v_cas_lessonstudent2cas as a")
            ->select(['e.name_cn AS subject_name', 'f.last_name AS teacher_name', 'c.lesson_work', 'b.start_time', 'b.end_time'])
            ->join("cas_lessoninfo as b", "a.lesson_id", "=", "b.id")
            ->join("teachrecord as c", "c.lessonid", "=", "b.lessonid")
            ->join("vtiger_account as d", "d.accountid", "=", "a.student_id")
            ->join("crm_item as e", "e.itemid", "=", "b.subject_id")
            ->join("vtiger_users as f", "f.id", "=", "b.staff_id")
            ->where(["c.submit" => 1, "c.record_status" => 1, "c.deleted" => 0, "d.ua_id" => $uaId])
            ->whereIn("a.l_status", [1, 2, 4])
            ->where(function ($q) use ($time) {
                $q->where("c.update_time", ">=", $time);
                $q->orWhere(function ($q) use ($time) {
                    $q->whereNull("c.update_time");
                    $q->where("c.create_time", ">=", $time);
                });
            })
            ->orderBy("c.create_time")
            ->orderBy("c.update_time")
            ->get();
        $return = [];
        $week = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        foreach ($result as $value) {
            $value = (array)$value;
            $startTime = strtotime(Arr::get($value, 'start_time'));
            $endTime = strtotime(Arr::get($value, 'end_time'));
            $times = date("Y-m-d", $startTime) . " " . $week[date("w", $startTime)] . " " . date("H:i", $startTime) . "-" . date("H:i", $endTime);
            $_return = [
                "from" => 'cas',
                "type" => 'drag',
                "planName" => Arr::get($value, "subject_name", "") . " " . Arr::get($value, "teacher_name", "") . " " . $times, //科目 或者 title
                "planType" => "", //学习 复习
                "description" => "",
                "finish" => "",//10
                "total" => "",//19
                "isStu" => false,
                "remark" => '',
                "teacherName" => Arr::get($value, "teacher_name", ""), //教师名称
                "feedBack" => Arr::get($value, "lesson_work", ""),//作业详情
                "room" => '',
                "campus" => '',
                "subject" => '',
                "startTime" => '',
                "endTime" => '',
                "isChecked" => false,
                "date" => '',
                "allDayChecked" => false,
                "uaId" => '',
                "studentId" => ''
            ];
            $return[] = $_return;
        }
        return $return;
    }

    public function studentTaskFromMillionWords(string $uaId, string $cookies)
    {
        $url = env("WORD_API_URL", "https://word-kf.thinktown.com:8443/index.php") . "/api/task/all";
        $user = User::where("ua_id", $uaId)->first();
        if (!$user) {
            return [];
        }
        $data = [
            'hide' => 1,
            "student_smpid" => $user->user_code,
        ];
        $result = Common::postSimpleCurl($url, $data, ["cookie" => $cookies]);
        $result = json_decode($result, true);
        if (!$result) {
            return [];
        }
        $data = Arr::get($result, "data.tasks", []);
        if (!$data) {
            return [];
        }
        $return = [];
        $type = [1 => "学习", 2 => "复习"];
        foreach ($data as $key => $value) {
            $desc = "";
            $practiceType = "";
            $totalNum = "";
            foreach (Arr::get($value, "combine_arr", []) as $_detail) {
                if (Arr::get($_detail, "status", -1) == 0 && !Arr::get($_detail, "test", null)) {
                    $desc = Arr::get($_detail, "description", "");
                    $practiceType = Arr::get($_detail, "test_method_text", "");
                    $totalNum = Arr::get($_detail, "total_number", "");
                    break;
                }
            }
            if ($totalNum) {
                $totalNum = "({$totalNum}词)";
            }
            $planType = Arr::get($type, Arr::get($_detail, "task_type", 2), "");
            $_return = [
                "from" => 'million',
                "type" => 'drag',
                "planName" => Arr::get($value, "title", "") . " " . Arr::get($value, "finish", 0) . "/" . Arr::get($value, "all", 0) . "  " . $planType . " " . $desc . " " . $totalNum . " " . $practiceType, //科目 或者 title
                "planType" => $planType, //学习 复习
                "description" => $desc,
                "finish" => Arr::get($value, "finish", 0),//10
                "total" => Arr::get($value, "all", 0),//19
                "isStu" => false,
                "remark" => '',
                "teacherName" => '', //教师名称
                "feedBack" => '',//作业详情
                "room" => '',
                "campus" => '',
                "subject" => '',
                "startTime" => '',
                "endTime" => '',
                "isChecked" => false,
                "date" => '',
                "allDayChecked" => false,
                "uaId" => '',
                "studentId" => ''
            ];
            $return[] = $_return;
        }
        return $return;
    }

    public function studentScheduleList(string $studentId, string $cookies, int $hour = 48)
    {
        $result = $this->studentOverLesson($studentId, $hour);
        $result = array_merge($result, $this->studentTaskFromMillionWords($studentId, $cookies));
        return $result;
    }

    /**
     * @param string $strTeacherUaId 教师的ua id
     * @param int $dataScope 0 本人 1本团队所有 2 本人&本团队所有
     * @return array
     */
    public function studentList($strTeacherUaId, $dataScope)
    {
        $arrList = [];
        switch ($dataScope) {
            case 0:
                //本人名下的学生
                $arrGuideStudentList = DB::connection('lms')->select(
                    "select * from (select distinct student_id,ua_id,name,accountstate
from lms_crm_guide_student
join lms_user on lms_crm_guide_student.student_uaid=lms_user.ua_id
where teacher_uaid = ?
and relation in ('Owner', 'Server')
and accountstate in ('意向','在读','自修', '暂无需求', '结束')) as t order by field(t.accountstate, '在读', '意向', '自修', '暂无需求', '结束')", [$strTeacherUaId]
                );
                break;
            case 1:
                //团队内的，剔除本人 todo 先不处理
            default:
                $arrGuideStudentList = DB::select("select * from (select distinct student_id,ua_id,`name`,accountstate
from lms.lms_crm_guide_student
join lms.lms_user on lms_crm_guide_student.student_uaid=lms_user.ua_id
where teacher_uaid in (SELECT a.uaid FROM cas_crm.xkt_user_ids a JOIN cas_crm.vtiger_user2team b on a.crmid = b.userid
WHERE b.teamid = (SELECT teamid FROM cas_crm.xkt_user_ids a JOIN cas_crm.vtiger_user2team b on a.crmid = b.userid where a.uaid = ? LIMIT 1))
and relation in ('Owner', 'Server')
and accountstate in ('意向','在读','自修', '暂无需求', '结束')) as t order by field(t.accountstate, '在读', '意向', '自修', '暂无需求', '结束')", [$strTeacherUaId]);
                break;
        }
        //获取关注的学员列表
        $attentions = DB::select("SELECT u_ua_id,s_ua_id FROM sc_attention_record WHERE u_ua_id = ? order by updated_at desc", [$strTeacherUaId]);
        $teachers = [$strTeacherUaId];
        if ($dataScope == 2) {
            $teachers = DB::select("SELECT a.uaid FROM cas_crm.xkt_user_ids a JOIN cas_crm.vtiger_user2team b on a.crmid = b.userid
WHERE b.teamid = (SELECT teamid FROM cas_crm.xkt_user_ids a JOIN cas_crm.vtiger_user2team b on a.crmid = b.userid where a.uaid = ? LIMIT 1)", [$strTeacherUaId]);
            $teachers = collect($teachers)->map(function ($item) { return $item->uaid;})->toArray();
        }
        //获取名下教师
        //获取附加的学员列表
        $planAttachStudents = [];
        $sql = "SELECT * FROM (SELECT distinct a.s_ua_id as ua_id,c.name,accountstate  FROM sc_plan_attach_user a JOIN lms.lms_crm_guide_student b ON a.s_ua_id=b.student_uaid JOIN lms.lms_user c ON b.student_uaid=c.ua_id WHERE b.accountstate IN ('意向','在读','自修', '暂无需求', '结束') AND a.u_ua_id in (%s)) as t order by field(t.accountstate, '在读', '意向', '自修', '暂无需求', '结束')";
        $s = "";
        foreach ($teachers as $teacher) {
            $s .= ",?";
        }
        $s = substr($s, 1);
        $sql = sprintf($sql, $s);
        if ($teachers) {
            $planAttachStudents = DB::select($sql, $teachers);
        }
        $arrGuideStudentList = array_merge($arrGuideStudentList, $planAttachStudents);
        $attentionList = [];
        foreach ($arrGuideStudentList as $item) {
            $student = [
                'student_uaid' => $item->ua_id,
                'student_name' => $item->name,
                'attention' => false,
                'accountstate' => $item->accountstate
            ];
            $match = false;
            foreach ($attentions as $attention) {
                if ($item->ua_id == $attention->s_ua_id) {
                    $student['attention'] = true;
                    $attentionList[$item->ua_id] = $student;
                    $match = true;
                    break;
                }
            }
            if ($match) {
                continue;
            }
            $arrList[$item->ua_id] = $student;
        }
        $sortValues = [];
        foreach ($attentions as $attention) {
            if (Arr::has($attentionList, $attention->s_ua_id)) {
                $sortValues[$attention->s_ua_id] = Arr::get($attentionList, $attention->s_ua_id);
            }
        }
        $arrList = array_merge($sortValues, $arrList);
        return array_values($arrList);
    }

    private function lesson($strStudentUaId, $date)
    {
        $s = $date . " 00:00:00";
        $e = $date . " 23:59:59";
        return DB::connection('cas_crm')->select("SELECT
	c.start_time,c.end_time,c.id,c.lessonid,
	e.name_cn AS subject_name,
	g.code AS room_name,
	h.campus,
	f.last_name AS teacher_name
FROM
	new_v_cas_lessonstudent2cas AS a
	JOIN vtiger_account AS b ON b.accountid = a.student_id
	JOIN cas_lessoninfo AS c ON c.id = a.lesson_id
	JOIN crm_item AS e ON e.itemid = c.subject_id
	JOIN vtiger_users AS f ON f.id = c.staff_id
	LEFT JOIN cas_rooms g ON g.id = c.room_id
	LEFT JOIN v_campus h ON h.campusid = c.teach_campus
WHERE
a.l_status in (1,2,4) and
c.deleted = 0 and
c.courses_status = 1 and
c.start_time >= ? AND
c.start_time <= ? and
b.ua_id = ? union
SELECT c.start_time,c.end_time,c.id,c.lessonid,
	'模考' AS subject_name,
	g.code AS room_name,
	h.campus,
	f.last_name AS teacher_name FROM
crm_v_class_student AS a
	JOIN vtiger_account AS b ON b.accountid = a.student_id
	JOIN cas_lessoninfo_extra AS c ON c.class_id = a.class_id
	LEFT JOIN vtiger_users AS f ON f.id = c.staff_id
	LEFT JOIN cas_rooms g ON g.id = c.room_id
	LEFT JOIN v_campus h ON h.campusid = c.teach_campus
	WHERE
	a.`status` in (2,3,4,5,6) and
	c.deleted = 0 and
c.start_time >= ? AND
c.start_time <= ? and
b.ua_id = ?", [$s, $e, $strStudentUaId, $s, $e, $strStudentUaId]);
    }

    /**
     * 每日计划关注学生
     * @param $objUser
     * @param $strStudentUaId
     */
    public function attention($objUser, $strStudentUaId, $attention = true)
    {
        $uaId = $objUser->ua_id;
        //判断是否是该用户的学生
        //todo 有2种情况 1是本人 2 是团队下 先不管
        //是否已经关注
        $isAttention = DB::select("select * from sc_attention_record where u_ua_id = ? and s_ua_id = ?", [$uaId, $strStudentUaId]);
        if ($attention && $isAttention) {
            throw new \Exception("你已经关注该学生");
        }
        if (!$attention) {
            DB::select("delete from sc_attention_record where u_ua_id = ? and s_ua_id = ?", [$uaId, $strStudentUaId]);
            return true;
        }
        DB::select("insert into sc_attention_record (u_ua_id, s_ua_id) VALUES (?,?)", [$uaId, $strStudentUaId]);
        return true;
    }

    /**
     * 学生详情
     * @param $objUser
     * @param $strStudentUaId
     */
    public function studentInfo($objUser, $strStudentUaId)
    {
        $r = [
            "studentName" => "",
            "studentUaId" => $strStudentUaId,
            "canAssign" => false,
            "comment" => "",
            'supervisor' => "",
            'users' => [],
        ];
        $arrGuideStudentList = DB::connection('lms')->select(
            "select distinct student_id,teacher_uaid,name
from lms_crm_guide_student
join lms_user on lms_crm_guide_student.teacher_uaid=lms_user.ua_id
where student_uaid = ?
and relation in ('Owner', 'Server')
and accountstate in ('意向','在读','自修', '暂无需求', '结束')", [$strStudentUaId]);
        $guides = [];
        foreach ($arrGuideStudentList as $guide) {
            $guides[] = $guide->name;
        }
        $isMyStudent = $this->isMyStudent($objUser, $strStudentUaId);
        if ($isMyStudent) {
            $r['canAssign'] = true;
        }
        $r['supervisor'] = implode(",", $guides);
        $studentInfo = DB::connection('lms')->select("select * from lms_user where ua_id = ?", [$strStudentUaId]);
        if ($studentInfo) {
            $studentInfo = $studentInfo[0];
            $r['studentName'] = $studentInfo->name;
        }
        //获取comment
        $record = DB::select("select * from sc_plan_attach_record where s_ua_id = ?", [$strStudentUaId]);
        if ($record) {
            $record = $record[0];
            $r['comment'] = $record->memo;
        }
        //拿员工
        $sql = "SELECT a.ua_id,a.`name`,a.user_code, b.s_ua_id as checked FROM ua.ua_users a
LEFT JOIN student_center.sc_plan_attach_user b on b.u_ua_id = a.ua_id and b.s_ua_id = ?
WHERE user_type_id = 1 and user_status = 1";
        $users = DB::select($sql, [$strStudentUaId]);
        foreach ($users as $index => &$user) {
            if ($user->checked) {
                $user->checked = true;
            } else {
                $user->checked = false;
            }
        }
        $r['users'] = $users;

        return $r;
    }

    /**
     * 分配交接人
     * @param $objUser
     * @param $data
     * @return bool
     * @throws \Exception
     */
    public function assignPlanAttachUser($objUser, $data)
    {
        $isMyStudent = $this->isMyStudent($objUser, $data['studentId']);
        $attachUser = DB::select("select * from sc_plan_attach_user where s_ua_id = ? and u_ua_id = ?", [$data['studentId'], $objUser->ua_id]);
        if (!$isMyStudent && !$attachUser) {
            throw new \Exception("不是该学生的督导和计划交接人");
        }
        //提前修改备注
        $record = DB::select("select * from sc_plan_attach_record where s_ua_id = ?", [$data['studentId']]);
        if ($record) {
            DB::select("update sc_plan_attach_record set memo = ?, u_ua_id = ? where s_ua_id = ?", [$data['comment'], $objUser->ua_id, $data['studentId']]);
        } else {
            DB::select("insert into sc_plan_attach_record (s_ua_id, u_ua_id, memo) VALUES (?,?,?)", [$data['studentId'], $objUser->ua_id, $data['comment']]);
        }
        if (!$isMyStudent) {
            //非督导只能修改备注
            return true;
        }
        if (is_string($data['attachUser'])) {
            $data['attachUser'] = explode(",", $data['attachUser']);
        }
        DB::select("delete from sc_plan_attach_user where s_ua_id = ?", [$data['studentId']]);
        if (count($data['attachUser']) == 0) {
            return true;
        }
        $sql = "insert into sc_plan_attach_user (s_ua_id, u_ua_id) VALUES ";
        $bindings = [];
        $count = count($data['attachUser']);
        foreach ($data['attachUser'] as $index => $value) {
            $sql .= "(?,?)";
            if ($count != $index + 1) {
                $sql .= ",";
            }
            $bindings[] = $data['studentId'];
            $bindings[] = $value;
        }
        DB::select($sql, $bindings);
        return true;
    }

    /**
     * 是否是我的学生
     * @param $objUser
     * @param $student
     */
    public function isMyStudent($objUser, $student)
    {
        $isTopMulMp = DB::select('SELECT count(*) as t FROM `top`.xkt_role a JOIN `top`.xkt_user_role b on a.role_id = b.role_id
JOIN `top`.v_crm_user c on c.user_id = b.user_id
WHERE c.ua_id = ? AND a.name in (?, ?)', [$objUser->ua_id, "督导", "MP"]);
        $isTopMulMp = $isTopMulMp ? $isTopMulMp[0]->t == 2 : false;

        if (!$isTopMulMp) {
            $arrGuideStudentList = DB::connection('lms')->select(
                "select 1
from lms_crm_guide_student
where teacher_uaid = ?
and student_uaid = ?
and relation in ('Owner', 'Server')
and accountstate in ('意向','在读','自修', '暂无需求', '结束')", [$objUser->ua_id, $student]);
        } else {
            $arrGuideStudentList = DB::select("select 1
from lms.lms_crm_guide_student
where teacher_uaid in (SELECT a.uaid FROM cas_crm.xkt_user_ids a JOIN cas_crm.vtiger_user2team b on a.crmid = b.userid
WHERE b.teamid = (SELECT teamid FROM cas_crm.xkt_user_ids a JOIN cas_crm.vtiger_user2team b on a.crmid = b.userid where a.uaid = ? LIMIT 1))
and relation in ('Owner', 'Server')
and student_uaid = ?
and accountstate in ('意向','在读','自修', '暂无需求', '结束')", [$objUser->ua_id, $student]);
        }
        return $arrGuideStudentList ? true : false;
    }

    public function schedule_list($data = [])
    {
        $start = Arr::get($data, "start", "");
        $end = Arr::get($data, "end", "");
        $month = (bool) Arr::get($data, "month", false);
        $teacher = Arr::get($data, "teacher");
        $type = Arr::get($data, "type", "all") ?: "all"; // all cas custom
        $strStudentUaId = Arr::get($data, "uaid");
        //教师不为空只拿课表
        $arrScheduleList = $this->_scheduleListOfTimeRangeCustom($strStudentUaId, $start, $end, $teacher ? "cas" : $type)->groupBy("schedule_date");
        $arrLessonList = $this->_scheduleListOfTimeRangeLesson($strStudentUaId, $start, $end, $type, $teacher)->groupBy(function ($item) {
            return date("Y-m-d", strtotime($item->start_time));
        });
        return $month ? $this->scheduleListOfDay($start, $end, [$arrLessonList, $arrScheduleList]) : $this->scheduleListOfHour($start, $end, [$arrLessonList, $arrScheduleList]);
    }

    public function scheduleListOfDay($start, $end, array $list = []): array
    {
        //先根据日期聚合

        $data = $this->scheduleListOfHour($start, $end, $list);
        $_data = [];
        foreach ($data as $day => $item) {
            $_content = collect();
            foreach ($item as $content) {
                $_content = $_content->merge(Arr::get($content, "content", []));
            }
            $_data[$day] = $_content;
        }
        return $_data;
    }
    private function formatScheduleInfoWithHour($item, $begin, $end) {
        switch ($item) {
            case $item instanceof Schedule:
                $current = new Carbon($item['schedule_date'] . ' ' . $item['begin']);
                break;
            case $item instanceof RelationOfCasCrmModel:
                $current = new Carbon($item->start_time);
                break;
            default:
                $current = null;
                 break;
        }
        if (!$current) {
            return false;
        }
        if ($begin->timestamp <= $current->timestamp && $current->timestamp <= $end->timestamp) {
            return $this->formatScheduleInfo($item);
        }
        return false;

    }

    private function formatScheduleInfo($item): array
    {
        switch ($item) {
            case $item instanceof Schedule:
                return $this->formatScheduleLocal($item);
            case $item instanceof RelationOfCasCrmModel:
                return $this->formatScheduleLesson($item);
            default:
                return [];
        }
    }

    private function formatScheduleLocal(Schedule $item)
    {
        return [
            'id' => $item->id,
            'allDayChecked' => $item->all_day,
            'startTime' => Carbon::create($item->begin)->format('G:i'),
            'endTime' => Carbon::create($item->end)->format('G:i'),
            'from' => ScheduleDetail::FORM_MAP[$item->detail->from],
            'type' => ScheduleDetail::TYPE_MAP[$item->detail->type],
            'planName' => $item->detail->name,
            'remark' => $item->detail->remark,
            'isChecked' => $item->detail->is_checked,
            'finish' => $item->detail->finish,
            'total' => $item->detail->total,
            'feedBack' => $item->detail->feedBack,
            'campus' => $item->detail->campus,
            'room' => $item->detail->room,
            'subject' => $item->detail->subject,
            'isStu' => $item->creator->user_type_id == User::USER_TYPE_ID_STUDENT,
            'editable' => false,
            'teacherName' => $item->detail->teacher_name,
        ];
    }
    private function formatScheduleLesson(RelationOfCasCrmModel $item)
    {
        return [
            'id' => 0,
            'allDayChecked' => 0,
            'startTime' => Carbon::create($item->start_time)->format('G:i'),
            'endTime' => Carbon::create($item->end_time)->format('G:i'),
            'from' => 'cas',
            'type' => 'class',
            'planName' => '',
            'remark' => null,
            'isChecked' => null,
            'finish' => null,
            'total' => null,
            'feedBack' => null,
            'campus' => $item->campus,
            'room' => $item->room_name,
            'subject' => $item->subject_name,
            'teacherName' => $item->teacher_name,
            'isStu' => null,
            'editable' => false,
        ];
    }

    /**
     * 周视图
     * @return mixed
     */
    public function scheduleListOfHour($start, $end, array $list = [])
    {
        //先根据日期聚合
        $data = [];
        $start = strtotime($start);
        $end = strtotime($end);
        while ($start <= $end) {
            $now = date("Y-m-d", $start);
            $data[$now] = Schedule::TIME_LIME;
            //loop
            foreach ($data[$now] as &$range) {
                $_begin = new Carbon($now . ' ' . $range['startTime']);
                $_end = new Carbon($now . ' ' . $range['endTime']);
                $range['content'] = [];
                foreach ($list as $_list) {
                    $_item = $_list->get($now, collect());
                    foreach ($_item as $item) {
                        $find = $this->formatScheduleInfoWithHour($item, $_begin, $_end);
                        if ($find !== false) {
                            $range['content'][] = $find;
                        }
                    }
                }
            }
            $start += 86400;
        }
        return $data;
    }

    /**
     * 区间内的学生的计划
     * @param $uaId
     * @param $start
     * @param $end
     * @param $type
     * @return mixed
     */
    private function _scheduleListOfTimeRangeCustom($uaId, $start, $end, $type)
    {
        //cas 只查课表
        if ('cas' == $type) {
            return collect();
        }
        return Schedule::with('detail')->with('creator')
            ->where("schedule_date", ">=", $start)
            ->where("schedule_date", "<=", $end ?: $start)
            ->where("owner_uaid", $uaId)
            ->get();
    }
    private function _scheduleListOfTimeRangeLesson($uaId, $start, $end, $type, $teacher)
    {
        if ('custom' == $type) {
            return collect();
        }
        //一人班 的 学生课
        $lessonType = $teacher ? "非模考" : 'all';
        $start = $start . " 00:00:00";
        $end = $end . " 23:59:59";
        return RelationOfCasCrmModel::lessons($uaId, $start, $end, $teacher ? explode(",", $teacher) : [], $lessonType);
    }


    public function teachers()
    {
        $ua_id = Auth::user()->ua_id;
        return RelationOfCasCrmModel::teachers($ua_id);
    }


}
