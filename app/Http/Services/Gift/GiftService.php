<?php

namespace App\Http\Services\Gift;

use App\Http\Services\Point\PointService;
use App\Models\FastModel\FastQueryModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class GiftService
{

    public function sendGift($data = [])
    {
        //todo
        //["from": "CAST", "belong_id":1, "target_ua_id":"xkt123", "gifts" : [1=>1,2=>1,3=>1]]

        $from = Arr::get($data, "from", "");
        if ($from == "CAST") {
            return $this->sendGiftWithCast($data);
        }
        return "赠送失败";
    }

    public function sendGiftWithCast($data = []) {
        $belong_id = Arr::get($data, "belong_id");
        $gifts = Arr::get($data, "gifts", []);
        $error1 = "赠送失败";
        //check belong_id
        $model = FastQueryModel::teacherEvaluate()
            ->where("id", $belong_id)
            ->first();
        if (!$model) {
            return $error1;
        }
        $owner = optional(Auth::user())->ua_id;
        if ($model->ua_id != $owner) {
            return $error1;
        }
        if ($model->status == 1) {
            return $error1;
        }

        //校验礼物有效、积分可用
        $configs = collect($gifts)->keys();
        $configs = FastQueryModel::configOfGifts()->whereIn("id", $configs)->get();
        if (!$configs || $configs->isEmpty()) {
            return $error1;
        }
        $needScore = 0;
        $reason = "赠送老师感谢礼";
        $insert = [];
        $time = date("Y-m-d H:i:s");
        foreach ($configs as $config) {
            $detail = json_decode($config->configs, true);
            $name = Arr::get($detail, "name", "");
            $score = (int) Arr::get($detail, "score", 0);
            foreach ($gifts as $configId => $num) {
                $num = (int) $num;
                if ($num <= 0) {
                    continue;
                }
                if ($configId == $config->id) {
                    $_score = $score * $num;
                    $needScore += $_score;
                    $reason .= "《{$name}》*$num ";
                    $insert[] = [
                        "config_id" => $config->id,
                        "belong_id" => $belong_id,
                        "belong_type" => "CAST",
                        "target_ua_id" => $model->target_ua_id,
                        "ua_id" =>  $owner,
                        "gift_num" => $num,
                        "gift_score" => $_score,
                    ];
                }

            }
        }
        $point = new PointService();
        $result = $point->sendGift($owner, $needScore, trim($reason));
        if (!$result) {
            return "积分不足";
        }
        //插入赠送记录
        FastQueryModel::giftRecord()->insert($insert);
        return $point->getStudentPoint($owner);
    }

}
