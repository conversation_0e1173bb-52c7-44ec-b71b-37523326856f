<?php

namespace App\Http\Services;

use App\Models\Schedule;
use App\Models\ScheduleDetail;
use App\User;
use Carbon\Carbon;

class MiniProgramService
{
    public function scheduleList($strStudentUaId, $strStartDate, $strEndDate)
    {
        $arrDateList = [];

        $strStartTimestamp = strtotime($strStartDate);
        $strEndTimestamp = strtotime($strEndDate) + 86400;
        $keyTime = $strStartTimestamp;
        while ($keyTime < $strEndTimestamp) {
            $currentDate = date('Y-m-d', $keyTime);
            $arrDateList[$currentDate] = [];
            $keyTime += 86400;
        }
        $arrScheduleList = Schedule::with('detail')->with('creator')
            ->where([
                'owner_uaid' => $strStudentUaId,
            ])
            ->whereBetween('schedule_date', [$strStartDate, $strEndDate])
            ->get();
        foreach ($arrDateList as $date => &$arrDayList) {
            foreach ($arrScheduleList as $item) {
                if ($item['schedule_date'] != $date) {
                    continue;
                }
                $isEditable = false;
                if ($item['owner_uaid'] == $item['creator_uaid'] && !$item->detail->is_freeze) {
                    $isEditable = true;
                }
                $arrDayList[] = [
                    'id' => $item->id,
                    'ua_id' => $strStudentUaId,
                    'allDayChecked' => $item->all_day,
                    'startTime' => Carbon::create($item->begin)->format('G:i'),
                    'endTime' => Carbon::create($item->end)->format('G:i'),
                    'from' => ScheduleDetail::FORM_MAP[$item->detail->from],
                    'type' => ScheduleDetail::TYPE_MAP[$item->detail->type],
                    'planName' => $item->detail->name,
                    'remark' => $item->detail->remark,
                    'isChecked' => $item->detail->is_checked,
                    'finish' => $item->detail->finish,
                    'total' => $item->detail->total,
                    'feedBack' => $item->detail->feedBack,
                    'campus' => $item->detail->campus,
                    'room' => $item->detail->room,
                    'subject' => $item->detail->subject,
                    'isStu' => $item->creator->user_type_id == User::USER_TYPE_ID_STUDENT,
                    'editable' => $isEditable,
                    'teacherName' => $item->detail->teacher_name,
                ];
            }
        }
        return $arrDateList;
    }

    public function detail($iScheduleId)
    {
        $objSchedule = Schedule::with('detail')->with('creator')
            ->where([
                'id' => $iScheduleId
            ])
            ->first();
        $isEditable = false;
        if ($objSchedule['owner_uaid'] == $objSchedule['creator_uaid'] && !$objSchedule->detail->is_freeze) {
            $isEditable = true;
        }
        return [
            'id' => $objSchedule->id,
            'ua_id' => $objSchedule->owner_uaid,
            'allDayChecked' => $objSchedule->all_day,
            'schedule_date' => $objSchedule->schedule_date,
            'startTime' => Carbon::create($objSchedule->begin)->format('G:i'),
            'endTime' => Carbon::create($objSchedule->end)->format('G:i'),
            'from' => ScheduleDetail::FORM_MAP[$objSchedule->detail->from],
            'type' => ScheduleDetail::TYPE_MAP[$objSchedule->detail->type],
            'planName' => $objSchedule->detail->name,
            'remark' => $objSchedule->detail->remark,
            'isChecked' => $objSchedule->detail->is_checked,
            'finish' => $objSchedule->detail->finish,
            'total' => $objSchedule->detail->total,
            'feedBack' => $objSchedule->detail->feedBack,
            'campus' => $objSchedule->detail->campus,
            'room' => $objSchedule->detail->room,
            'subject' => $objSchedule->detail->subject,
            'isStu' => $objSchedule->creator->user_type_id == User::USER_TYPE_ID_STUDENT,
            'editable' => $isEditable,
            'teacherName' => $objSchedule->detail->teacher_name,
        ];
    }
}
