<?php

namespace App\Http\Services\Evaluate;

use App\Http\Services\Point\PointService;
use App\Models\FastModel\FastQueryModel;
use App\Utils\DateUtil;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class EvaluateService
{

    public function ifTeacherEval($uaId): bool
    {
        //1. config 存在 并处于周期内
        return (bool) $this->teacherEvalActiveConfigMixed(null, $uaId);
    }

    public function ifShowTeacherEvalButton($uaId): bool
    {
        //1. config 存在 并处于周期内
        return (bool) $this->teacherEvalActiveConfigMixed(null, $uaId, false);
    }


    public function studentHasUnfinishedEval($config, $uaId, $needCheckOpen = true): bool
    {
        $dataBelong = $this->configDataBelong($config);
        $model = FastQueryModel::teacherEvaluateStudentRecord()
            ->where("belong_date", $this->_generalDataBelong($dataBelong))
            ->where("ua_id", $uaId)
            ->first();
        if (!$model) {
            //没有记录说明没生成 不需要调研
            return false;
        }
        //指定配置下 配置code
        $recordInfos = FastQueryModel::teacherEvaluateRecordInfo()
            ->where("belong_id", $model->id)
            ->where("config_id", $config["id"])
            ->where("type", "student")
            ->whereIn("code", $this->configEvalDataCode($config))
            ->get();
        if ($needCheckOpen && !$this->needOpenEval($config, optional($recordInfos->first())->last_open_time)) {
            return false;
        }
        return FastQueryModel::teacherEvaluate()->where("belong_student_eval_id", $model->id)->whereIn("belong_record_id", $recordInfos->pluck("id")->toArray())
            ->where("status", 0)->exists();
    }
    public function studentHasEval($config, $uaId): bool
    {
        $dataBelong = $this->configDataBelong($config);
        $model = FastQueryModel::teacherEvaluateStudentRecord()
            ->where("belong_date", $this->_generalDataBelong($dataBelong))
            ->where("ua_id", $uaId)
            ->first();
        if (!$model) {
            //没有记录说明没生成 不需要调研
            return false;
        }
        //指定配置下 配置code
        $recordInfos = FastQueryModel::teacherEvaluateRecordInfo()
            ->where("belong_id", $model->id)
            ->where("config_id", $config["id"])
            ->where("type", "student")
            ->whereIn("code", $this->configEvalDataCode($config))
            ->get();
        return FastQueryModel::teacherEvaluate()->where("belong_student_eval_id", $model->id)->whereIn("belong_record_id", $recordInfos->pluck("id")->toArray())
            ->where("status", 1)->exists();
    }
    public function needOpenEval($config, $lastOpenTime = null): bool
    {
        $evalWindows = Arr::get($config, "evalWindows", "");
        if (!$evalWindows) {
            return false;
        }
        if ($lastOpenTime == null) {
            return true;
        }
        $evalWindows = Arr::get($config, "evalWindows", "");
        if (Str::endsWith($evalWindows, "day")) {
            $evalWindows = (int) Str::replaceLast("day", "", $evalWindows);
            return time() - strtotime($lastOpenTime) >= $evalWindows * 86400;
        }
        if (Str::endsWith($evalWindows, "hour")) {
            $evalWindows = (int) Str::replaceLast("hour", "", $evalWindows);
            return time() - strtotime($lastOpenTime) >= $evalWindows * 3600;
        }
        if (Str::endsWith($evalWindows, "min")) {
            $evalWindows = (int) Str::replaceLast("min", "", $evalWindows);
            return time() - strtotime($lastOpenTime) >= $evalWindows * 60;
        }
        //todo 其它周期
        return false;
    }


    public function configDataBelong($config, $key = "dataBelong")
    {
        $dataBelong = Arr::get($config, $key);
        $time = time();
        if ($dataBelong == "month") {
            return [date("Y-m-01", $time)];
        } else if (is_array($dataBelong)) {
            //简单的返回
            return $dataBelong;
        }
        else {
            if ($dataBelong == "lastFinanceMonth") {
                $time = strtotime("-1 month", time());
            }
            //默认是财务月
            $nowD = (int) date("d", $time);
            if ($nowD >= 21) {
                return [date("Y-m-01", strtotime("+1 month", $time))];
            } else {
                return [date("Y-m-01", $time)];
            }
        }
    }

    public function configDataBelongStr($config, $key = "dataBelong")
    {
        return $this->_generalDataBelong($this->configDataBelong($config, $key));
    }

    public function configUserDataBelong($config)
    {
        return $this->configDataBelong($config, "userDataBelong");
    }

    public function configDataCode($config) {
        return Arr::get($config, "dataCode", "A");
    }
    public function configEvalDataCode($config) {
        return Arr::get($config, "evalDataCode", []);
    }


    /**
     * 符合当下配置的
     * @return mixed
     */
    public function teacherEvalActiveConfig($configId = null, $needMix = false)
    {
        $now = time();
        //默认拿最新的一条
        $models = FastQueryModel::configOfTeacherEvaluate()->when($configId, function ($q) use ($configId){
            $q->where("id", $configId);
        })->orderBy("id", "DESC")->get();
        $configs = [];
        foreach ($models as $model) {
            $config = json_decode($model->configs, true);
            if (!$this->inEval($config, $now)) {
                continue;
            }
            $config["id"] = $model->id;
            if (!$needMix) {
                return $config;
            }
            $configs[] = $config;
        }
        return !$needMix ? false : (!$configs ? false : $configs);
    }

    public function teacherEvalActiveConfigMixed($configId, $uaId = "", $checkOpen = true)
    {
        $configs = $this->teacherEvalActiveConfig($configId, true);
        if ($configs === false) {
            return false;
        }

        foreach ($configs as $config) {
            //找到最近一条未完成的调研
            if ($this->studentHasUnfinishedEval($config, $uaId, $checkOpen)) {
                return $config;
            }
        }
        return false;
    }

    protected function inEval($config = [], $now = null): bool
    {
        $now = $now ?: time();
        $openTime = strtotime(Arr::get($config, "openTime"));
        $closeTime = strtotime(Arr::get($config, "closeTime"));
        $evalType = Arr::get($config, "evalType", "everyMonth");
        $evalStart = Arr::get($config, "evalStart");
        $evalEnd = Arr::get($config, "evalEnd");
        //未开
        if ($openTime > $now) {
            return false;
        }
        //已关
        if ($closeTime < $now) {
            return false;
        }
        $formatEvalTime = $this->formatEvalTime($evalType, $evalStart, $evalEnd, $now);
        if (!$formatEvalTime) {
            return false;
        }

        //不在周期内
        if ($formatEvalTime[0] > $now || $formatEvalTime[1] < $now) {
            return false;
        }
        //附加 判断类型
        $evalRangeType = Arr::get($config, "evalRangeType", "");
        if (!$evalRangeType) {
            return true;
        }
        $evalRangeType = is_string($evalRangeType) ? [$evalRangeType] : (array) $evalRangeType;

        foreach ($evalRangeType as $type) {
            switch ($type) {
                case "weekend":
                    //周末
                    $d = date("w", $now);
                    if ($d == 0 || $d == 6) {
                        return true;
                    }
                    break;
                case "workday":
                    //工作日
                    $d = date("w", $now);
                    if ($d != 0 && $d != 6) {
                        return true;
                    }
                    break;
                case "sunday":
                    //周日
                    $d = date("w", $now);
                    if ($d == 0) {
                        return true;
                    }
                    break;
                case "monday":
                    //周一
                    $d = date("w", $now);
                    if ($d == 1) {
                        return true;
                    }
                    break;
                case "tuesday":
                    //周二
                    $d = date("w", $now);
                    if ($d == 2) {
                        return true;
                    }
                    break;
                case "wednesday":
                    //周三
                    $d = date("w", $now);
                    if ($d == 3) {
                        return true;
                    }
                    break;
                case "thursday":
                    //周四
                    $d = date("w", $now);
                    if ($d == 4) {
                        return true;
                    }
                    break;
                case "friday":
                    //周五
                    $d = date("w", $now);
                    if ($d == 5) {
                        return true;
                    }
                    break;
                case "saturday":
                    //周六
                    $d = date("w", $now);
                    if ($d == 6) {
                        return true;
                    }
                    break;
                default:
                    break;

            }
        }


        return false;
    }

    protected function formatEvalTime($evalType, $evalStart, $evalEnd, $now = null) {
        $now = $now ?: time();
        $ym = date("Y-m-", $now);
        if ($evalType == "everyMonth") {
            if ($evalStart == "monthStart") {
                $evalStart = "01 00:00:00";
            }
            //月底
            if ($evalEnd == "monthEnd") {
                $evalEnd = $this->getLastDayOfMonth($ym) . " 23:59:59";
            }
            return [strtotime($ym . $evalStart), strtotime($ym . $evalEnd)];
        } else if ($evalType == "assignTime") {
            //指定时间
            return [strtotime($evalStart), strtotime($evalEnd)];
        } else if ($evalType == "everyFinanceMonth") {
            $yme = date("Y-m-", $now);
            if ($evalStart == "monthStart") {
                $evalStart = "21 00:00:00";
            }
            //月底
            if ($evalEnd == "monthEnd") {
                $evalEnd = "20 23:59:59";
            }
            if (Str::startsWith($evalEnd, "next")) {
                $end = strtotime("+1 month", $now);
                $yme = date("Y-m-", $end);
                $evalEnd = Str::replaceFirst("next", "", $evalEnd);
            }
            return [strtotime($ym . $evalStart), strtotime($yme . $evalEnd)];
        }
        return false;
    }

    function getLastDayOfMonth($ym) {
        // 将日期设置为下个月的 1 号
        $nextMonth = strtotime("{$ym}01 +1 month");
        // 减去一天，得到上个月的最后一天
        return date("d", strtotime("-1 day", $nextMonth));
    }


    public function studentEvalData($config, $uaId, $now = null): array
    {
        $now = $now ?: time();
        $relation = Arr::get($config, "relation");
        $configId = Arr::get($config, "id");
        $data = $this->dealRelationData($uaId, $relation, $now, $config);
        //todo 获取周期内数据
        return $data;
    }

    public function dealRelationData($uaId, $relation, $now, $config): array
    {
        $relations = explode("|", $relation);
        $data = [];
        foreach ($relations as $relation) {
            if ($relation == "studentWithTeacher") {
                $data["studentWithTeacher"] = $this->relationOfStudentWithTeacher($uaId, $config, $now);
            }else if ($relation == "studentWithAbroad") {
                //留学的
                $data["studentWithAbroad"] = $this->relationOfStudentWithAbroad($uaId, $config, $now);
            }
        }
        return $data;
    }


    public function relationOfStudentWithAbroad($uaId, $config, $now): array
    {
        $formatDateTime = $this->formatDateTime($config, $now);
        if (!$formatDateTime) {
            return [];
        }
        $crmId = null;
        if ($uaId) {
            $crmId = $this->uaIdToCrmId($uaId);
            if (!$crmId) {
                return [];
            }
        }
        $models = FastQueryModel::pasStudentWithAbroad()
            ->selectRaw("DISTINCT (select ua_id from cas_crm.vtiger_account where accountid = cas_crm.sms_order.studentid) as student_ua_id,(case role when 'PUD' then 'CAE' when 'PUS' then 'CPS' else role end) as catename,'' as lessonname, staffid,staffname,'other' as counts, (select ua_id from cas_crm.v_crm_user where user_id = cas_crm.sms_order_role.staffid limit 1) as ua_id")
            ->join(DB::raw("cas_crm.sms_order_role"), DB::raw("cas_crm.sms_order_role.orderid"), "=",  DB::raw("cas_crm.sms_order.id"))
            ->where(DB::raw("cas_crm.sms_order_role.range"), "partial")
            ->whereIn(DB::raw("cas_crm.sms_order_role.role"), ['PUD','PUS'])
            ->where(DB::raw("cas_crm.sms_order_role.staffid"), ">", 1)
            ->whereIn(DB::raw("cas_crm.sms_order.proitem"), ['TQ6','TQ7','TQ8','TQ9','TQ10','TQ11','TQ12','TAU-P','TAU-E','TAU-T','HAU-E'])
            ->where(DB::raw("cas_crm.sms_order_role.end"), ">=", $formatDateTime[0])
            ->where(DB::raw("cas_crm.sms_order_role.start"), "<=", $formatDateTime[1])
            ->when($crmId, function ($query) use ($crmId) {
                $query->where(DB::raw("cas_crm.sms_order.studentid"), $crmId);
            })
            ->get();
        return $models->toArray();
    }

    public function relationOfStudentWithTeacher($uaId, $config, $now): array
    {

        $formatDateTime = $this->formatDateTime($config, $now);
        if (!$formatDateTime) {
            return [];
        }
        //uaId to crmId
        $crmId = null;
        if ($uaId) {
            $crmId = $this->uaIdToCrmId($uaId);
            if (!$crmId) {
                return [];
            }
        }
        $belongDate = $this->configDataBelongStr($config);
        $now = strtotime($belongDate);
        //check
        $nowMo = date("Y-m-01", $now);
        $lastMo = date("Y-m-01", strtotime("-1 month", $now));
        //todo 可能会慢
        $models = FastQueryModel::pasStudentWithTeacher()
            ->selectRaw("vc.ua_id as student_ua_id,catename,lessonname,catename,staffname,1 as counts, vcu.ua_id as ua_id")
            ->join(DB::raw("cas_crm.vtiger_account as vc"), DB::raw("vc.accountid"), "=", DB::raw("cas_crm.pas_ls.studentid"))
            ->join(DB::raw("cas_crm.v_crm_user as vcu"), DB::raw("vcu.user_id"), "=", DB::raw("cas_crm.pas_ls.staffid"))
            ->where("lessondate", ">=", $formatDateTime[0])
            ->where("lessondate", "<=", $formatDateTime[1])
            ->when($crmId, function ($query) use ($crmId) {
                $query->where("studentid", $crmId);
            })
            ->where("lessonstatus", 1)
            ->whereNotExists(function (Builder $q) use ($nowMo, $lastMo) {
                $q->select(DB::raw(1))
                    ->from('teacher_evaluate')
                    ->where("status", 1)
                    ->whereColumn("ua_id", DB::raw("vc.ua_id"))
                    ->whereColumn("target_ua_id", DB::raw("vcu.ua_id"))
                    ->whereIn("belong_date", [$nowMo, $lastMo]);
            })
            ->get();
        return $models->toArray();
    }

    public function uaIdToCrmId($uaId)
    {
        $model = FastQueryModel::crmStudent()->where("ua_id", $uaId)->first();
        return $model ? $model->accountid : null;
    }


    private function formatDateTime($config, $now)
    {
        $lastMonth = date("Y-m-", strtotime("-1 month", $now));
        $dataType = Arr::get($config, "dataType");
        if ($dataType == "lastFinanceMonth") {
            $nowDay = (int)date("d", $now);
            if ($nowDay >= 21) {
                $nowMonth = date("Y-m-", $now);
                return [$lastMonth . "21", $nowMonth . "20"];
            }
            $last2Month = date("Y-m-", strtotime("-2 month", $now));
            return [$last2Month . "21" ,$lastMonth . "20"];

        } else if ($dataType == "lastMonth") {
            $dataStart = Arr::get($config, "dataStart");
            $dataEnd = Arr::get($config, "dataEnd");
            return [$lastMonth . $dataStart, $lastMonth . $dataEnd];
        } else if ($dataType == "assignTime") {
            return [Arr::get($config, "dataStart"), Arr::get($config, "dataEnd")];
        }
        return false;

    }



    public function studentEvalTeacherList($uaId, $fromWindows = false)
    {
        $config = $this->teacherEvalActiveConfigMixed(null, $uaId, false);
        if (!$config) {
            return ["configId" => null, "data" => []];
        }
        $dataBelong = $this->configDataBelong($config);
        $models = FastQueryModel::teacherEvaluate()
            ->selectRaw("sc_teacher_evaluate.*, (select (IFNULL(online_eval_num, 0) + IFNULL(offline_eval_num, 0)) as teacher_eval_num from sc_teacher_evaluate_target_user where sc_teacher_evaluate_target_user.id = belong_user_eval_id) as teacher_eval_num, code")
            ->join("evaluate_record_info", "evaluate_record_info.id", "teacher_evaluate.belong_record_id")
            ->whereIn("code", $this->configEvalDataCode($config))
            ->where("type", "student")
            ->where("evaluate_record_info.config_id", $config["id"])
            ->where("belong_date", $this->_generalDataBelong($dataBelong))
            ->where("ua_id", $uaId)
            ->where("status", 0)
            ->orderBy("sort", "DESC")
            ->get();
        $hasEval = $this->studentHasEval($config, $uaId);
        $models = $this->groupTeacherLessonData($models, $hasEval);

        $this->updateOpenWindowTime($fromWindows, $uaId, $config);

        return ["configId" => $config["id"], "data" => $models];

    }

    public function updateOpenWindowTime($fromWindows, $uaId, $config)
    {
        if (!$fromWindows) {
            return;
        }
        $model = FastQueryModel::teacherEvaluateStudentRecord()
            ->where("belong_date", $this->_generalDataBelong($this->configDataBelong($config)))
            ->where("ua_id", $uaId)
            ->first();
        if ($model) {
            FastQueryModel::teacherEvaluateRecordInfo()
                ->where("belong_id", $model->id)
                ->where("type", "student")
                ->where("config_id", $config["id"])
                ->whereIn("code", $this->configEvalDataCode($config))
                ->update(["last_open_time" => date("Y-m-d H:i:s")]);
        }
    }

    public function groupTeacherLessonData($models, $hasEval = false): array
    {
        $data = [];
        $_data = [];

        foreach ($models as $model) {
            $ext = json_decode($model->ext, true);
            $p = collect(Arr::get($ext, "product", []))->map(function ($item) {
                if (in_array($item, ["CAE", "CPS"])) {
                    return "教育顾问";
                }
                return $item;
            })->unique()->implode("|");

            if ($hasEval && $model->teacher_eval_num > 0) {
                $_data[] = [
                    "id" => Arr::get($ext, "id"),
                    "evalId" => $model->id,
                    "name" => Arr::get($ext, "name"),
                    "product" => $p,
                    "lesson" => Arr::get($ext, "lesson"),
                    "sort" => $model->sort,
                    "teacher_eval_num" => $model->teacher_eval_num,
                    'code' => $model->code,
                ];
            } else {
                $data[] = [
                    "id" => Arr::get($ext, "id"),
                    "evalId" => $model->id,
                    "name" => Arr::get($ext, "name"),
                    "product" => $p,
                    "lesson" => Arr::get($ext, "lesson"),
                    "sort" => $model->sort,
                    "teacher_eval_num" => $model->teacher_eval_num,
                    'code' => $model->code,
                ];
            }
        }
        $evalNum = array_column($_data, "teacher_eval_num");
        $sort = array_column($_data, "sort");
        array_multisort($evalNum, SORT_ASC, $sort, SORT_DESC, $_data);
        $this->flashPushStatus($models);
        return array_values(array_merge($data, $_data));
    }

    public function flashPushStatus($models) {
        DB::beginTransaction();
            FastQueryModel::teacherEvaluate()->whereIn("id", $models->pluck("id")->toArray())->whereNull("push_status")->update(["push_status" => date("Y-m-d H:i:s")]);
            $subQuery = FastQueryModel::teacherEvaluate()
                ->selectRaw('belong_user_eval_id, COUNT(DISTINCT ua_id) as total')
                ->whereIn('belong_user_eval_id',  $models->pluck("belong_user_eval_id")->toArray())
                ->whereNotNull("push_status")
                ->groupBy('belong_user_eval_id');
            $subSql = $subQuery->toSql();
            DB::update("UPDATE sc_teacher_evaluate_target_user AS main INNER JOIN ($subSql) AS sub ON main.id = sub.belong_user_eval_id SET main.reach_num = sub.total", $subQuery->getBindings());
        DB::commit();
    }


    public function submitEvaluate($data) {
        $configId = Arr::get($data, "configId");
        $uaId = Arr::get($data,"uaId");
        $config = $this->teacherEvalActiveConfig($configId);
        if (!$config) {
            return "配置不存在";
        }
        $isAnonymous = (int) Arr::get($data,"isAnonymous", 0);
        $isAnonymous = $isAnonymous >= 1 ? 1: 0;
        $recordId = 0;
        $activeNum = 0;
        $eval = Arr::get($data, "eval", []);
        $insertData = [];
        $tagData = [];
        foreach ($eval as $item) {
            if (mb_strlen(Arr::get($item, "content")) > 200) {
                return "评价内容不能超过200个字符";
            }
            $id = Arr::get($item, "evalId");
            $score = Arr::get($item, "score", -1);
            $content = Arr::get($item, "content");
            $tags = Arr::get($item, "tags", []);
            $_tags = "";
            $evalEntity = FastQueryModel::teacherEvaluate()->where("id", $id)->first();
            if (!$evalEntity) {
                continue;
            }
            if ($evalEntity->status != 0 && $evalEntity->eval_from == "TAP") {
                return "数据变动，请刷新重试";
            }
            if ($evalEntity->status != 0 && $evalEntity->eval_from != "TAP") {
                $activeNum++;
                continue;
            }
            //分数
            if ((int) $score < 0) {
                continue;
            }
            if ($tags && is_array($tags)) {
                $tags = array_unique($tags);
                $_tags = implode("/", $tags);
                foreach ($tags as $tag) {
                    $tagData[] = [
                        "belong_teacher_evaluate_id" => $evalEntity->id,
                        "tag" => $tag
                    ];
                }

            }
            $evalEntity->eval_score = (int) $score;
            $evalEntity->eval_content = $_tags ? $_tags . "\n" . $content : $content;
            $evalEntity->status = 1;
            $evalEntity->eval_from = "TAP";
            $evalEntity->eval_time = date("Y-m-d H:i:s");
            if ($isAnonymous) {
                $evalEntity->anonymous = 1;
            }
            $insertData[] = $evalEntity;
            $recordId = $evalEntity->belong_student_eval_id;
            $activeNum ++;
        }
        if ($activeNum <= 0) {
            return "提交数据不能为空";
        }
        DB::beginTransaction();
        foreach ($insertData as $item) {
            $item->save();
        }
        //添加完成
        if ($recordId) {
            $this->_updateTeacherEvaluateStudentRecordNum2($recordId, $activeNum);
        }
        FastQueryModel::teacherEvaluateTags()->insert($tagData);
        DB::commit();
        //after
        $score = $this->dealPoint($uaId, $config, $activeNum);
        $this->afterEvalCal($insertData);
        $showEval = $this->ifShowTeacherEvalButton($uaId);
        return ["result" => true, "msg" => ["tips" => "提交成功，积分+$score", "show_evaluate" => $showEval, "show_evaluate_tips" => $showEval && $this->showEvaluateTips($uaId)]];
    }

    private function _updateTeacherEvaluateStudentRecordNum($id, $activeNum)
    {
        FastQueryModel::teacherEvaluateStudentRecord()->where("id", $id)->update(["fill_num" => DB::raw("fill_num + $activeNum")]);
    }

    private function _updateTeacherEvaluateStudentRecordNum2($studentEvalId)
    {
        DB::beginTransaction();
        $num = FastQueryModel::teacherEvaluate()->where("belong_student_eval_id", $studentEvalId)->where("status", 1)->count();
        FastQueryModel::teacherEvaluateStudentRecord()->where("id", $studentEvalId)->update(["fill_num" => $num]);
        DB::commit();
    }

    public function dealPoint($uaId, $config, $nums)
    {
        $pointConfig = (int) Arr::get($config, "singleEvalPoint", 0);
        if ($pointConfig <= 0) {
            return 0;
        }
        $point = $nums * $pointConfig;
        if ($point <= 0) {
            return 0;
        }
        (new PointService())->teacherEvaluatePoint($uaId, $point);
        return $point;
    }

    public function afterEvalCal($insertData = []) {
        if (!$insertData) {
            return;
        }
        $_insertData = [];
        foreach ($insertData as $model) {
            if (!$model->belong_user_eval_id) {
                continue;
            }
            $_insertData[] = $model->belong_user_eval_id;
        }
        $insertData = collect($_insertData)->unique();
        $model = FastQueryModel::teacherEvaluate()
            ->selectRaw("sum(eval_score) as total_score,count(*) as nums, belong_user_eval_id, eval_from")
            ->where("status", 1)
            ->whereIn("belong_user_eval_id", $insertData)
            ->groupBy(["belong_user_eval_id", "eval_from"])
            ->get()->groupBy("belong_user_eval_id");

        $high_score = 9;
        $model2 = FastQueryModel::teacherEvaluate()
            ->selectRaw("count(*) as nums, belong_user_eval_id")
            ->where("status", 1)
            ->whereIn("belong_user_eval_id", $insertData)
            ->where("eval_score",">=", $high_score)
            ->groupBy(["belong_user_eval_id"])
            ->get()->groupBy("belong_user_eval_id");

       foreach ($model as $id => $item) {
           $online_score = null;
           $offline_score = null;
           $online_num = null;
           $offline_num = null;
           $high_num = null;
           foreach ($item as $value) {
                if ($value->eval_from == "TAP") {
                    $online_num += $value->nums;
                    $online_score += $value->total_score;
                } else {
                    $offline_num += $value->nums;
                    $offline_score += $value->total_score;
                }
                if ($model2->has($id)) {
                    $high_num = $model2->get($id)->first()->nums;
                }
           }
           $update = [
               "online_eval_num" => $online_num,
               "online_eval_score" => $online_score,
               "offline_eval_num" => $offline_num,
               "offline_eval_score" => $offline_score,
               "high_eval_score_num" => $high_num
           ];
           FastQueryModel::teacherEvaluateTargetUser()->where("id", $id)->update($update);
       }

    }


    public function showEvaluateTips($uaId): bool
    {
        $config = $this->teacherEvalActiveConfigMixed(null, $uaId, false);
        if (!$config) {
            return false;
        }
        $evalTips = $this->configEvalTips($config);
        if (!$evalTips) {
            return false;
        }
        $dataBelong = $this->configDataBelong($config);
        $model = FastQueryModel::teacherEvaluateStudentRecord()
            ->whereIn("belong_date", $dataBelong)
            ->where("ua_id", $uaId)
            ->first();

        if ($model) {
            $model = FastQueryModel::teacherEvaluateRecordInfo()
                ->where("belong_id", $model->id)
                ->where("type", "student")
                ->where("config_id", $config["id"])
                ->whereIn("code", $this->configEvalDataCode($config))
                ->where("close_status", 0)
                ->exists();
            if (!$model) {
                return false;
            }
        }
        return true;
    }

    public function closeEvaluateTips($uaId)
    {
        $config = $this->teacherEvalActiveConfig();
        if (!$config) {
            return false;
        }
        $dataBelong = $this->configDataBelong($config);
        $evalCode = $this->configEvalDataCode($config);
        $model = FastQueryModel::teacherEvaluateStudentRecord()
            ->whereIn("belong_date", $dataBelong)
            ->where("ua_id", $uaId)
            ->first();
        if ($model) {
            FastQueryModel::teacherEvaluateRecordInfo()
                ->where("belong_id", $model->id)
                ->where("type", "student")
                ->whereIn("code", $evalCode)
                ->where("config_id", $config["id"])
                ->update(["close_status" => 1]);
        }
        return true;
    }


    public function evaluateTrigger($config) {
        $runTime = $this->formatEvaluateTriggerDate($config);
        if (!$runTime) {
            return;
        }
        $nowTime = time();
        if ($nowTime < $runTime[0] || $nowTime > $runTime[1]) {
            return;
        }
        $dataTime = $this->formatDateTime($config, $nowTime);
        if (!$dataTime) {
            return;
        }
        //进行计算
        $this->syncTargetRelationData($config, $dataTime);
    }

    public function syncTargetRelationData($config, $dataTime)
    {
        //只考虑新增的情况
        //1. 获取符合的 列表
//        $relations
        $this->evalRelationBaseData($config);
    }

    public function evalRelationBaseData($config) {
        //获取符合条件所有数据
        $relations = explode("|", Arr::get($config, "relation", ""));
        $data = $this->studentEvalData($config, null);
        $studentTarget = [];
        $teacherTarget = [];
        foreach ($relations as $relation) {
            $relationData = Arr::get($data, $relation, []);
            $this->_evalStudentWithMixed($studentTarget, $teacherTarget, $relationData, $config);
        }
        $this->evalRelationBaseDataConvert($studentTarget);
        $teacherData = $this->fillTeacherEvalDate($config, $teacherTarget);
        $this->fillStudentEvalData($config, $studentTarget, $teacherData);

        //后置 补充 attach 部分的数据
        $this->fillAttachStudentEvalData($config);

        //维护最新的
        $this->fillStudentEvalMainId($config);

        //维护 老师和学生需要填写的数字
        $this->fillNeedNum($config);

    }
    public function fillNeedNum($config) {
        $dataBelong = $this->configDataBelongStr($config);
        $subQuery = FastQueryModel::teacherEvaluate()
            ->selectRaw('belong_student_eval_id, COUNT(DISTINCT target_ua_id) as total')
            ->where('belong_date', $dataBelong)
            ->groupBy('belong_student_eval_id');

        DB::transaction(function () use ($subQuery) {
            $subSql = $subQuery->toSql();
            DB::update("UPDATE sc_teacher_evaluate_student_record AS main INNER JOIN ($subSql) AS sub ON main.id = sub.belong_student_eval_id SET main.need_num = sub.total", $subQuery->getBindings());
        });

        $subQuery = FastQueryModel::teacherEvaluate()
            ->selectRaw('belong_user_eval_id, COUNT(DISTINCT ua_id) as total')
            ->where('belong_date', $dataBelong)
            ->groupBy('belong_user_eval_id');

        DB::transaction(function () use ($subQuery) {
            $subSql = $subQuery->toSql();
            DB::update("UPDATE sc_teacher_evaluate_target_user AS main INNER JOIN ($subSql) AS sub ON main.id = sub.belong_user_eval_id SET main.need_num = sub.total", $subQuery->getBindings());
        });
    }
    public function fillStudentEvalMainId($config) {
        $dataBelong = $this->configDataBelongStr($config);
        $list = FastQueryModel::teacherEvaluate()->selectRaw("MAX(id) as id, ua_id, target_ua_id")->where("belong_date", $dataBelong)
            ->groupBy(["ua_id", "target_ua_id"])->get();
        //把财务月内所有数据刷成0
        FastQueryModel::teacherEvaluate()->where("belong_date", $dataBelong)->update(["main_id" => 0]);
        //批量更新维护最新的数据
        FastQueryModel::teacherEvaluate()->whereIn("id", $list->pluck("id")->toArray())->update(["main_id" => DB::raw("id")]);
    }

    public function fillAttachStudentEvalData($config) {
        //补充 附加数据
        $dataAttachDate = Arr::get($config, "dataAttachDate", "");
        if (!$dataAttachDate) {
            return false;
        }
        $dataAttachCode = Arr::get($config, "dataAttachCode", []);
        if (!$dataAttachCode) {
            return false;
        }
        $dataAttachInsertCode = Arr::get($config, "dataAttachInsertCode", "");
        if (!$dataAttachInsertCode) {
            return false;
        }
        //要拿配置下要评价的配置
        $nowDataCode = $this->configEvalDataCode($config);
        $nowDate = $this->configDataBelongStr($config);
        //未评价的内容 & 当期没有关系
        $data = FastQueryModel::teacherEvaluate()
            ->select([
                "teacher_evaluate.*",
                "student_name","student_code","owner_ua_id","owner_name","owner_code","owner_team","owner_team_id","owner_mpg","owner_mpg_id",
                "user_name","user_code","team","team_id","mpg","mpg_id","role"
                ])
            ->join("teacher_evaluate_student_record", "teacher_evaluate.belong_student_eval_id", "=", "teacher_evaluate_student_record.id")
            ->join("teacher_evaluate_target_user", "teacher_evaluate.belong_user_eval_id", "=", "teacher_evaluate_target_user.id")
            ->join("evaluate_record_info", "teacher_evaluate.belong_record_id", "=", "evaluate_record_info.id")
            ->where("status", 0)
            ->where("teacher_evaluate.belong_date", $dataAttachDate)
            ->where("evaluate_record_info.type", "student")
            ->whereIn("evaluate_record_info.code", $dataAttachCode)
            ->whereNotExists(function (Builder $q) use ($nowDataCode, $nowDate) {
                $q->select(DB::raw(1))
                    ->from(DB::raw("sc_teacher_evaluate as aa"))
                    ->join(DB::raw("sc_evaluate_record_info as bb"), DB::raw("aa.belong_record_id"), DB::raw("bb.id"))
                    ->whereColumn(DB::raw("aa.ua_id"), "teacher_evaluate.ua_id")
                    ->whereColumn(DB::raw("aa.target_ua_id"), "teacher_evaluate_target_user.ua_id")
                    ->where(DB::raw("bb.type"), "student")
                    ->whereIn(DB::raw("bb.code"), $nowDataCode)
                    ->where(DB::raw("aa.belong_date"), $nowDate);
            })->get();
        $insert = [];
        $teacherRecord = [];
        $studentRecord = [];
        $relation = [];
        //2种可能 1 老师关系没有 2 老师关系存在的
        foreach ($data as $item) {
            //4种情况 1 新老师 2 旧老师 3 新学生 4 旧学生
            $teacherId = $item->target_ua_id;
            $studentId = $item->ua_id;
            if (!isset($studentRecord[$studentId])) {
                $studentRecord[$studentId] =  FastQueryModel::teacherEvaluateStudentRecord()->updateOrCreate([
                    "belong_date" => $nowDate,
                    "ua_id" => $studentId,
                ], [
                    "student_name" => $item->student_name,
                    "student_code" => $item->student_code,
                    "owner_ua_id" => $item->owner_ua_id,
                    "owner_name" => $item->owner_name,
                    "owner_code" => $item->owner_code,
                    "owner_team" => $item->owner_team,
                    "owner_team_id" => $item->owner_team_id,
                    "owner_mpg" => $item->owner_mpg,
                    "owner_mpg_id" => $item->owner_mpg_id
                ]);
            }
            if (!isset($relation[$studentId])) {
                $relation[$studentId] = FastQueryModel::teacherEvaluateRecordInfo()->updateOrCreate([
                    "type" => "student",
                    "belong_id" => $studentRecord[$studentId]->id,
                    "code" => $dataAttachInsertCode,
                    "config_id" => $config["id"]
                ]);
            }
            if (!isset($teacherRecord[$teacherId])) {
                $teacherRecord[$teacherId] = FastQueryModel::teacherEvaluateTargetUser()->updateOrCreate(
                    [
                        "belong_date" => $nowDate,
                        "ua_id" => $teacherId
                    ],[
                        "user_name" => $item->user_name,
                        "user_code" => $item->user_code,
                        "team" => $item->team,
                        "team_id" => $item->team_id,
                        "mpg" => $item->mpg,
                        "mpg_id" => $item->mpg_id,
                        "role" => $item->role,
                    ]
                );
            }
            $insert[] = [
                "belong_user_eval_id" => $teacherRecord[$teacherId]->id,
                "belong_student_eval_id" => $studentRecord[$studentId]->id,
                "ua_id" => $item->ua_id,
                "belong_date" => $nowDate,
                "config_id" => $config["id"],
                "target_ua_id" => $item->target_ua_id,
                "sort" => $item->sort,
                "ext" => $item->ext,
                "belong_record_id" => $relation[$studentId]->id,
                "parent_id" => $item->id,
            ];
            if (count($insert) > 1000) {
                FastQueryModel::teacherEvaluate()->insert($insert);
                $insert = [];
            }
        }
        if (!empty($insert)) {
            FastQueryModel::teacherEvaluate()->insert($insert);
        }
        if (!empty($studentRecord)) {
            foreach ($studentRecord as $item) {
                $item->save();
            }
        }
        if (!empty($teacherRecord)) {
            foreach ($teacherRecord as $item) {
                $item->save();
            }
        }
        return true;
    }

    public function evalRelationBaseDataConvert(&$studentTarget) {
        foreach ($studentTarget as &$item) {
            foreach ($item as &$lesson) {
                $t = [];
                foreach ($lesson["product"] as $p => $items) {
                    if ($p == "CPS" || $p == "CAE") {
                        $t[] = $p;
                    } else {
                        $_tmp = implode("+", collect($items)->unique()->toArray());
                        $t[] = $p . " " . $_tmp;
                    }

                }
                $lesson["product"] = $t;
            }
        }

    }

    public function fillTeacherEvalDate($config, $teacherData) {
        //需要获取ID
        $dataBelong = $this->configDataBelong($config);
        $teacherData = collect($teacherData);
        //base teacher data
        $targetUser = $this->belongDataTargetUser($config);
        foreach ($targetUser as $user) {
            $user = $user->toArray();
//            $neeNum = collect($teacherData->get($user["uaid"],collect()))->unique()->count();
            $model = FastQueryModel::teacherEvaluateTargetUser()->updateOrCreate(
                [
                    "belong_date" => $this->_generalDataBelong($dataBelong),
                    "ua_id" => $user["uaid"]
                ],[
                    "config_name" => $config["evalName"],
                    "relation" => Arr::get($config, "relation"),
                    "user_name" => Arr::get($user, "staffname"),
                    "user_code" => Arr::get($user, "staffnum"),
                    "team" => Arr::get($user, "team"),
                    "team_id" => Arr::get($user, "teamid"),
                    "mpg" => Arr::get($user, "mpg"),
                    "mpg_id" => Arr::get($user, "mpgid"),
                    "role" => Arr::get($user, "role"),
//                    "need_num" => $neeNum,
                ]
            );
            $teacherData[$model->ua_id] = $model->id;
        }
        return $teacherData;
    }

    public function belongDataTargetUser($config) {
        $targetRole = Arr::get($config, "userDataRole", []);
        $passTeam = Arr::get($config, "userDataPassTeam");
        return FastQueryModel::pasPcaUser()
            ->selectRaw("uaid,cas_crm.pas_grouppca.groupname as team,cas_crm.pas_grouppca.gid as teamid,cas_crm.vtiger_mpg.mpg,cas_crm.vtiger_mpg.mpgid,pas_staffpca.staffnum,pas_staffpca.staffname, GROUP_CONCAT(DISTINCT pas_staffpca.role) as role")
            ->join(DB::raw("cas_crm.pas_grouppca"), function ($q) {
                $q->on( DB::raw("cas_crm.pas_grouppca.gid"), "=", DB::raw("cas_crm.pas_staffpca.gid"));
                $q->on(DB::raw("cas_crm.pas_grouppca.mo"), "=", DB::raw("cas_crm.pas_staffpca.mo"));
            })
            ->join(DB::raw("cas_crm.xkt_user_ids"), DB::raw("cas_crm.xkt_user_ids.crmid"), "=", DB::raw("cas_crm.pas_staffpca.staffid"))
            ->join(DB::raw("cas_crm.vtiger_mpg"), DB::raw("cas_crm.vtiger_mpg.mpgid"), "=", DB::raw("cas_crm.pas_grouppca.mpgid"))
            ->whereIn("role", $targetRole)
//            ->where("level", "like", "T%")
            ->when($passTeam, function ($query) use ($passTeam) {
                $query->where(function ($query) use ($passTeam) {
                    $passTeam = explode(",", $passTeam);
                    foreach ($passTeam as $team) {
                        if (Str::contains($team,"%")) {
                            $query->orWhere(DB::raw("cas_crm.pas_grouppca.groupname"), "not like", $team);
                        } else {
                            $query->orWhere(DB::raw("cas_crm.pas_grouppca.groupname"), "!=", $team);
                        }
                    }
                });

            })
            ->whereIn(DB::raw("cas_crm.pas_staffpca.mo"), $this->configUserDataBelong($config))
            ->groupBy("uaid")
            ->get();

    }

    protected function _generalDataBelong($dataBelong = []) {
        return implode("|", $dataBelong);

    }

    public function getStudentExtraUser($ids, $type = "owner") {
        //默认拿跟进人
       return FastQueryModel::crmOwner()
           ->selectRaw("cas_crm.vtiger_account.ua_id,cas_crm.vtiger_account.accountname,cas_crm.vtiger_account.smpid,cas_crm.xkt_user_ids.uaid as owner_id,cas_crm.vtiger_users.last_name as name,cas_crm.xkt_user_ids.staffnum, cas_crm.vtiger_team.teamid,cas_crm.vtiger_team.team,cas_crm.vtiger_mpg.mpgid,cas_crm.vtiger_mpg.mpg")
            ->join(DB::raw("cas_crm.vtiger_account"), DB::raw("cas_crm.vtiger_account.accountid"), "=", DB::raw("cas_crm.vtiger_crmentity.crmid"))
           ->leftJoin(DB::raw("cas_crm.vtiger_users"), DB::raw("cas_crm.vtiger_users.id"), "=", DB::raw("cas_crm.vtiger_crmentity.smownerid"))
           ->leftJoin(DB::raw("cas_crm.xkt_user_ids"), DB::raw("cas_crm.xkt_user_ids.crmid"), "=", DB::raw("cas_crm.vtiger_users.id"))
            ->leftJoin(DB::raw("cas_crm.vtiger_user2team"), DB::raw("cas_crm.vtiger_user2team.userid"), "=", DB::raw("cas_crm.vtiger_crmentity.smownerid"))
            ->leftJoin(DB::raw("cas_crm.vtiger_team"), DB::raw("cas_crm.vtiger_user2team.teamid"), "=", DB::raw("cas_crm.vtiger_team.teamid"))
            ->leftJoin(DB::raw("cas_crm.vtiger_mpg"), DB::raw("cas_crm.vtiger_mpg.mpgid"), "=", DB::raw("cas_crm.vtiger_team.gmid"))
            ->whereIn(DB::raw("cas_crm.vtiger_account.ua_id"), $ids)
            ->get()->groupBy("ua_id");
    }

    public function fillStudentEvalData($config, $studentData, $teacherData)
    {
        $dataBelong = $this->configDataBelong($config);
        $studentData = collect($studentData);
        $teacherData = collect($teacherData)->toArray();
        $studentOwner = $this->getStudentExtraUser($studentData->keys())->toArray();
        //1. 为学生生成record
        //2. 添加预填写数据
        $insert = [];
        foreach ($studentData as $studentUaId => $items) {
            $count = 0;
            foreach ($items as $targetUaId => $item) {
                $teacherId = Arr::get($teacherData, $targetUaId, 0);
                if (is_array($teacherId)) {
                    $teacherId = null;
                } else {
                    //只计算有老师关系的
                    $count ++;
                }
            }
            //没有老师的 pass
            if ($count == 0) {
                continue;
            }
            //更新基本record
            $_studentOwner = [];
            if (Arr::has($studentOwner, $studentUaId)) {
                $_studentOwner = $studentOwner[$studentUaId][0];
            }
           $model =  FastQueryModel::teacherEvaluateStudentRecord()->updateOrCreate([
                "belong_date" => $this->_generalDataBelong($dataBelong),
                "ua_id" => $studentUaId,
            ], [
                "config_name" => $config["evalName"],
                "need_num" => $count,
                "student_name" => Arr::get($_studentOwner, "accountname", ""),
                "student_code" => Arr::get($_studentOwner, "smpid", ""),
                "owner_ua_id" => Arr::get($_studentOwner, "owner_id", ""),
                "owner_name" => Arr::get($_studentOwner, "name", ""),
                "owner_code" => Arr::get($_studentOwner, "staffnum", ""),
                "owner_team" => Arr::get($_studentOwner, "team", ""),
                "owner_team_id" => Arr::get($_studentOwner, "teamid", ""),
                "owner_mpg" => Arr::get($_studentOwner, "mpg", ""),
                "owner_mpg_id" => Arr::get($_studentOwner, "mpgid", "")
            ]);
            //补充关系
            $infoModel = FastQueryModel::teacherEvaluateRecordInfo()->updateOrCreate([
                "config_id" => $config["id"],
                "belong_id" => $model->id,
                "type" => "student",
                "code" => $this->configDataCode($config)
            ]);

            //添加预填写数据
            $old = FastQueryModel::teacherEvaluate()
                ->where("belong_date", $this->_generalDataBelong($dataBelong))
                ->where("ua_id", $studentUaId)
                ->where("belong_record_id", $infoModel->id)
                ->get()->groupBy("target_ua_id");

            $update = [];
            foreach ($items as $targetUaId => $item) {
                if ($old->has($targetUaId)) {
                    //pass
                    $_old = $old->get($targetUaId)->first();
                    if ($_old->status == 0) {
                        $_old->ext = collect($item)->toJson();
                        $_old->sort = Arr::get($item, "sort", 0);
                        $update[] = $_old;
                    }
                    continue;
                }
                $teacherId = Arr::get($teacherData, $targetUaId, 0);
                if (is_array($teacherId)) {
                    //pass 不符合条件的
                    continue;
                }
                $insert[] = [
                    "belong_student_eval_id" => $model->id,
                    "belong_user_eval_id" => $teacherId,
                    "belong_date" => $this->_generalDataBelong($dataBelong),
                    "ua_id" => $studentUaId,
                    "target_ua_id" => $targetUaId,
                    "config_id" => $config["id"],
                    "eval_from" => "TAP",
                    "sort" => Arr::get($item, "sort", 0),
                    "belong_record_id" => $infoModel->id,
                    "ext" => collect($item)->toJson()
                ];
            }
            if (count($insert) >= 1000) {
                FastQueryModel::teacherEvaluate()->insert($insert);
                echo "批量插入" . count($insert) . "条\n";
                $insert = [];
            }
            if($update) {
               foreach ($update as $item) {
                   //执行更新
                   $item->save();
               }
            }


        }
        if ($insert) {
            FastQueryModel::teacherEvaluate()->insert($insert);
            echo "批量插入" . count($insert) . "条\n";
        }
    }

    public function _evalStudentWithMixedDiffEval($data, $config) {
        $relation = [];
        $_data = collect($data)->groupBy("student_ua_id");
        foreach ($_data as $studentUaId => $item) {
            if (!$studentUaId) {
                //排除uaid 为空的情况
                continue;
            }
            $teacherLesson = collect($item)->groupBy("ua_id");
            foreach ($teacherLesson as $teacherUaId => $lessons) {
                $relation[] = ["student_ua_id" => $studentUaId, "teacher_ua_id" => $teacherUaId];
            }
        }
        $belongDate = $this->configDataBelongStr($config);
        $now = strtotime($belongDate);
        //check
        $nowMo = date("Y-m-01", $now);
        $lastMo = date("Y-m-01", strtotime("-1 month", $now));
        $data2 = FastQueryModel::teacherEvaluate()
            ->select(["ua_id", "target_ua_id"])
            ->whereIn("belong_date", [$nowMo, $lastMo])
            ->where("status", 1)
            ->where(function ($query) use ($relation) {
                foreach ($relation as $item) {
                    $query->orWhere(function ($query) use ($item) {
                        $query->where("ua_id", $item["student_ua_id"])
                            ->where("target_ua_id", $item["teacher_ua_id"]);
                    });
                }
            })->groupBy(["ua_id", "target_ua_id"])->get();
        $data2 = $data2->mapWithKeys(function ($item) {
            return [$item->ua_id . '_' . $item->target_ua_id => true];
        })->all();
        return collect($data)->filter(function ($item) use ($data2) {
            $key = $item['student_ua_id'] . '_' . $item['ua_id'];
            return !isset($data2[$key]);
        })->values()->toArray();
    }


    public function _evalStudentWithMixed(&$studentTarget, &$teacherTarget, $data, $config) {
//        $data = $this->_evalStudentWithMixedDiffEval($data, $config);
        $_data = collect($data)->groupBy("student_ua_id");
        foreach ($_data as $studentUaId => $item) {
            if (!$studentUaId) {
                //排除uaid 为空的情况
                continue;
            }
            if (!isset($studentTarget[$studentUaId])) {
                $studentTarget[$studentUaId] = [];
            }
            $teacherLesson = collect($item)->groupBy("ua_id");
            foreach ($teacherLesson as $teacherUaId => $lessons) {
                if (!isset($studentTarget[$studentUaId][$teacherUaId])) {
                    $studentTarget[$studentUaId][$teacherUaId] = [
                        "name" => "",
                        "id" => "",
                        "product" => [],
                        "lesson" => 0,
                        "sort" => 0,
                    ];
                }
                $tmp = &$studentTarget[$studentUaId][$teacherUaId];
                foreach ($lessons as $lesson) {
                    $tmp["id"] = Arr::get($lesson, "ua_id");
                    $counts = Arr::get($lesson, "counts", 0);
                    $sort = $counts;
                    if ($counts === "other") {
                        $counts = 0;
                        if (Arr::get($lesson, "catename") == "CAE") {
                            $sort = 5;
                        } else {
                            $sort = 1;
                        }
                    } else {
                        $sort = 10000 * $counts;
                    }
                    $tmp["lesson"] += $counts;
                    $tmp["sort"] += $sort;
                    if (!isset($tmp["product"][Arr::get($lesson, "catename")])) {
                        $tmp["product"][Arr::get($lesson, "catename")] = [];
                    }
                    $tmp["product"][Arr::get($lesson, "catename")][] = Arr::get($lesson, "lessonname");
                    $tmp["name"] = Arr::get($lesson, "staffname");
                }
                unset($tmp);
            }
        }
        $_data = collect($data)->groupBy("ua_id");
        foreach ($_data as $teacherUaId => $item) {
            if (!isset($teacherTarget[$teacherUaId])) {
                $teacherTarget[$teacherUaId] = [];
            }
            $studentLesson = collect($item)->groupBy("student_ua_id");
            if ($studentLesson->has("")) {
                $studentLesson->forget("");
            }
            $teacherTarget[$teacherUaId] = array_unique(array_merge($studentLesson->keys()->toArray(), $teacherTarget[$teacherUaId]));
        }
    }


    public function formatEvaluateTriggerDate($config) {
        $evalTriggerType = Arr::get($config, "evalTriggerType", "assignTime");
        $now = time();
        $ym = date("Y-m-", $now);
        $evalTriggerStart = Arr::get($config, "evalTriggerStart", "");
        $evalTriggerEnd = Arr::get($config, "evalTriggerEnd", "");
        if ($evalTriggerType == "assignTime") {
            return [strtotime($evalTriggerStart), strtotime($evalTriggerEnd)];
        } else if ($evalTriggerType == "everyFinanceMonth") {
            //默认当前月 除非实用next标志
            $yme = date("Y-m-", $now);
            if ($evalTriggerStart == "monthStart") {
                $evalTriggerStart = "21 00:00:00";
            }
            //月底
            if ($evalTriggerEnd == "monthEnd") {
                $evalTriggerEnd = "20 23:59:59";
            }
            if (Str::startsWith($evalTriggerEnd, "next")) {
                $end = strtotime("+1 month", $now);
                $yme = date("Y-m-", $end);
                $evalTriggerEnd = Str::replaceFirst("next", "", $evalTriggerEnd);
            }
            return [strtotime($ym . $evalTriggerStart), strtotime($yme . $evalTriggerEnd)];
        }
        return false;
    }


    public function importTeacherEvaluate($uaId, $data = []) {
        $data = Arr::get($data, "data", []);
        if (false === $this->checkUserActionCanDo($uaId, "import", true)) {
            return false;
        }
        //对于导入数据进行处理
        //eg. 2025-01-01 | 王老菊 ｜ 1234 ｜ 赵同学 ｜ 1234567890 ｜ 10 ｜ 煞笔老师 | 2022-01-01 12:00:00
        //目前考虑到财务月只有一次调研

        //step1. 获取列表内用户
        $teachers = [];
        $students = [];
        foreach ($data as $eval) {
            $teacher = Arr::get($eval, 2, "");
            if ($teacher) {
                $teachers[] = $teacher;
            }
            $student = Arr::get($eval, 4, "");
            if ($student) {
                $students[] = $student;
            }
        }
        $teachers = FastQueryModel::crmUser()->select(["user_code", "ua_id"])->whereIn("user_code", $teachers)->get()->groupBy("user_code");
        $students = FastQueryModel::crmStudent()->select(["ua_id", "smpid"])->whereIn("smpid", $students)->get()->groupBy("smpid");
        $needDo = [];
        foreach ($data as &$eval) {
            if (false === $find = $this->_checkImportEval($eval, $teachers, $students)) {
                continue;
            }
            $needDo[] = $find;
        }
        unset($eval);
        if ($needDo) {
            $studentRecord = [];
            collect(collect($needDo)->toArray())->groupBy("belong_student_eval_id")->each(function ($group, $key) use (&$studentRecord) {
                $studentRecord[$key] = $group->count();
                return true;
            });
            DB::beginTransaction();
            foreach ($needDo as $item) {
                $item->save();
            }
            foreach ($studentRecord as $key => $activeNum) {
                $this->_updateTeacherEvaluateStudentRecordNum2($key, $activeNum);
            }
            DB::commit();
            //after deal
            $this->afterEvalCal($needDo);
        }
        return $data;
    }

    private function _checkImportEval(&$eval, $teachers, $students)
    {
        $teacher = Arr::get($eval, 2, "");
        $student = Arr::get($eval, 4, "");
        $month = Arr::get($eval, 0, "");
        $evalTime = Arr::get($eval, 7, "");
        $score =  Arr::get($eval, 5, -1);
        $evalContent = Arr::get($eval, 6, "");
        if (!is_numeric($score) || doubleval($score) != intval($score) || $score > 10 || $score < 0) {
            $eval[8] = "评分不正确";
            return false;
        }
        if ($evalTime && false === strtotime($evalTime)) {
            $eval[8] = "评价时间不正确";
            return false;
        }
        if (false === strtotime($month)) {
            $eval[8] = "财务月错误";
            return false;
        }
        if (!$teachers->has($teacher)) {
            $eval[8] = "老师不存在";
            return false;
        }
        if (!$students->has($student)) {
            $eval[8] = "学生不存在";
            return false;
        }
        $findRelation = [
            "ua_id" => $students->get($student)->first()->ua_id,
            "target_ua_id" => $teachers->get($teacher)->first()->ua_id,
            "belong_date" => date("Y-m-01", strtotime($month))
        ];

        $from = "线下";
        $find = FastQueryModel::teacherEvaluate()->where($findRelation)->where("main_id", ">", 0)->first();
        if (!$find) {
            if (strtotime($month) > time()) {
                $eval[8] = "财务月不能超过当前财务月";
                return false;
            }
            // 尝试创建关系
            $find = $this->createTeacherStudentEvalRelation(
                $findRelation,
                $students->get($student)->first(),
                $teachers->get($teacher)->first(),
                $month
            );

            if (!$find) {
                $eval[8] = "无法创建教师学生关系";
                return false;
            }
            $from = "导入新增";
        }

        $find->eval_time = null;
        if ($evalTime) {
            $find->eval_time = $evalTime;
        }
        $find->status = 1;
        $find->eval_content = Str::substr($evalContent, 0, 200);
        $find->eval_score = $score;
        $find->eval_from = $find->eval_from == "导入新增" ? $find->eval_from : $from;
        return $find;
    }

    /**
     * 创建教师-学生评价关系
     * @param array $relation 关系数组
     * @param object $student 学生信息
     * @param object $teacher 教师信息
     * @param string $month 评价月份
     * @return mixed
     */
    private function createTeacherStudentEvalRelation($relation, $student, $teacher, $month, $from = "导入新增")
    {


        // 获取教师的团队和MPG信息
        $teacherInfo = FastQueryModel::pasPcaUser()
            ->selectRaw("uaid,cas_crm.pas_grouppca.groupname as team,cas_crm.pas_grouppca.gid as teamid,cas_crm.vtiger_mpg.mpg,cas_crm.vtiger_mpg.mpgid,pas_staffpca.staffnum,pas_staffpca.staffname, GROUP_CONCAT(DISTINCT pas_staffpca.role) as role")
            ->join(DB::raw("cas_crm.pas_grouppca"), function ($q) {
                $q->on(DB::raw("cas_crm.pas_grouppca.gid"), "=", DB::raw("cas_crm.pas_staffpca.gid"));
                $q->on(DB::raw("cas_crm.pas_grouppca.mo"), "=", DB::raw("cas_crm.pas_staffpca.mo"));
            })
            ->join(DB::raw("cas_crm.xkt_user_ids"), DB::raw("cas_crm.xkt_user_ids.crmid"), "=", DB::raw("cas_crm.pas_staffpca.staffid"))
            ->join(DB::raw("cas_crm.vtiger_mpg"), DB::raw("cas_crm.vtiger_mpg.mpgid"), "=", DB::raw("cas_crm.pas_grouppca.mpgid"))
            ->where("uaid", $teacher->ua_id)
            ->where(DB::raw("cas_crm.pas_grouppca.mo"), date("Y年m月", strtotime($month)))
            ->first();
        if (!$teacherInfo) {
            return false;
        }

        // 获取学生的跟进人信息
        $studentOwnerInfo = FastQueryModel::crmOwner()
            ->selectRaw("cas_crm.vtiger_account.ua_id,cas_crm.vtiger_account.accountname,cas_crm.vtiger_account.smpid,cas_crm.xkt_user_ids.uaid as owner_id,cas_crm.vtiger_users.last_name as name,cas_crm.xkt_user_ids.staffnum, cas_crm.vtiger_team.teamid,cas_crm.vtiger_team.team,cas_crm.vtiger_mpg.mpgid,cas_crm.vtiger_mpg.mpg")
            ->join(DB::raw("cas_crm.vtiger_account"), DB::raw("cas_crm.vtiger_account.accountid"), "=", DB::raw("cas_crm.vtiger_crmentity.crmid"))
            ->leftJoin(DB::raw("cas_crm.vtiger_users"), DB::raw("cas_crm.vtiger_users.id"), "=", DB::raw("cas_crm.vtiger_crmentity.smownerid"))
            ->leftJoin(DB::raw("cas_crm.xkt_user_ids"), DB::raw("cas_crm.xkt_user_ids.crmid"), "=", DB::raw("cas_crm.vtiger_users.id"))
            ->leftJoin(DB::raw("cas_crm.vtiger_user2team"), DB::raw("cas_crm.vtiger_user2team.userid"), "=", DB::raw("cas_crm.vtiger_crmentity.smownerid"))
            ->leftJoin(DB::raw("cas_crm.vtiger_team"), DB::raw("cas_crm.vtiger_user2team.teamid"), "=", DB::raw("cas_crm.vtiger_team.teamid"))
            ->leftJoin(DB::raw("cas_crm.vtiger_mpg"), DB::raw("cas_crm.vtiger_mpg.mpgid"), "=", DB::raw("cas_crm.vtiger_team.gmid"))
            ->where(DB::raw("cas_crm.vtiger_account.ua_id"), $student->ua_id)
            ->first();

        if (!$studentOwnerInfo) {
            return false;
        }

         DB::beginTransaction();
        // 1. 创建或获取学生记录
        $studentRecord = FastQueryModel::teacherEvaluateStudentRecord()->firstOrCreate(
            [
                "belong_date" => $relation['belong_date'],
                "ua_id" => $student->ua_id,
            ],
            [
                "student_name" => $studentOwnerInfo->accountname ?? '',
                "student_code" => $studentOwnerInfo->smpid ?? '',
                "need_num" => 0,
                "fill_num" => 0,
                // 添加学生跟进人信息
                "owner_ua_id" => $studentOwnerInfo->owner_id ?? '',
                "owner_name" => $studentOwnerInfo->name ?? '',
                "owner_code" => $studentOwnerInfo->staffnum ?? '',
                "owner_team" => $studentOwnerInfo->team ?? '',
                "owner_team_id" => $studentOwnerInfo->teamid ?? '',
                "owner_mpg" => $studentOwnerInfo->mpg ?? '',
                "owner_mpg_id" => $studentOwnerInfo->mpgid ?? ''
            ]
        );


        // 2. 创建或获取教师目标用户记录
        $teacherRecord = FastQueryModel::teacherEvaluateTargetUser()->firstOrCreate(
            [
                "belong_date" => $relation['belong_date'],
                "ua_id" => $teacher->ua_id,
            ],
            [
                "user_name" => $teacherInfo->staffname ?? '',
                "user_code" => $teacherInfo->staffnum ?? '',
                "need_num" => 0,
                "reach_num" => 0,
                "online_eval_num" => 0,
                "offline_eval_num" => 0,
                // 添加教师团队信息
                "team" => $teacherInfo->team ?? '',
                "team_id" => $teacherInfo->teamid ?? '',
                "mpg" => $teacherInfo->mpg ?? '',
                "mpg_id" => $teacherInfo->mpgid ?? '',
                "role" => $teacherInfo->role ?? ''
            ]
        );

        // 3. 创建评价记录
        $evaluate = FastQueryModel::teacherEvaluate()->create([
            "belong_user_eval_id" => $teacherRecord->id,
            "belong_student_eval_id" => $studentRecord->id,
            "belong_date" => $relation['belong_date'],
            "ua_id" => $relation['ua_id'],
            "target_ua_id" => $relation['target_ua_id'],
            "status" => 0,
            "eval_from" => $from,
            "main_id" => 0,
            "sort" => 0,
            "ext" => json_encode([
                "name" => $teacherInfo->staffname ?? '',
                "id" => $teacherInfo->uaid ?? '',
                "product" => [$from],
                "lesson" => 0
            ])
        ]);
        // 更新main_id
        $evaluate->main_id = $evaluate->id;
        $evaluate->save();
        $studentRecord->need_num = $studentRecord->need_num + 1;
        $teacherRecord->need_num = $teacherRecord->need_num + 1;
        $studentRecord->save();
        $teacherRecord->save();
        DB::commit();
        return $evaluate;
    }

    private function checkUserActionCanDo($uaId, $action = "data",  $needBool = false)
    {
        if (!$uaId) {
            return false;
        }
        $user = FastQueryModel::config()->where("type", "=" ,"CSAT用户白名单")->first();
        if (!$user) {
            return false;
        }
        $configs = $user->configs;
        $configs = collect(json_decode($configs, true));
        if (!$configs->has($uaId)) {
            return false;
        }
        $config = $configs->get($uaId);
        if ($action == "data") {
            $data = $config["data"];
            if ($needBool && $data == "all") {
                return true;
            }
            return $data;
        } else if ($action == "anonymousHide" || $action == "share") {
            return (bool) Arr::get($config, $action, false);
        }
        else {
            $actionInfo = explode(",", $config["action"]);
            if ((in_array($action, $actionInfo) || in_array("all", $actionInfo)) && $needBool) {
                return true;
            }
        }
        return false;
    }


    /**
     * 待评价老师列表
     * @param $data
     * @param $page
     * @param $pageNum
     * @return array
     */
    public function evaluateUserList($data, $page = 1, $pageNum = 20): array
    {
        $uaId = Arr::get($data, "uaId", "");
        $sort = Arr::get($data, "sort", "");
        $order = Arr::get($data, "sortOrder", "");
        if (false === $this->checkUserActionCanDo($uaId, "data", true)) {
            return [];
        }
        $month = Arr::get($data, "month", "");
        $team = Arr::get($data, "team", "");
        $teacher = Arr::get($data, "search", "");
        $findMyData = $this->_myTeamEvalUserId($uaId);
        $list = FastQueryModel::teacherEvaluateTargetUser()
            ->selectRaw("*,(select count(1) from sc_teacher_evaluate where belong_user_eval_id = sc_teacher_evaluate_target_user.id and eval_score >= 9 and status = 1) as high9")
            ->when($month, function ($query) use ($month) {
                return $query->where("belong_date", $month);
            })
            ->when($team, function ($query) use ($team) {
                return $query->where("team_id", $team);
            })
            ->when($teacher, function ($query) use ($teacher) {
                return $query->where("ua_id", $teacher);
            })
            ->when($findMyData && $findMyData != "all", function ($query) use ($findMyData) {
                if (isset($findMyData["sql"])) {
                    $query->whereRaw($findMyData["sql"]);
                } else {
                    $query->where($findMyData);
                }
            })
            ->when($sort, function ($query) use ($sort, $order) {
                if ($sort == 'user_name') {
                    $sort = "user_code";
                }
                return $query->orderBy($sort, $order);
            })
            ->when(!$sort, function ($query) {
                return $query->orderBy("mpg")->orderBy("team")->orderBy("user_code");
            })
            ->paginate($pageNum);
        foreach ($list->items() as $item) {
            if ($item->online_eval_score === null && $item->offline_eval_score == null) {
                $item['avg'] = null;
                $item['satisfy'] = null;
            } else {
                $total = (int)$item->online_eval_num + (int)$item->offline_eval_num;
                $score = (int )$item->online_eval_score + (int)$item->offline_eval_score;
                $item['avg'] = bcdiv($score, $total, 2);
                if ((int)$item->high9 > 0) {
                    $item['satisfy'] = bcdiv($item->high9 * 100, $total, 2) . "%";
                }
            }
        }
        return $list->toArray();
    }

    public function evaluateList($data, $page = 1, $pageNum = 20): array
    {
        $uaId = Arr::get($data, "uaId", "");
        if (false === $this->checkUserActionCanDo($uaId, "data", true)) {
            return [];
        }
        $sort = Arr::get($data, "sort", "");
        $order = Arr::get($data, "sortOrder", "");
        $id = Arr::get($data, "id");
        $model = FastQueryModel::teacherEvaluateTargetUser()->findOrFail($id);
        $targetUaid = $model->ua_id;
        $month = Arr::get($data, "month", "");
        $search = Arr::get($data, "search", "");

        $list = FastQueryModel::teacherEvaluate()
            ->select(["teacher_evaluate.eval_score", "teacher_evaluate.status", "teacher_evaluate.eval_from", "teacher_evaluate.eval_time", "teacher_evaluate.push_status", "teacher_evaluate.eval_content"
            ,"teacher_evaluate_student_record.student_name", "teacher_evaluate_student_record.student_code", "teacher_evaluate_student_record.owner_mpg", "teacher_evaluate_student_record.owner_team", "teacher_evaluate.ua_id", "teacher_evaluate.config_id", "teacher_evaluate.belong_date", "teacher_evaluate.id"
            ,"teacher_evaluate.anonymous"
            ])
            ->join("teacher_evaluate_student_record", "teacher_evaluate.belong_student_eval_id", "=", "teacher_evaluate_student_record.id")
            ->where("main_id", ">", 0)
            ->when(!$month, function ($query) use ($id) {
                return $query->where("belong_user_eval_id", $id);
            })
            ->when($month, function ($query) use ($month, $targetUaid) {
                //符合条件的老师
                $query->where("target_ua_id", $targetUaid);
                return $query->where("teacher_evaluate.belong_date", $month);
            })
           ->when($search, function ($query) use ($search) {
               //找学生
               return $query->where("teacher_evaluate.ua_id", $search);
           });
        if ($sort) {
            $list->orderBy($sort, $order);
        } else {
            $list = $list->orderBy("teacher_evaluate.status", "DESC")
                ->orderBy("teacher_evaluate_student_record.owner_mpg")
                ->orderBy("teacher_evaluate_student_record.owner_team")
                ->orderBy("teacher_evaluate_student_record.student_code");
        }
        $list = $list->paginate($pageNum);
        $mo = DateUtil::getFinancialMonth(date("Y-m-d"));
        if ($list->items()) {
            $item = collect($list->items());
            $first = $item->first();
            //当期学生和教师关系
            $studentTeachers = FastQueryModel::teacherEvaluate()
                ->join("teacher_evaluate_target_user", "teacher_evaluate_target_user.id", "teacher_evaluate.belong_user_eval_id")
                ->selectRaw("sc_teacher_evaluate.ua_id, user_name,user_code,(IFNULL(online_eval_num, 0)+IFNULL(offline_eval_num, 0)) as eval_num")
                ->where("teacher_evaluate_target_user.belong_date", $first->belong_date)
                ->whereIn("teacher_evaluate.ua_id", $item->pluck("ua_id"))
                ->groupBy(["teacher_evaluate.ua_id", "teacher_evaluate_target_user.ua_id"])
                ->get();
            $studentTeachers = $studentTeachers->groupBy("ua_id");
            $canEdit = $this->checkUserActionCanDo($uaId, "edit", true);
            $anonymousHide = $this->checkUserActionCanDo($uaId, "anonymousHide", true);
            $share = $this->checkUserActionCanDo($uaId, "share", true);
            foreach ($list->items() as &$item) {
                $item["teachers"] = $studentTeachers->get(Arr::get($item, "ua_id"), []);
                $item->belong_date = Carbon::parse($item->belong_date)->format("Y-m");
                if ($item->eval_time) {
                    $item->eval_time = Carbon::parse($item->eval_time)->format("Y-m-d H:i:s");
                }
                if ($item->push_status) {
                    $item->push_status = Carbon::parse($item->push_status)->format("Y-m-d H:i:s");
                }
                $item->canEdit = false;
                if ($canEdit) {
                    $item->canEdit = true;
                }
                //只有当期财务月有按钮
                $item->canShare = $item->status == 0 && $share && $mo == $item->belong_date;
                //评论的 如果需要匿名的隐藏评论内容
                if ($item->status && $anonymousHide && $item->anonymous) {
                    $item->eval_score = "-";
                    $item->eval_content = "-";
                }
            }
            unset($item);
        }
        return $list->toArray();
    }

    private function _myTeamEvalUserId($uaId)
    {
        if (!$uaId) {
            return false;
        }
        //找到我的团队
        //GM 查看自己MPG的员工
        //品控查看 所有数据
        $level = $this->checkUserActionCanDo($uaId);

        if ($level == "all") {
            return "all";
        }
        //get my mpg
        $model = FastQueryModel::crmUser()->where("ua_id", $uaId)->first();
        if (!$model) {
            return false;
        }
        if (Str::startsWith($level, "mpg") && Str::contains($level,"owner")) {
            //寻找当前财务月的团队
            $findMpg = "select b.gmid from cas_crm.vtiger_user2team a join cas_crm.vtiger_team b on b.teamid = a.teamid where a.userid = ?";
            $mpg = DB::select($findMpg, [$model->user_id]);
            if ($mpg) {
                return ["mpg_id" => (int) Arr::get($mpg, 0)->gmid];
            }
        }

        if (Str::startsWith($level, "team") && Str::contains($level,"owner")) {
            return ["team_id" => $model->team_id];
        }

        if (Str::startsWith($level, "sql")) {
            return ["sql" => explode(":", $level)[1]];
        }

        return false;
    }


    public function exportTeacherEvaluate($uaId, $data) {
        if (false === $this->checkUserActionCanDo($uaId, "export", true)) {
            return false;
        }
        $type = Arr::get($data, "type", "teacher");

        if ($type == "all") {
            return $this->exportTeacherEvaluateOfAll($uaId, $data);
        } else {
            return $this->exportTeacherEvaluateOfTeacher($uaId, $data);
        }



    }

    private function formatExportMonth($month) {
        if (! $month) {
            return [];
        }
        $months = explode(",", $month);
        if (count($months) != 2) {
            return [$month];
        }

        $start = new Carbon($months[0]);
        $end = new Carbon($months[1]);

        if ($start > $end) {
            return [];
        }

        $result = [];
        while ($start <= $end) {
            $result[] = $start->format('Y-m-01');
            $start->addMonth();
        }
        return $result;
    }

    public function exportTeacherEvaluateOfAll($uaId, $data)
    {
        ini_set("max_execution_time", env("MAX_EXECUTION_TIME", 180));
        $findMyData = $this->_myTeamEvalUserId($uaId);
        $month = Arr::get($data, "month");
        $month = $this->formatExportMonth($month);
        $team = Arr::get($data, "team");
        $teacher = Arr::get($data, "search");
        $list = FastQueryModel::teacherEvaluateTargetUser()
            ->join("teacher_evaluate", "teacher_evaluate.belong_user_eval_id", "teacher_evaluate_target_user.id")
            ->join("teacher_evaluate_student_record", "teacher_evaluate_student_record.id", "teacher_evaluate.belong_student_eval_id")
            ->selectRaw("
            sc_teacher_evaluate.belong_date,
            sc_teacher_evaluate.eval_score,
            sc_teacher_evaluate.eval_content,
            sc_teacher_evaluate.eval_from,
            sc_teacher_evaluate.eval_time,
            sc_teacher_evaluate.push_status,
            sc_teacher_evaluate.anonymous,
            sc_teacher_evaluate_student_record.student_name,
            sc_teacher_evaluate_student_record.student_code,
            sc_teacher_evaluate_student_record.owner_mpg,
            sc_teacher_evaluate_student_record.owner_team,
            sc_teacher_evaluate_student_record.owner_name,
            sc_teacher_evaluate_student_record.owner_code,
            sc_teacher_evaluate_student_record.need_num as student_need_num,
            sc_teacher_evaluate_student_record.fill_num as student_fill_num,
            sc_teacher_evaluate_target_user.mpg,
            sc_teacher_evaluate_target_user.team,
            sc_teacher_evaluate_target_user.user_name,
            sc_teacher_evaluate_target_user.user_code,
            sc_teacher_evaluate_target_user.need_num,
            sc_teacher_evaluate_target_user.reach_num,
            sc_teacher_evaluate_target_user.offline_eval_num,
            sc_teacher_evaluate_target_user.online_eval_num,
            sc_teacher_evaluate_target_user.online_eval_score,
            sc_teacher_evaluate_target_user.offline_eval_score,
             sc_teacher_evaluate_target_user.high_eval_score_num as high_eval_9")
            ->when($month, function ($query) use ($month) {
                return $query->whereIn("teacher_evaluate_target_user.belong_date", $month);
            })
            ->when($team, function ($query) use ($team) {
                return $query->where("teacher_evaluate_target_user.team_id", $team);
            })
            ->when($teacher, function ($query) use ($teacher) {
                return $query->where("teacher_evaluate_target_user.ua_id", $teacher);
            })
            ->when($findMyData && $findMyData != "all", function ($query) use ($findMyData) {
                if (isset($findMyData["sql"])) {
                    $query->whereRaw($findMyData["sql"]);
                } else {
                    $query->where($findMyData);
                }
            })
            ->where("main_id", ">", 0)
            ->orderBy("teacher_evaluate.belong_date","desc")
            ->get();
        $anonymousHide = $this->checkUserActionCanDo($uaId, "anonymousHide", true);
        return collect($list)->map(function ($item)  use ($anonymousHide){
            return [
                "month" => date("Y-m", strtotime($item->belong_date)),
                "mpg" => $item->mpg,
                "team" => $item->team,
                "teacher" => $item->user_name,
                "teacher_code" => $item->user_code,
                "need_num" => $item->need_num,
                "reach_num" => $item->reach_num,
                "eval_num" => (int) $item->online_eval_num + (int) $item->offline_eval_num,
                "avg" => (int) $item->online_eval_num + (int) $item->offline_eval_num  > 0 ? bcdiv((int) $item->online_eval_score + (int) $item->offline_eval_score, (int) $item->online_eval_num + (int) $item->offline_eval_num, 2) : "--",
                "satisfy" => (int) $item->online_eval_num + (int) $item->offline_eval_num  > 0 ? bcdiv($item->high_eval_9 * 100, (int) $item->online_eval_num + (int) $item->offline_eval_num , 2) . "%" : "--",
                "student_name" => $item->student_name,
                "student_code" => $item->student_code,
                "eval_score" => $anonymousHide && $item->anonymous ? "匿名" : ($item->eval_score === null ? "" : $item->eval_score),
                "eval_content" => $anonymousHide && $item->anonymous ? "匿名" : ($item->eval_content === null ? "" : $item->eval_content),
                "push_status" => $item->push_status ? date("Y-m-d H:i:s", strtotime($item->push_status)) : "",
                "eval_time" => $item->eval_time === null ? "" : date("Y-m-d H:i:s", strtotime($item->eval_time)),
                "student_need_num" => $item->student_need_num,
                "student_fill_num" => $item->student_fill_num,
                "owner_mpg" => $item->owner_mpg,
                "owner_team" => $item->owner_team,
                "owner_name" => $item->owner_name,
                "owner_code" => $item->owner_code,
                "eval_from" => $item->eval_from,
            ];
        });
    }

    public function exportTeacherEvaluateOfTeacher($uaId, $data) {
        $findMyData = $this->_myTeamEvalUserId($uaId);
        $month = Arr::get($data, "month");
        $month = $this->formatExportMonth($month);
        $team = Arr::get($data, "team");
        $teacher = Arr::get($data, "search");
        $list = FastQueryModel::teacherEvaluateTargetUser()
            ->selectRaw("*, high_eval_score_num as high_eval_9")
            ->when($month, function ($query) use ($month) {
                return $query->whereIn("belong_date", $month);
            })
            ->when($team, function ($query) use ($team) {
                return $query->where("team_id", $team);
            })
            ->when($teacher, function ($query) use ($teacher) {
                return $query->where("ua_id", $teacher);
            })
            ->when($findMyData && $findMyData != "all", function ($query) use ($findMyData) {
                if (isset($findMyData["sql"])) {
                    $query->whereRaw($findMyData["sql"]);
                } else {
                    $query->where($findMyData);
                }
            })
            ->orderBy("belong_date","desc")
            ->get();
        return collect($list)->map(function ($item) {
            return [
                "month" => date("Y-m", strtotime($item->belong_date)),
                "mpg" => $item->mpg,
                "team" => $item->team,
                "teacher" => $item->user_name,
                "teacher_code" => $item->user_code,
                "need_num" => $item->need_num,
                "reach_num" => $item->reach_num,
                "eval_num" => (int) $item->online_eval_num + (int) $item->offline_eval_num,
                "avg" => (int) $item->online_eval_num + (int) $item->offline_eval_num  > 0 ? bcdiv((int) $item->online_eval_score + (int) $item->offline_eval_score, (int) $item->online_eval_num + (int) $item->offline_eval_num, 2) : "--",
                "satisfy" => (int) $item->online_eval_num + (int) $item->offline_eval_num  > 0 ? bcdiv((int) $item->high_eval_9 * 100, (int) $item->online_eval_num + (int) $item->offline_eval_num , 2) . "%" : "--",
            ];
        });
    }


    public function teacherEvaluateSearchParam($uaId) {
        if (false === $this->checkUserActionCanDo($uaId, "data", true)) {
            return false;
        }
        $findMyData = $this->_myTeamEvalUserId($uaId);
        $base = FastQueryModel::teacherEvaluateTargetUser()
            ->when($findMyData && $findMyData != 'all', function ($query) use ($findMyData) {
                if (isset($findMyData["sql"])) {
                    $query->whereRaw($findMyData["sql"]);
                } else {
                    $query->where($findMyData);
                }
            });
        $month = (clone $base)->select("belong_date")->groupBy("belong_date")->get();

        $user = (clone $base)->select(["ua_id", "user_name", "user_code", "team_id", "team"])->groupBy("ua_id")->get();

        $mo = [];
        foreach ($month as $item) {
            $mo[$item->belong_date] = date("Y-m", strtotime($item->belong_date));
        }
        $userData = [
            [
                "key" => "",
                "value" => "全部",
                "children" => [],
            ]
        ];
        $_user = $user->groupBy("team_id");
        foreach ($_user as $teamId => $items) {
            $item = $items->first();
            $tmp = [
                "key" => $teamId,
                "value" => $item->team,
                "children" => []
            ];
            foreach ($items as $item) {
                $_tmp = [
                    "key" => $item->ua_id,
                    "value" => $item->user_name . " " . $item->user_code,
                ];
                $userData[0]["children"][] = $_tmp;
                $tmp["children"][] = $_tmp;
            }
            $userData[] = $tmp;
        }


        return [
            "user" => $userData,
            "month" => !$mo ? new \stdClass() : $mo,
            "export" => $this->checkUserActionCanDo($uaId, "export", true),
            "import" => $this->checkUserActionCanDo($uaId, "import", true),
        ];
    }

    public function teacherEvaluateSearchParam2($uaId, $teacherEvalId) {
        if (false === $this->checkUserActionCanDo($uaId, "data", true)) {
            return [];
        }
        //用于 默认值
        $teacherEval = FastQueryModel::teacherEvaluateTargetUser()->where("id", $teacherEvalId)->findOrFail($teacherEvalId);

        $findMyData = $this->_myTeamEvalUserId($uaId);
        $base = FastQueryModel::teacherEvaluateTargetUser()
            ->join("teacher_evaluate", "teacher_evaluate_target_user.id", "teacher_evaluate.belong_user_eval_id")
            ->join("teacher_evaluate_student_record", "teacher_evaluate_student_record.id", "teacher_evaluate.belong_student_eval_id")
            ->where("teacher_evaluate.target_ua_id", $teacherEval->ua_id)
            ->when($findMyData && $findMyData != 'all', function ($query) use ($findMyData) {
                if (isset($findMyData["sql"])) {
                    $query->whereRaw($findMyData["sql"]);
                } else {
                    $query->where($findMyData);
                }
            });
        $month = (clone $base)->select(["teacher_evaluate.belong_date", "teacher_evaluate_student_record.ua_id", "student_name", "student_code"])->groupBy(["teacher_evaluate.belong_date", "teacher_evaluate_student_record.ua_id"])->get();
        $month = $month->groupBy("belong_date");
        $data = [];
        foreach ($month as $mo => $item) {
            $data[] = [
                "key" => $mo,
                "value" => $mo,
                "selected" => $teacherEval->belong_date == $mo,
                "children" => $item
            ];
        }
        return $data;
    }

    public function editTeacherEvaluate($uaId, $id, $data) {
        if (false === $this->checkUserActionCanDo($uaId, "edit", true)) {
            return false;
        }
        $findMyData = $this->_myTeamEvalUserId($uaId);

        $model = FastQueryModel::teacherEvaluateTargetUser()
            ->join("teacher_evaluate", "teacher_evaluate_target_user.id", "teacher_evaluate.belong_user_eval_id")
            ->join("teacher_evaluate_student_record", "teacher_evaluate_student_record.id", "teacher_evaluate.belong_student_eval_id")
            ->when($findMyData && $findMyData != 'all', function ($query) use ($findMyData) {
                if (isset($findMyData["sql"])) {
                    $query->whereRaw($findMyData["sql"]);
                } else {
                    $query->where($findMyData);
                }
            })
            ->where("teacher_evaluate.id", $id)->first();
        if (!$model) {
            return "你没有权限操作";
        }
//        if ($model->eval_from == "TAP" && $model->status == 1) {
//            return "你不能操作这条评价";
//        }
        $score = (int) Arr::get($data, "score", -1);
        if ($score < 0 || $score > 10) {
            return "分数必须在0-10之间";
        }
        $content = Arr::get($data, "content");
        if (Str::length($content) > 500) {
            return "评价内容过长";
        }
        $time = Arr::get($data, "time");
        if ($time && !strtotime($time)) {
            return "时间格式错误";
        }
        DB::beginTransaction();
        $model = FastQueryModel::teacherEvaluate()->find($id);
        if (!$model) {
            DB::rollBack();
            return "你没有权限操作";
        }
//        if ($model->eval_from == "TAP" && $model->status == 1) {
//            return "你不能操作这条评价";
//        }
        $model->eval_from = $model->eval_from == "导入新增" ? $model->eval_from : "线下";
        $model->eval_score = $score;
        if ($content) {
            $model->eval_content = $content;
        }
        if ($time) {
            $model->eval_time = $time;
        }
        $model->status = 1;
        $model->save();
        DB::commit();
        $this->_updateTeacherEvaluateStudentRecordNum2($model->belong_student_eval_id);
        $this->afterEvalCal([$model]);
        return true;
    }

    public function getTeacherEvaluateDetail($id)
    {
        $model = FastQueryModel::teacherEvaluateTargetUser()
            ->select(["teacher_evaluate.belong_date", "teacher_evaluate_student_record.ua_id", "student_name", "student_code",
                "teacher_evaluate_target_user.user_name", "teacher_evaluate_target_user.user_code", "teacher_evaluate.id", "eval_score", "eval_content", "status", "eval_time"])
            ->join("teacher_evaluate", "teacher_evaluate_target_user.id", "teacher_evaluate.belong_user_eval_id")
            ->join("teacher_evaluate_student_record", "teacher_evaluate_student_record.id", "teacher_evaluate.belong_student_eval_id")
            ->where("teacher_evaluate.id", $id)->first();

        if (!$model) {
            return [];
        }
        $model->eval_time = $model->eval_time ? date("Y-m-d H:i:s", strtotime($model->eval_time)) : null;
        $model->belong_date = date("Y-m", strtotime($model->belong_date));
        return $model;
    }

    private function configEvalTips($config)
    {
        return (bool) Arr::get($config, "evalTips", true);
    }


    public function dealTeacherEvaluatePermission($data) {
        $uaId = Arr::get($data, "uaId", "");
        if (!$uaId) {
            return false;
        }
        $action = Arr::get($data, "action", "add");

        $_data = Arr::get($data, "data", []);

        $user = FastQueryModel::config()->where("type", "=" ,"CSAT用户白名单")->first();
        if (!$user) {
            return false;
        }
        $configs = $user->configs;
        $configs = collect(json_decode($configs, true));
        if ($action == "delete" && !$configs->has($uaId)) {
            return false;
        } else if ($action == "delete") {
            $configs->forget($uaId);
            FastQueryModel::config()->where("type", "=" ,"CSAT用户白名单")->update(["configs" => json_encode($configs->toArray(), JSON_UNESCAPED_UNICODE)]);
        } else {
            $configs->put($uaId, $_data);
            FastQueryModel::config()->where("type", "=" ,"CSAT用户白名单")->update(["configs" => json_encode($configs->toArray(), JSON_UNESCAPED_UNICODE)]);
        }
        return true;
    }

    public function doEvalShare($evalId, $uaId)
    {
        //分享具有最高权限
        if (false === $this->checkUserActionCanDo($uaId, "share", true)) {
            return false;
        }

        $model = FastQueryModel::teacherEvaluate()->find($evalId);
        if (!$model || $model->main_id <= 0) {
            return false;
        }

        $now = date("Y-m-d");

        $financialMonth = DateUtil::getFinancialMonth($now);
        if ($financialMonth != $model->belong_date) {
            return false;
        }

        $shareInsert = [
            "belong_date" => $financialMonth,
            "ua_id" => $uaId,
            "target_ua_id" => $model->ua_id,
            "from_eval_id" => $model->id,
            "share_key" => Str::random(32),
        ];

        $shareModel = FastQueryModel::teacherEvaluateShare()->where("belong_date", $financialMonth)->where("ua_id", $uaId)->where("target_ua_id", $model->ua_id)->first();
        if (!$shareModel) {
            try {
                //防止share碰撞
                $shareId = FastQueryModel::teacherEvaluateShare()->insertGetId($shareInsert);
                $shareKey = $shareInsert["share_key"];
            } catch (Exception $e) {
                return false;
            }
        } else {
            $shareKey = $shareModel->share_key;
            $shareId = $shareModel->id;
        }

        //判断是否已经存在数据
        $shareModelInfo = FastQueryModel::teacherEvaluateShareInfo()->where("belong_date", $financialMonth)->where("target_ua_id", $model->ua_id)->exists();
        if (!$shareModelInfo) {
            DB::beginTransaction();
            $list = FastQueryModel::teacherEvaluate()
                ->where("ua_id", $model->ua_id)
                ->where("belong_date", $financialMonth)
                ->where("main_id", ">", 0)
                ->where("status", 0)->get();
            if (!$list || $list->isEmpty()) {
                DB::rollBack();
                return false;
            }
            $shareModelInfoInsert = [];
            foreach ($list as $item) {
                $shareModelInfoInsert[] = [
                    "belong_date" => $financialMonth,
                    "target_ua_id" => $model->ua_id,
                    "share_id" => $shareId,
                    "eval_id" => $item->id,
                    "status" => 0,
                    "from_share_id" => 0,
                ];
            }
            FastQueryModel::teacherEvaluateShareInfo()->insert($shareModelInfoInsert);
            DB::commit();
        }
        return $this->generalShareContent($financialMonth, $model->ua_id, $shareKey);
    }

    private function generalShareContent($belongDate, $targetUaId, $shareKey) {
        $url = env("APP_URL") . "/csatH5?shareKey=" . $shareKey;
        $list = FastQueryModel::teacherEvaluateShareInfo()
            ->select(["teacher_evaluate.ua_id", "teacher_evaluate.target_ua_id", "teacher_evaluate_student_record.student_name", "teacher_evaluate_student_record.student_code",
                "teacher_evaluate_target_user.team", "teacher_evaluate_target_user.user_name", "teacher_evaluate_target_user.user_code"])
            ->join("teacher_evaluate", "teacher_evaluate.id", "=", "teacher_evaluate_share_info.eval_id")
            ->join("teacher_evaluate_target_user", "teacher_evaluate_target_user.id", "=", "teacher_evaluate.belong_user_eval_id")
            ->join("teacher_evaluate_student_record", "teacher_evaluate_student_record.id", "=", "teacher_evaluate.belong_student_eval_id")
            ->where("teacher_evaluate_share_info.belong_date", $belongDate)
            ->where("teacher_evaluate_share_info.target_ua_id", $targetUaId)
            ->where("teacher_evaluate_share_info.status", 0)
            //防止被提前评价
            ->where("teacher_evaluate.status", 0)
            ->orderBy("teacher_evaluate.sort", "DESC")
            ->get();
        if (!$list || $list->isEmpty()) {
            return false;
        }

        $data = ["name" => "", "smpid" => "", "teacher" => [], "url" => $url, "mo" => DateUtil::getFinancialMonth(date("Y-m-d"), "Y年m月")];
        foreach ($list as $item) {
            $data["name"] = $item->student_name;
            $data["smpid"] = $item->student_code;
            $data["teacher"][] = $item->team . " " . $item->user_name . " " . $item->user_code;
        }
        $data["teacher"] = implode($data["teacher"], "、");
        return $data;
    }

    public function shareEvaluateUser($shareKey)
    {
        //获取列表 需要剔除当前财务月已经评价的
        $financialMonth = DateUtil::getFinancialMonth(date("Y-m-d"));
        $shareEvaluate = FastQueryModel::teacherEvaluateShare()
            ->select(["teacher_evaluate_share.*", "teacher_evaluate_student_record.student_name", "teacher_evaluate_student_record.student_code"])
            ->join("teacher_evaluate", "teacher_evaluate.id", "=", "teacher_evaluate_share.from_eval_id")
            ->join("teacher_evaluate_student_record", "teacher_evaluate_student_record.id", "=", "teacher_evaluate.belong_student_eval_id")
            ->where("share_key", $shareKey)->first();
        if (!$shareEvaluate) {
            return false;
        }
        //查看是否存在未完成的调研
        $shareEvaluateInfo = $this->getShareEvaluateInfoWithUnEval($shareEvaluate);
        if (!$shareEvaluateInfo || $shareEvaluateInfo->isEmpty()) {
            return "暂无需要评价的教师～";
        }
        $data = [];
        foreach ($shareEvaluateInfo as $model) {
            $ext = json_decode($model->ext, true);
            $p = collect(Arr::get($ext, "product", []))->map(function ($item) {
                if (in_array($item, ["CAE", "CPS"])) {
                    return "教育顾问";
                }
                return $item;
            })->unique()->implode("|");
            $data[] = [
                "id" => Arr::get($ext, "id"),
                "evalId" => $model->id,
                "name" => Arr::get($ext, "name"),
                "product" => $p,
                "lesson" => Arr::get($ext, "lesson"),
                "sort" => $model->sort,
                "teacher_eval_num" => $model->teacher_eval_num,
                'code' => $model->code,
            ];
        }
        return ["name" => $shareEvaluate->student_name, "smpid" => $shareEvaluate->student_code, "data" => $data];
    }

    private function getShareEvaluateInfoWithUnEval($shareEvaluate, $select = ["teacher_evaluate.*"])
    {
        return  FastQueryModel::teacherEvaluateShareInfo()
            ->select($select)
            ->join("teacher_evaluate", "teacher_evaluate.id", "=", "teacher_evaluate_share_info.eval_id")
            ->where("teacher_evaluate_share_info.target_ua_id", $shareEvaluate->target_ua_id)
            ->where("teacher_evaluate_share_info.belong_date", $shareEvaluate->belong_date)
            ->where("teacher_evaluate_share_info.status", 0)
            ->where("teacher_evaluate.status", 0)
            //提出当期已经评价的 后面评价的
            ->whereNotExists(function (Builder $q){
                $q->select(DB::raw(1))
                    ->from(DB::raw("sc_teacher_evaluate as sc_te") )
                    ->whereColumn("te.ua_id", "teacher_evaluate.ua_id")
                    ->whereColumn("te.target_ua_id", "teacher_evaluate.target_ua_id")
                    ->whereColumn(DB::raw("CAST(sc_te.belong_date as DATE)"), ">=", "teacher_evaluate_share_info.belong_date")
                    ->where("te.status", 1);
            })
            ->orderBy("teacher_evaluate.sort", "DESC")
            ->get();
    }


    public function submitShareEvaluate($data)
    {
        $shareKey = Arr::get($data, "shareKey");
        $isAnonymous = (int) Arr::get($data,"isAnonymous", 0);
        $isAnonymous = $isAnonymous >= 1 ? 1: 0;
        $recordId = 0;
        $activeNum = 0;
        $eval = Arr::get($data, "eval", []);
        $insertData = [];
        $insertDataShareInfo = [];
        $tagData = [];
        $financialMonth = DateUtil::getFinancialMonth(date("Y-m-d"));


        $shareEval = FastQueryModel::teacherEvaluateShare()->where("share_key", $shareKey)->first();
        if (!$shareEval) {
            return "分享评价不存在";
        }
        $shareEvalInfo = $this->getShareEvaluateInfoWithUnEval($shareEval, ["teacher_evaluate_share_info.*"]);

        if (!$shareEvalInfo || $shareEvalInfo->isEmpty()) {
            return "已完成评价";
        }

        $shareEvalInfo = $shareEvalInfo->groupBy("eval_id");

        foreach ($eval as $item) {
            if (mb_strlen(Arr::get($item, "content")) > 200) {
                return "评价内容不能超过200个字符";
            }
            $id = Arr::get($item, "evalId");
            $score = Arr::get($item, "score", -1);
            $content = Arr::get($item, "content");
            $tags = Arr::get($item, "tags", []);
            $_tags = "";
            if (!$shareEvalInfo->has($id)) {
                continue;
            }
            $_shareEvalInfo = collect($shareEvalInfo->get($id))->first();
            //要看是否属于当期需要评价的
            $evalEntity = FastQueryModel::teacherEvaluate()->where("id", $id)->first();
            //不存在的 构建关系
            if (!$evalEntity) {
                continue;
            }
            //旧的已经被评价了 不处理了
            if ($evalEntity->status != 0) {
                $activeNum++;
                continue;
            }
            //分数
            if ((int) $score < 0) {
                continue;
            }

            $evalEntity->eval_from = "分享评价";
            if($evalEntity->belong_date == $financialMonth && $evalEntity->main_id <= 0 || $evalEntity->belong_date != $financialMonth) {
                $_evalEntity = FastQueryModel::teacherEvaluate()
                    ->where("belong_date", $financialMonth)
                    ->where("main_id" , ">", 0)
                    ->where("target_ua_id", $evalEntity->target_ua_id)
                    ->where("ua_id", $evalEntity->ua_id)
                    ->first();
                if (!$_evalEntity) {
                    //当期不存在
                    $findRelation = [
                        "ua_id" => $evalEntity->ua_id,
                        "target_ua_id" => $evalEntity->target_ua_id,
                        "belong_date" => $financialMonth,
                    ];
                    $student = new \stdClass();
                    $student->ua_id = $evalEntity->ua_id;
                    $teacher = new \stdClass();
                    $teacher->ua_id = $evalEntity->target_ua_id;
                    $evalEntity = $this->createTeacherStudentEvalRelation($findRelation, $student, $teacher, $financialMonth, "分享新增");
                    if (!$evalEntity) {
                        return "创建关系失败";
                    }
                } else {
                    $evalEntity = $_evalEntity;
                    $evalEntity->eval_from = "分享评价";
                }
            }

            if ($tags && is_array($tags)) {
                $tags = array_unique($tags);
                $_tags = implode("/", $tags);
                foreach ($tags as $tag) {
                    $tagData[] = [
                        "belong_teacher_evaluate_id" => $evalEntity->id,
                        "tag" => $tag
                    ];
                }
            }
            $evalEntity->eval_score = (int) $score;
            $evalEntity->eval_content = $_tags ? $_tags . "\n" . $content : $content;
            $evalEntity->status = 1;
            $evalEntity->eval_time = date("Y-m-d H:i:s");
            if ($isAnonymous) {
                $evalEntity->anonymous = 1;
            }

            $_shareEvalInfo->status = 1;
            $_shareEvalInfo->from_share_id = $shareEval->id;
            $_shareEvalInfo->final_eval_id = $evalEntity->id;
            $insertDataShareInfo[] = $_shareEvalInfo;

            $insertData[] = $evalEntity;
            $recordId = $evalEntity->belong_student_eval_id;
            $activeNum ++;
        }
        if ($activeNum <= 0) {
            return "提交数据不能为空";
        }
        DB::beginTransaction();
        foreach ($insertData as $item) {
            $item->save();
        }
        foreach ($insertDataShareInfo as $item) {
            $item->save();
        }
        //添加完成
        if ($recordId) {
            $this->_updateTeacherEvaluateStudentRecordNum2($recordId);
        }
        FastQueryModel::teacherEvaluateTags()->insert($tagData);
        DB::commit();
        $this->afterEvalCal($insertData);
        return true;
    }

}
