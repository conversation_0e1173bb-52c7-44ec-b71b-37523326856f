<?php


namespace App\Http\Services\Task;


use App\Utils\Common;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

/**
 * 学生学习任务
 * Class StudentTaskService
 * @package App\Http\Services\task
 */
class StudentTaskService
{
    /**
     * Student task collect num
     */
    public function collect() {
       $emsCollect = Cache::remember("sc:index:collect:ems:". Auth::user()->ua_id, 60, function () {
           return $this->emsCollect();
       });
       $lmsCollect = Cache::remember("sc:index:collect:lms:". Auth::user()->ua_id, 60, function () {
           return $this->lmsCollect();
       });
       return [
           "ems.meetings" => Arr::get($emsCollect, 0),
           "ems.essays" => Arr::get($emsCollect, 1),
           "million.word" => (int) $this->millionWordCollect(),
           "lms.homework" => Arr::get($lmsCollect, 0),
           "lms.test" => Arr::get($lmsCollect, 1),
       ];
    }

    public function emsCollect() {
        $url = env("EMS_URL");
        $uri = "/api/v1/studentMeetingAndEssay";
        $result = Common::getSimpleCurl($url . $uri, ["uaId" => Auth::user()->ua_id]);
        if ($result) {
            $result = json_decode($result, true);
            return Arr::get($result, "data", [0,0]);
        }
        return [0, 0];
    }

    public function lmsCollect() {
        $url = env("LMS_URL");
        $uri = "/api/sc/homework/unFinish";
        $r = [0, 0];
        $result = Common::getSimpleCurl($url . $uri, [], ["cookie" => Common::getCookieStr()]);
        if ($result) {
            $result = json_decode($result, true);
            $r[0] = Arr::get($result, "data.unFinish", 0);
        }
        $uri = "/api/sc/test/unFinish";
        $result = Common::getSimpleCurl($url . $uri, [], ["cookie" => Common::getCookieStr()]);
        if ($result) {
            $result = json_decode($result, true);
            $r[1] = Arr::get($result, "data.unFinish", 0);
        }
        return $r;
    }

    public function millionWordCollect() {
        $url = env("WORD_API_URL") . "/api/task/all/number";
        $result = Common::postSimpleCurl($url, [], ["cookie" => Common::getCookieStr()]);
        if ($result) {
            $result = json_decode($result, true);
            return Arr::get($result, "data", 0);
        }
        return 0;
    }


    public function taskList($target = "ems.essays", $page = 1, $pageNum = 10, $data = []) {
        $r = new \stdClass();
        switch ($target) {
            case "ems.essays":
                $data['page'] = $page;
                $data['pageNum'] = $pageNum;
                $r = $this->emsEssays($data);
                break;
            case "ems.meetings":
                $r = $this->emsMeetings($page, $pageNum);
                break;
            case "lms.test":
                $r = $this->lms($data);
                break;
            case "lms.homework":
                $r = $this->lms($data, "homework");
                break;
            case "million.word":
                $r = $this->word($data);
            default:
                break;
        }
        return $r;
    }

    public function emsMeetings($page = 1, $pageNum = 10) {
        $url = env("EMS_URL") . "/api/v1/studentMeetings";
        $params = [
            "page" => $page,
            "pageNum" => $pageNum,
            "status" => 0,
            "uaId" => Auth::user()->ua_id,
        ];
        $r = Common::getSimpleCurl($url, $params);
        return Arr::get(json_decode($r, true), "data", []);
    }

    public function emsEssays($data) {
        $url = env("EMS_URL") . "/api/v1/studentEssay2";
        $data["uaId"] = Auth::user()->ua_id;
        $data['filterObjs'] = '{"status":["Ongoing"]}';
        unset($data["type"]);
        $r = Common::getSimpleCurl($url, $data);
        return Arr::get(json_decode($r, true), "data", []);

    }

    public function lms($data, $type = "test") {
        $url = env("LMS_URL") . "/api/sc/{$type}/list";
        $r = Common::getSimpleCurl($url, $data, ["cookie" => Common::getCookieStr()]);
        return Arr::get(json_decode($r, true), "data", []);
    }

    public function word($data) {
        $data["hide"] = 1;
        $url = env("WORD_API_URL") . "/api/task/all";
        $r = Common::postSimpleCurl($url, $data, ["cookie" => Common::getCookieStr()]);
        return Arr::get(json_decode($r, true), "data", []);
    }

    /**
     * word任务详情
     * @param $id
     */
    public function taskInfoWord($id) {
        $url = env("WORD_API_URL") . "/api/task/pre/number";
        $r = Common::getSimpleCurl($url, ['task_detail_id' => $id], ["cookie" => Common::getCookieStr()]);
        return Arr::get(json_decode($r, true), "data", []);
    }

    public function taskOfWordAny($data = []) {
        return $this->taskOfAny(env("WORD_API_URL"), $data);
    }

    public function taskOfAny($url, $data = [], $all = false) {
        $targetUri = Arr::get($data, "uri", "");
        $targetUri = $url . $targetUri;
        $targetMethod = Arr::get($data, "method", "GET");
        $targetContentType = Arr::get($data, "contentType", "json");
        $data = Arr::get($data, "data", []);
        $cookie = ["cookie" => Common::getCookieStr()];
        if (strtoupper($targetMethod) == "GET") {
            $r = strtolower($targetContentType) == "json" ? Common::getSimpleCurlByRawData($targetUri, $data, $cookie) : Common::getSimpleCurl($targetUri, $data, $cookie);
        } else {
            $r = strtolower($targetContentType) == "json" ? Common::postSimpleCurlByRawData($targetUri, $data, $cookie) : Common::postSimpleCurl($targetUri, $data, $cookie);
        }
        return $all ? json_decode($r, true) : Arr::get(json_decode($r, true), "data", []);
    }

    public function taskOfLmsAny($data = []) {
        $r = $this->taskOfAny(env("LMS_URL"), $data, true);
        $responseConvert = Arr::get($data, "responseConvert", "object");
        //特殊值转换
        if (!Arr::get($r, "data")) {
            if (strtolower($responseConvert) == 'object') {
                $r['data'] = new \stdClass();
            } else if (strtolower($responseConvert) == 'array') {
                $r['data'] = [];
            } else if (strtolower($responseConvert) == 'null') {
                $r['data'] = null;
            } else {
                $r['data'] = '';
            }
        }
        return $r;
    }

    public function taskOfGemsAny($data)
    {
        $_data = (array) Arr::get($data, 'data', []);
        $_data['sign'] = md5(env("SSO_SIGNATURE_KEY", ""));
        $_data['smpid'] = optional(Auth::user())->user_code;
        $data['data'] = $_data;
        return $this->taskOfAny(env("GEMS_URL"), $data);
    }

}
