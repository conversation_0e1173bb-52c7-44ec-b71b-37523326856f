<?php

namespace App\Http\Controllers\V2;

use App\Models\FastModel\FastQueryModel;
use App\Utils\DateUtil;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GiftRewardController
{
    public function giftRecord(Request $request) {
        $uaId = optional(Auth::user())->ua_id;
        $date = $request->input("date", date("Y-m-d"));
        if (!$uaId) {
            return [];
        }
        // 使用公共方法获取每个月21号到次月20号的时间区间
        $between = DateUtil::getMonthlyPeriodBetween($date);

        $list = FastQueryModel::giftRecord()->where("target_ua_id", $uaId)
            ->selectRaw("*,GROUP_CONCAT(id) as ids")
            ->whereBetween("created_at", $between)
            ->groupBy(["target_ua_id", "ua_id", "created_at"])
            ->paginate();


        $record = collect($list->items());
        $student = $record->groupBy("ua_id")->keys();
        $gifRecords = FastQueryModel::giftRecord()->where("target_ua_id", $uaId)
            ->whereBetween("created_at", $between)->get();




        return [];
    }

    public function giftRankType()
    {
        return [
            "financialMonth" => "当前财务月",
        ];
    }

    /**
     * 分数转化比例
     */
    const SCORE_RATE = 1.0;

    public function giftRank(Request $request) {
        $uaId = optional(Auth::user())->ua_id;
        $type = $request->input("type", "");

        $date = date("Y-m-d");
        if ($type == "financialMonth") {
            //todo debug
            $date = date("Y-m-d", strtotime("-1 month"));
        }
        $between = DateUtil::getMonthlyPeriodBetween($date);
        $list = FastQueryModel::giftRecord()
            ->join(DB::raw("ua.ua_users"), "gift_record.target_ua_id", DB::raw("ua.ua_users.ua_id"))
            ->join(DB::raw("top.ehr_user"), DB::raw("ua.ua_users.user_code"), DB::raw("top.ehr_user.user_code"))
            ->selectRaw("target_ua_id,sum(gift_num) as gift_nums, sum(gift_score) as gift_scores, top.ehr_user.wx_avatar,top.ehr_user.user_name")
            ->whereBetween("gift_record.created_at", $between)
            ->groupBy("target_ua_id")
            ->get();

        $list = collect($list)->sortBy(['gift_scores' => 'desc', 'user_name' => 'asc']);
        $gifts = $this->batchGetUserGiftNums(collect($list)->groupBy("target_ua_id")->keys(), $between);

        $myRank = [
            "score" => 0,
            "rank" => null,
        ];
        $rank = 1;
        $sameRank = 1;
        $lastScore = 0;
        foreach ($list as $item) {
            $item->gifts = [];
            $item->gift_scores = $item->gift_scores * self::SCORE_RATE;
            $item->isMine = $item->target_ua_id == $uaId;
            if ($item->isMine) {

            }
            if ($gifts->has($item->target_ua_id)) {
                $item->gifts = collect($gifts->get($item->target_ua_id))->sortBy(["gift_sort" => "DESC"]);
            }
        }
        return $list;
    }


    private function batchGetUserGiftNums($uaId = [], $between = []) {
        $find = FastQueryModel::giftRecord()
            ->join("config", "gift_record.config_id", "config.id")
            ->selectRaw("sum(gift_num) as gift_nums, target_ua_id, configs")
            ->whereBetween("gift_record.created_at", $between)
            ->whereIn("target_ua_id", $uaId)
            ->groupBy(["target_ua_id", "config_id"])
            ->get();
        // 如果需要进一步处理JSON数据，可以使用以下方式
        $result = $find->map(function ($item) {
            // 解析configs JSON字段
            $configs = json_decode($item->configs, true);
            $item->gift_name = $configs['name'] ?? '';
            $item->gift_image = $configs['image'] ?? '';
            $item->gift_sort = $configs['sort'] ?? 0;
            unset($item->configs);
            return $item;
        });
        return collect($result)->groupBy("target_ua_id");
    }

    /**
     * 获取当前用户的排名信息
     * @param $sortedList
     * @param $uaId
     * @return array
     */
    private function getCurrentUserRank($sortedList, $uaId)
    {
        $currentUserRank = null;
        foreach ($sortedList as $index => $item) {
            if ($item->target_ua_id == $uaId) {
                $currentUserRank = [
                    'rank' => $index + 1,
                    'gift_scores' => $item->gift_scores,
                    'gift_nums' => $item->gift_nums,
                    'user_name' => $item->user_name,
                    'wx_avatar' => $item->wx_avatar
                ];
                break;
            }
        }
        return $currentUserRank;
    }
}
