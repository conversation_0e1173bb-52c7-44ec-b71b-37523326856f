<?php

namespace App\Http\Controllers\V2;

use App\Http\Controllers\Controller;
use App\Http\Services\Evaluate\EvaluateService;
use App\Models\FastModel\FastQueryModel;
use Illuminate\Http\Request;

/**
 * 教评无状态
 */
class TeacherEvaluateStatelessController extends Controller
{
    public function evaluateUser(Request $request, EvaluateService $evaluateService)
    {
        $shareKey = $request->input("shareKey");
        if (!$shareKey) {
            return $this->respondWithJson(1, "链接不存在", 1);
        }

        $r = $evaluateService->shareEvaluateUser($shareKey);
        if (!$r) {
            return $this->respondWithJson(1, "链接不存在", 1);
        }
        if (is_string($r)) {
            return $this->respondWithJson(1, $r, 2);
        }
        return $this->success($r);
    }

    public function submitShareEvaluate(Request $request, EvaluateService $evaluateService)
    {
        $data = [
            "eval" => $request->input("eval"),
            "shareKey" => $request->input("shareKey"),
            "isAnonymous" => (int) $request->input("isAnonymous", 0),
        ];
        $r = $evaluateService->submitShareEvaluate($data);
        if ($r !== true) {
            return $this->success($r);
        }
        return $this->success($r);
    }

    public function config() {
        $model = FastQueryModel::config()->whereNull("deleted_at")->where("type", "CSAT评价标签配置")->first();
        if ($model) {
            $result = json_decode($model->configs, true);
            return $this->success($result);
        }
        return $this->success([]);
    }

}
