<?php

namespace App\Http\Controllers\Survey;

use App\Http\Controllers\Controller;
use App\Http\Services\Point\PointService;
use App\Http\Services\Survey\SurveyService;
use App\Models\Survey\StudentSurveyModel;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SurveyController extends Controller
{
    /**
     * 展示学员应该收到的调研
     * @param Request $request
     * @param SurveyService $surveyService
     * @return JsonResponse
     */
    public function getStudentSurvey(Request $request, SurveyService $surveyService)
    {
        $uaId = Auth::user()->ua_id;
        $dev = $request->input('dev', false);
        $data = $surveyService->getStudentSurvey($uaId, $dev);
        if ($data == null) $data = new \stdClass();
        return $this->response('', 1, $data);
    }


    /**
     * 保存学生填写的调研内容
     * @param Request $request
     * @param SurveyService $surveyService
     * @return JsonResponse
     */
    public function saveStudentSurvey(Request $request, SurveyService $surveyService)
    {
        $uaId = Auth::user()->ua_id;
        $studentSurveyId = $request->input('studentSurveyId');
        //先获取对应的调研
        $studentSurvey = StudentSurveyModel::where("student_survey_id", $studentSurveyId)
            ->where("uaid", $uaId)
            ->where("status", 1)
            ->first();
        $options = $request->input("options");

        if (!$studentSurvey) return $this->response('', 1, "未找到对应调研或改调研已完成填写");

        $result = $surveyService->saveStudentSurveyDetail($studentSurvey, $options);

        //这里要加入积分动作
        $surveyExt = $result->survey->ext;
        if (array_key_exists('point', $surveyExt)) {
            $point = $surveyExt['point'];
            if ($point > 0) {
                (new PointService())->surveyPoint($uaId, $point);
            }
        }

        return $this->response('', 1, $result);
    }
}
