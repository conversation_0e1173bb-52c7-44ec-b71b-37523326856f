<?php

namespace App\Http\Controllers\Message;

use App\Http\Controllers\Controller;
use App\Http\Services\Message\StuMessageService;
use App\Http\Services\Other\ThirdPartRecordService;
use Exception;
use Illuminate\Http\Request;

class StuMessageController extends Controller
{
    public function userReadMessage(Request $request, StuMessageService $stuMessageService)
    {
        $msgType = $request->input('msgType');
        $msgId = $request->input('msgId');
        $part = $request->input('part', '0');
        try {
            if (!(filter_var($msgType, FILTER_VALIDATE_INT) !== false)) {
                return $this->response('msgType不是数字', 0);
            }
            $result = $this->response("", 1, $stuMessageService->userReadMessage($msgType, $msgId, $part));
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $result;
    }

    public function userMessageList(Request $request, StuMessageService $stuMessageService)
    {
        $page = $request->input('page', 1);
        $msgType = $request->input('msgType');
        try {
            if (!(filter_var($page, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('page不是数字');
            }
            if (!(filter_var($msgType, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('msgType不是数字');
            }
            $result = $stuMessageService->userMessageList($page, 20, $msgType);
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function getHasMessage(Request $request, StuMessageService $stuMessageService, ThirdPartRecordService $thirdPartRecordService)
    {
        $result = false;
        try {
            $num = 0;
            $msgList2 = $stuMessageService->userMessageCount();
            foreach ($msgList2 as $item) {
                $num += $item->count;
            }
            if ($num > 0) {
                $result = true;
            }
            if (!$result) {
                $msgList1 = $thirdPartRecordService->unReadMessage("");
                foreach ($msgList1 as $item) {
                    if (is_array($item)) {
                        if (count($item) > 0) {
                            $num += $item[0];
                        } else {
                            $num += 0;
                        }
                    } else {
                        $num += $item;
                    }
                }

                if ($num > 0) {
                    $result = true;
                }
            }

        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function userMessageCount(StuMessageService $stuMessageService)
    {
        return $this->response("", 1, $stuMessageService->userMessageCount());
    }


    public function toastr(StuMessageService $stuMessageService)
    {
        return $this->response("", 1, $stuMessageService->toastr());
    }

    public function toastrRead(Request $request,StuMessageService $stuMessageService)
    {
        try {
            $id = $request->input("id", 0);
            $messageConfigId = $request->input("messageConfigId", 0);
            $r = $stuMessageService->toastrRead($id, $messageConfigId);
        } catch (Exception $e) {
            return $this->response($e->getMessage(), 0);
        }
        return $this->response("", 1, $r);
    }
}
