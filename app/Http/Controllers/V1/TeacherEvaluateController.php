<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use App\Http\Services\Evaluate\EvaluateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TeacherEvaluateController extends Controller
{
    public function evaluateUser(Request $request, EvaluateService $evaluateService): JsonResponse
    {
        $page = $request->input("page", 1);
        $pageNum = $request->input("pageNum", 10);
        $uaId = optional(Auth::user())->ua_id;
        $data = $request->all();
        $data["uaId"] = $uaId;
        return $this->success($evaluateService->evaluateUserList($data, $page, $pageNum));
    }

    public function evaluateList($id, Request $request, EvaluateService $evaluateService): JsonResponse
    {
        $page = $request->input("page", 1);
        $pageNum = $request->input("pageNum", 10);
        $uaId = optional(Auth::user())->ua_id;
        $data = $request->all();
        $data["uaId"] = $uaId;
        $data["id"] = $id;
        return $this->success($evaluateService->evaluateList($data, $page, $pageNum));
    }

    public function importEvaluate(Request $request, EvaluateService $evaluateService): JsonResponse
    {
        return $this->success($evaluateService->importTeacherEvaluate(optional(Auth::user())->ua_id, $request->all()));
    }


    public function exportEvaluate(Request $request, EvaluateService $evaluateService)
    {
        return $this->success($evaluateService->exportTeacherEvaluate(optional(Auth::user())->ua_id, $request->all()));
    }

    public function teacherEvaluateSearchParam(EvaluateService $evaluateService)
    {
        return $this->success($evaluateService->teacherEvaluateSearchParam(optional(Auth::user())->ua_id));
    }

    public function teacherEvaluateSearchParam2($id, EvaluateService $evaluateService)
    {
        return $this->success($evaluateService->teacherEvaluateSearchParam2(optional(Auth::user())->ua_id, $id));
    }

    public function editTeacherEvaluate($id, Request $request, EvaluateService $evaluateService)
    {
        $r = $evaluateService->editTeacherEvaluate(optional(Auth::user())->ua_id, $id, $request->all());
        if ($r === true) {
            return $this->success();
        }
        return $this->response($r, 0);
    }

    public function teacherEvaluateDetail($id, EvaluateService $evaluateService) {
        return $this->success($evaluateService->getTeacherEvaluateDetail($id));
    }

    public function teacherEvaluatePermissionChange(Request $request, EvaluateService $evaluateService)
    {
        return $this->success($evaluateService->dealTeacherEvaluatePermission($request->all()));
    }

    public function doEvalShare($id, Request $request, EvaluateService $evaluateService)
    {
        $r = $evaluateService->doEvalShare($id, optional(Auth::user())->ua_id);
        if ($r === false) {
            return $this->error("分享创建失败");
        }
        return $this->success($r);
    }
}
