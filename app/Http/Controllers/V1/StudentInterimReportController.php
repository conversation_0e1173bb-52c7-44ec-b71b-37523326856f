<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use App\Http\Services\InterimReport\StudentInterimReportService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class StudentInterimReportController extends Controller
{
    public function supervisorList(Request $request, StudentInterimReportService  $interimReportService)
    {
        $data = $request->all();
        //默认走团队数据
        return $this->response("", 1 ,$interimReportService->supervisorList(Arr::get($data, "page", 1), Arr::get($data, "pageNum", 10), Arr::get($data, "search", ""), Arr::get($data,"status"), true, Arr::get($data, "createUser", "")));
    }

    public function teacherList(Request $request, StudentInterimReportService  $interimReportService)
    {
        $data = $request->all();
        return $this->response("", 1 ,$interimReportService->teacherList(Arr::get($data, "page", 1), Arr::get($data, "pageNum", 10), Arr::get($data, "search", ""), Arr::get($data,"status")));
    }


    public function deleteReport($id, StudentInterimReportService  $interimReportService)
    {
        $r = $interimReportService->deleteReport($id);
        return $this->response("", 1, $r);
    }

    public function createReport(Request $request, StudentInterimReportService $interimReportService)
    {
        return $this->response("", 1, $interimReportService->createInterimReport($request->all()));
    }

    public function updateReport(Request $request, StudentInterimReportService $interimReportService)
    {
       return $this->response("", 1, $interimReportService->updateReport($request->all()));
    }

    public function reportInfo($id,Request $request, StudentInterimReportService $interimReportService)
    {
        $data = $request->all();
        return $this->response("", 1, $interimReportService->reportInfo($id, Arr::get($data, "isSupervisor"),  Arr::get($data, "refresh")));
    }

    public function timeOfStudentClassTeacher(Request $request, StudentInterimReportService $interimReportService)
    {
        $data = $request->all();
        return $this->response("", 1, $interimReportService->timeOfStudentClassTeacher(Arr::get($data, "uaId", ""), Arr::get($data,"start", ""), Arr::get($data,"end", "")));
    }

    public function studentList(Request $request, StudentInterimReportService  $interimReportService)
    {
        $data = $request->all();
        return $this->response("", 1, $interimReportService->supervisorStudent(Arr::get($data, "isMp", false)));
    }

    public function createUser(Request $request, StudentInterimReportService  $interimReportService)
    {
        $data = $request->all();
        //默认走团队的创建人
        return $this->response("", 1, $interimReportService->createUser(true));
    }

}
