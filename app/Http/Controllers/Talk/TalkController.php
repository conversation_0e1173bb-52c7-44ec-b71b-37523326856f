<?php

namespace App\Http\Controllers\Talk;

use App\Http\Controllers\Controller;
use App\Http\Services\Talk\TalkService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TalkController extends Controller
{
    public function getTopTalk(TalkService $talkService)
    {
        return $this->response("", 1, $talkService->getTopTalk());
    }

    public function getAllTalks(Request $request, TalkService $talkService)
    {
        $page = $request->input('page', 1);
        try {
            if (!(filter_var($page, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('page不是数字');
            }
            $result = $talkService->getAllTalksV2($page, 30);
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function getMyJoinTalks(Request $request, TalkService $talkService)
    {
        $page = $request->input('page', 1);
        try {
            if (!(filter_var($page, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('page不是数字');
            }
            $result = $talkService->getMyJoinTalks($page, 30);
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function getTalkDetail(Request $request, TalkService $talkService)
    {
        $talkId = $request->input('talkId');
        try {
            if (!(filter_var($talkId, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('talkId不是数字');
            }
            $result = $talkService->getTalkDetail($talkId);
        } catch (Exception $exception) {
            return $this->myResponse($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    /** 错误提示特殊处理,前端要求,这种错误时候message=error,errorMessage返回错误信息
     * @param $message
     * @param $code
     * @param $data
     * @param $headers
     * @param $options
     * @param $errorCode
     * @return JsonResponse
     */
    function myResponse($message = '', $code = 1, $data = [], $headers = [], $options = 0, $errorCode = 0): JsonResponse
    {
        $_data = ['success' => $code === 0 ? 0 : 1, 'message' => 'error', 'data' => $data, 'errorCode' => $errorCode, 'code' => $code === 0 ? 0 : 1, 'errorMessage' => $message ?: 'success',];
        return new JsonResponse($_data, 200, $headers, $options);
    }

    public function getTalkComments(Request $request, TalkService $talkService)
    {
        $page = $request->input('page', 1);
        $talkId = $request->input('talkId');
        $hot = $request->input('hot');
        $new = $request->input('new');
        $me = $request->input('me');
        $pageNum = $request->input('pageNum', 30);
        try {
            if (!(filter_var($page, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('page不是数字');
            }
            if (!(filter_var($talkId, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('talkId不是数字');
            }
            $result = $talkService->getTalkComments($talkId, $page, $pageNum, $hot, $new, $me);
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function getChildComments(Request $request, TalkService $talkService)
    {
        $page = $request->input('page', 1);
        $talkId = $request->input('talkId');
        $commentId = $request->input('commentId');
        try {
            if (!(filter_var($page, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('page不是数字');
            }
            if (!(filter_var($talkId, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('talkId不是数字');
            }
            if (!(filter_var($commentId, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('commentId不是数字');
            }
            $result = $talkService->getChildComments($talkId, $commentId, $page, 10, false);
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function getRoundOtherTalks(Request $request, TalkService $talkService)
    {
        $talkId = $request->input('talkId');
        try {
            if (!(filter_var($talkId, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('talkId不是数字');
            }
            $result = $talkService->getRoundOtherTalks($talkId);
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function saveTalkComment(Request $request, TalkService $talkService)
    {
        $talkId = $request->input('talkId');
        $toCommentId = $request->input('toCommentId');
        $sourceCont = $request->input('sourceCont', '');
        $file = $request->file('file');
        $replyStr = $request->input('reply', 'true');
        $reply = true;
        if ($replyStr != 'true') {
            $reply = false;
        }
        try {
            if (!(filter_var($talkId, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('talkId不是数字');
            }
            if (!(filter_var($toCommentId, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('toCommentId不是数字');
            }
            if (Str::length($sourceCont) == 0 && empty($file)) {
                throw new Exception('评论不能为空');
            }
            $pattern = '/<img[^>]+>/i';
            $replacement = '';
            $filterCont = preg_replace($pattern, $replacement, $sourceCont);
            $result = $talkService->saveTalkComment($talkId, $toCommentId, $file, $sourceCont, $filterCont, $reply);
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response($result[0], 1, $result[1]);
    }

    public function delTalkComment(Request $request, TalkService $talkService)
    {
        $commentId = $request->input('commentId');
        try {
            if (!(filter_var($commentId, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('commentId不是数字');
            }
            $result = $talkService->delTalkComment($commentId);
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function actionComment(Request $request, TalkService $talkService)
    {
        $commentId = $request->input('commentId');
        $category = $request->input('category');
        try {
            if (!(filter_var($commentId, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('commentId不是数字');
            }
            if (!(filter_var($category, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('category不是数字');
            }
            if ($category != 1 && $category != 2) {
                throw new Exception('非法参数');
            }
            $result = $talkService->actionComment($commentId, $category);
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function getRankingTypes(Request $request, TalkService $talkService)
    {
        try {
            $result = $talkService->getRankingTypes();
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function getRankingDetail(Request $request, TalkService $talkService)
    {
        $rankType = $request->input('rankType');
        try {
            if (!(filter_var($rankType, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('rankType不是数字');
            }
            $result = $talkService->getRankingDetail($rankType);
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function getAllComments(Request $request, TalkService $talkService)
    {
        $talkId = $request->input('talkId');
        try {
            if (!(filter_var($talkId, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('talkId不是数字');
            }
            $result = $talkService->getAllComments($talkId);
        } catch (Exception $exception) {
            return $this->myResponse($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function closeTalkTip(Request $request, TalkService $talkService)
    {
        $talkId = $request->input('talkId');
        try {
            if (!(filter_var($talkId, FILTER_VALIDATE_INT) !== false)) {
                throw new Exception('talkId不是数字');
            }
            $result = $talkService->closeTalkTip($talkId);
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }

    public function getEmoji(Request $request, TalkService $talkService)
    {
        try {
            $result = $talkService->getEmoji();
        } catch (Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $result);
    }
}
