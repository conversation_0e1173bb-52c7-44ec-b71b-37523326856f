<?php

namespace App\Http\Controllers\Schedule;

use App\Http\Controllers\Controller;
use App\Http\Services\ScheduleService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ScheduleController extends Controller
{
    public function teacher(ScheduleService $scheduleService) {
        return $this->success($scheduleService->teachers());
    }


    public function index(Request $request, ScheduleService $scheduleService)
    {
        $data = $request->all();
        $rules = [
            'start' => 'required|date_format:Y-m-d',
            'end' => 'required|date_format:Y-m-d',
            'month' => 'sometimes|in:0,1',
            'teacher' => 'sometimes|string',
            'type' => 'sometimes|in:all,cas,custom',
            'uaid' => 'required|string',
        ];

        // 自定义错误消息
        $messages = [
            'start.required' => 'The start date is required.',
            'start.date_format' => 'The start date must be in the format Y-m-d.',
            'end.required' => 'The end date is required.',
            'end.date_format' => 'The end date must be in the format Y-m-d.',
            'type.in' => 'The type field must be one of the following: all, cas, custom.',
            'uaid.required' => 'The uaid field is required.',
        ];

        // 执行验证
        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            return $this->error(collect($validator->errors()->getMessages())->first());
        }
        return $this->success($scheduleService->schedule_list($data));
    }
}
