<?php

namespace App\Http\Controllers\Schedule;

use App\Http\Controllers\Controller;
use App\Http\Services\ScheduleService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class IndexController extends Controller
{
    public function index(Request $request, ScheduleService $scheduleService)
    {
        $strStudentUaId = $request->input('studentId');
        $date = $request->input('date', date('Y-m-d'));
        try {
            $arrList = $scheduleService->index(Auth::user(), $strStudentUaId, $date);
        } catch (\Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response('', 1, $arrList);
    }

    public function create(Request $request, ScheduleService $scheduleService)
    {
        #数据验证
        $validator = Validator::make($request->all(), [
            'planName' => 'required|max:1000',
            'remark' => 'nullable|max:500',
        ]);
        if ($validator->fails()) {
            return $this->response($validator->errors()->first(), 0);
        }
        #主表常规字段
        $arrPostData['uaId'] = $request->input('uaId');
        $arrPostData['studentId'] = $request->input('studentId');
        $arrPostData['date'] = $request->input('date');
//        $arrPostData['startTime'] = $request->input('startTime');
//        $arrPostData['endTime'] = $request->input('endTime');
        $arrPostData['time'] = $request->input('time');
        $arrPostData['allDayChecked'] = $request->input('allDayChecked', 0);
        #扩展表常规字段
        $arrPostData['from'] = $request->input('from', '');
        $arrPostData['type'] = $request->input('type', '');
        $arrPostData['planName'] = $request->input('planName', '');
        $arrPostData['remark'] = $request->input('remark', '');
        #扩展字段
        $arrPostData['finish'] = $request->input('finish');
        $arrPostData['total'] = $request->input('total');
        $arrPostData['feedBack'] = $request->input('feedBack');
        $arrPostData['room'] = $request->input('room');
        $arrPostData['campus'] = $request->input('campus');
        $arrPostData['subject'] = $request->input('subject');
        $arrPostData['teacherName'] = $request->input('teacherName');
        $arrPostData['planType'] = $request->input('planType');
        $arrPostData['description'] = $request->input('description');
        #
        try {
            $scheduleService->create(Auth::user(), $arrPostData);
        } catch (\Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response();
    }

    public function edit(Request $request, ScheduleService $scheduleService)
    {
        #数据验证
        $validator = Validator::make($request->all(), [
            'planName' => 'required|max:1000',
            'remark' => 'nullable|max:500',
        ]);
        if ($validator->fails()) {
            return $this->response($validator->errors()->first(), 0);
        }
        #主表常规字段
        $arrPostData['id'] = $request->input('id', 0);
        $arrPostData['uaId'] = $request->input('uaId');
        $arrPostData['studentId'] = $request->input('studentId');
        $arrPostData['date'] = $request->input('date');
//        $arrPostData['startTime'] = $request->input('startTime');
//        $arrPostData['endTime'] = $request->input('endTime');
        $arrPostData['time'] = $request->input('time');
        $arrPostData['allDayChecked'] = $request->input('allDayChecked', 0);
        #扩展表常规字段
        $arrPostData['from'] = $request->input('from', '');
        $arrPostData['type'] = $request->input('type', '');
        $arrPostData['planName'] = $request->input('planName', '');
        $arrPostData['remark'] = $request->input('remark', '');
        #扩展字段
        $arrPostData['finish'] = $request->input('finish');
        $arrPostData['total'] = $request->input('total');
        $arrPostData['feedBack'] = $request->input('feedBack');
        $arrPostData['room'] = $request->input('room');
        $arrPostData['campus'] = $request->input('campus');
        $arrPostData['subject'] = $request->input('subject');
        $arrPostData['teacherName'] = $request->input('teacherName');
        $arrPostData['planType'] = $request->input('planType');
        $arrPostData['description'] = $request->input('description');
        #
        try {
            $scheduleService->edit(Auth::user(), $arrPostData);
        } catch (\Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response();
    }

    public function finish(Request $request, ScheduleService $scheduleService)
    {
        $iScheduleId = $request->input('id', 0);
        try {
            $scheduleService->finish(Auth::user(), $iScheduleId);
        } catch (\Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response();
    }

    public function delete(Request $request, ScheduleService $scheduleService)
    {
        $iScheduleId = $request->input('id', 0);
        try {
            $scheduleService->delete(Auth::user(), $iScheduleId);
        } catch (\Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response();
    }

    public function studentList(Request $request, ScheduleService $scheduleService)
    {
        $strTeacherUaId = $request->input('uaId');
        $dataScope = (int)$request->input("dataScope", 0);
        try {
            $arrList = $scheduleService->studentList($strTeacherUaId, $dataScope);
        } catch (\Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $arrList);
    }

    public function studentScheduleList(ScheduleService $scheduleService, Request $request)
    {
        $studentId = $request->input('studentId');
        $cookies = $request->header("cookie", "");
        try {
            $r = $scheduleService->studentScheduleList($studentId, $cookies);
        } catch (\Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response("", 1, $r);
    }

    /**
     * 标记学生
     * @param ScheduleService $scheduleService
     * @param Request $request
     */
    public function attentionStudent(ScheduleService $scheduleService, Request $request)
    {
        $studentId = $request->input('studentId');
        try {
            $r = $scheduleService->attention(Auth::user(), $studentId, $request->input("attention", true));
        } catch (\Exception $e) {
            return $this->response($e->getMessage(), 0);
        }
        return $this->response("", 1, $r);
    }

    /**
     * 每日计划的学生详情
     * @param ScheduleService $scheduleService
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function studentInfo(ScheduleService $scheduleService, Request $request)
    {
        return $this->response("", 1, $scheduleService->studentInfo(Auth::user(), $request->input("studentId")));
    }

    /**
     * 附加每日计划计划交接人
     * @param ScheduleService $scheduleService
     * @param Request $request
     */
    public function assignPlanAttachUser(ScheduleService $scheduleService, Request $request)
    {
        $data = [
            'studentId' => $request->input("studentId"),
            'comment' => $request->input("comment"),
            'attachUser' => $request->input("attachUser", [])
        ];
        try {
            $r = $scheduleService->assignPlanAttachUser(Auth::user(), $data);
        } catch (\Exception $e) {
            return $this->response($e->getMessage(), 0);
        }
        return $this->response("", 1, $r);


    }
}
