<?php


namespace App\Http\Controllers\Medal;


use App\Http\Controllers\Controller;
use App\Http\Services\Medal\MedalService;
use Illuminate\Http\Request;

/**
 * 勋章相关控制器
 * Class MedalController
 * @package App\Http\Controllers\Medal
 */
class MedalController extends Controller
{
    public function userMedalList(MedalService $medalService)
    {
        return $this->response("", 1, $medalService->userMedalList());
    }

    public function drop(Request $request, MedalService $medalService)
    {
        return $this->response("", 1, $medalService->medalDrop($request->input("id", "")));
    }

    public function categoryMedal(MedalService $medalService) {
        return $this->response("", 1, $medalService->categoryMedal());
    }
    public function dropMedalList(MedalService $medalService) {
        return $this->response("", 1, $medalService->userMedalList(true));
    }
}
