<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use <PERSON><PERSON>\Lumen\Routing\Controller as BaseController;

class Controller extends BaseController
{
    protected function response($message = '', $code = 1, $data = [], $headers = [], $options = 0, $errorCode = 0): JsonResponse
    {
        $_data = [
            'success' => $code === 0 ? 0 : 1,
            'message' => $message ?: 'success',
            'data' => $data,
            'errorCode' => $errorCode,
            'code' => $code === 0 ? 0 : 1,
            'errorMessage' => $message ?: 'success',
        ];
        return new JsonResponse($_data, 200, $headers, $options);
    }


    /**
     * 通用化JSON化返回
     * @param $code
     * @param string $data
     * @param int $errorCode
     * @return \Illuminate\Http\JsonResponse
     */
    protected function respondWithJson($code, $data = '', $errorCode = 0, $cookie = null)
    {
        if ($code == 101 && $data && is_array($data)) {
            asort($data);
        }
        if ($cookie) {
            return response()->json([
                'code' => "$code",
                'msg' => config('code.' . $code),
                'data' => $data,
                'errorCode' => $errorCode,
                'errorMsg' => config('code.' . $errorCode),
            ])->cookie($cookie);
        }
        return response()->json([
            'code' => "$code",
            'msg' => config('code.' . $code),
            'data' => $data,
            'errorCode' => $errorCode,
            'errorMsg' => config('code.' . $errorCode),
        ]);
    }

    protected function success($data = [])
    {
        return $this->response("", 1, $data);
    }

    protected function error($message)
    {
        return $this->response($message, 0);
    }
}
