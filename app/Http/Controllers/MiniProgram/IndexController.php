<?php

namespace App\Http\Controllers\MiniProgram;

use App\Http\Controllers\Controller;
use App\Http\Services\MiniProgramService;
use Illuminate\Http\Request;

class IndexController extends Controller
{
    public function index(Request $request, MiniProgramService $miniProgramService)
    {
        $strStartDate = $request->input('start_date', date('Y-m-d', strtotime('today')));
        $strEndDate = $request->input('end_date', date('Y-m-d', strtotime('today')));
        $strStudentUaId = $request->input('ua_id', '');
        try {
            $arrList = $miniProgramService->scheduleList($strStudentUaId, $strStartDate, $strEndDate);
        } catch (\Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response('', 1, $arrList);
    }

    public function detail(Request $request, MiniProgramService $miniProgramService)
    {
        $iScheduleId = $request->input('id', 0);
        try {
            $arrData = $miniProgramService->detail($iScheduleId);
        } catch (\Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        return $this->response('', 1, $arrData);
    }
}
