<?php

namespace App\Http\Controllers\PersonalCenter;

use App\ConstDir\File\FileConst;
use App\Http\Controllers\Controller;
use App\Http\Resources\StudentInfoResource;
use App\Http\Services\File\FileService;
use App\Http\Services\Point\PointService;
use App\Http\Services\Student\PersonalCenterService;
use App\Http\Services\Student\ResourceService;
use App\Models\Student\StudentInfoModel;
use App\Models\Student\StudentResourceModel;
use App\Models\Verify\UserData;
use App\Utils\QueueUtil;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use stdClass;

class ResourceController extends Controller
{
    /**
     * 更换头像
     * 图片审核异步
     * @param Request $request
     * @param PersonalCenterService $personalCenterService
     * @param FileService $fileService
     * @return JsonResponse
     * @throws FileNotFoundException
     */
    public function changeAvater(Request $request, PersonalCenterService $personalCenterService, FileService $fileService)
    {
        $storage = Storage::disk(FileService::$filesystem);
        $avaterFile = $request->file('avaterFile');
        $uaId = Auth::user()->ua_id;
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        if ($studentInfo->avater_checking > 0) return $this->response('已有审核中的头像', 0);
        if (!$avaterFile) {
            $studentInfo = $personalCenterService->applyAvater($uaId, null);
            $msg = "已恢复默认头像";
        } else {
            $file = $fileService->uploadImage($avaterFile, $uaId, FileConst::STATUS_IN_USE);
            if (is_string($file)) return $this->response($file, 0);
            $resourceId = $file->id;
            if (!$resourceId) return $this->response('头像上传失败', 0);

            $studentInfo = $personalCenterService->saveCheckingAvater($uaId, $resourceId);
            if (!$studentInfo) return $this->response('头像记录失败', 0);
            $data = [[
                "targetType" => UserData::TargetType_IMG,
                "content" => base64_encode($avaterFile->get()),
                "platform" => env("CHECK_PLATFORM", 'ali'),
                'fileUrl' => $storage->url($file->file_path),
            ]];
            $userData = new UserData();
            $userData->data = $data;
            $userData->classPath = "App\Http\Services\Student\ResourceService";
            $userData->methodName = "getResourceChecked";
            $userData->notifyData = ["uaId" => $uaId, "resourceId" => $resourceId, "resourceType" => StudentResourceModel::TYPE_AVATER];
            //图片审核使用异步处理
            QueueUtil::verifyInfoAsync($userData);
            //下面这段是同步处理
            //$studentInfo = QueueUtil::verifyInfoSync($userData);
            $msg = "头像将在审核通过后生效";
        }
        $data = StudentInfoResource::make($studentInfo);
        return $this->response($msg, 1, $data);
    }


    /**
     * 更换头像框
     * 可以舍弃
     * @param Request $request
     * @param PersonalCenterService $personalCenterService
     * @param ResourceService $resourceService
     * @return JsonResponse
     */
    public function changeAvaterFrame(Request $request, PersonalCenterService $personalCenterService, ResourceService $resourceService)
    {
        $uaId = Auth::user()->ua_id;
        $resourceId = $request->input('frame', 0);
        $resource = $resourceService->getActiveSingleResource($uaId, $resourceId, StudentResourceModel::TYPE_AVATER_FRAME);
        if ($resource || $resourceId == 0) {
            $studentInfo = $personalCenterService->changeAvaterFrame($resourceId, $uaId);
            $data = StudentInfoResource::make($studentInfo);
            return $this->response('', 1, $data);
        } else {
            return $this->response('资源异常', 0);
        }
    }


    /**
     * 更换背景皮肤
     * 可以舍弃
     * @param Request $request
     * @param PersonalCenterService $personalCenterService
     * @param ResourceService $resourceService
     * @return JsonResponse
     */
    public function changeSkin(Request $request, PersonalCenterService $personalCenterService, ResourceService $resourceService)
    {
        $uaId = Auth::user()->ua_id;
        $resourceId = $request->input('skin', 0);
        $resource = $resourceService->getActiveSingleResource($uaId, $resourceId, StudentResourceModel::TYPE_SKIN);
        if ($resource || $resourceId == 0) {
            $studentInfo = $personalCenterService->changeSkin($resourceId, $uaId);
            $data = StudentInfoResource::make($studentInfo);
            return $this->response('', 1, $data);
        } else {
            return $this->response('资源异常', 0);
        }
    }

    /**
     * 更换学员使用中的资源
     * 2头像框 3皮肤
     * @param Request $request
     * @param PersonalCenterService $personalCenterService
     * @param ResourceService $resourceService
     * @return JsonResponse
     */
    public function changeResource(Request $request, PersonalCenterService $personalCenterService, ResourceService $resourceService)
    {
        $uaId = Auth::user()->ua_id;
        $resourceId = $request->input('resourceId', 0);
        $resourceType = $request->input('resourceType', 0);
        $resource = $resourceService->getActiveSingleResource($uaId, $resourceId, $resourceType);
        if ($resource || $resourceId == 0) {
            switch ($resourceType) {
                case StudentResourceModel::TYPE_AVATER_FRAME:
                    $studentInfo = $personalCenterService->changeAvaterFrame($resourceId, $uaId);
                    break;
                case StudentResourceModel::TYPE_SKIN:
                    $studentInfo = $personalCenterService->changeSkin($resourceId, $uaId);
                    break;
                default:
                    return $this->response('类型异常', 0);
            }
            $data = StudentInfoResource::make($studentInfo);
            return $this->response('', 1, $data);
        } else {
            return $this->response('用户未拥有此资源，无法使用', 0);
        }
    }


    /**
     * 获取学员资源列表
     * @param Request $request
     * @param ResourceService $resourceService
     * @return JsonResponse
     */
    public function resourceList(Request $request, ResourceService $resourceService)
    {
        $uaId = Auth::user()->ua_id;
        $resourceType = $request->input('resourceType', 0);
        $list = $resourceService->getResources($uaId, $resourceType);
        $num = 0;
        if (is_array($list)) $num = count($list);
        if (is_object($list)) $num = $list->count();
        if ($num == 0) $list = new stdClass();
        return $this->response('', 1, $list);
    }


    /**
     * 资源兑换
     * @param Request $request
     * @param ResourceService $resourceService
     * @param PointService $pointService
     * @return JsonResponse
     * @throws \Exception
     */
    public function exchangeResource(Request $request, ResourceService $resourceService, PointService $pointService)
    {
        $uaId = Auth::user()->ua_id;
        $resourceId = $request->input('resourceId');
        if (!$resourceId) return $this->response('兑换资源不存在', 0);

        $resourceType = $request->input('resourceType');
        if (!$resourceType || !in_array($resourceType, array_keys(ResourceService::SYS_RESOURCE_TYPE))) return $this->response('资源类型异常', 0);

        $resource = $resourceService->getActiveSingleResource($uaId, $resourceId, $resourceType);
        if ($resource) return $this->response('已有该资源，无需兑换', 0);

        //积分处兑换
        $exchange = $pointService->exchange($uaId, ResourceService::SYS_RESOURCE_TYPE[$resourceType], $resourceId);
        if (!$exchange['success']) return $this->response($exchange['msg'], 0);

        //兑换成功增加归属记录
        $studentRseource = $resourceService->newResource($uaId, $resourceId, $resourceType, "", [], StudentResourceModel::STATUS_ACTIVE);
        //兑换成功返回当前积分
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        return $this->response('兑换成功', 1, ["point" => $studentInfo->point]);
    }


    /**
     * 获取兑换记录
     * @param Request $request
     * @param ResourceService $resourceService
     * @return JsonResponse
     */
    public function exchangeHistory(Request $request, ResourceService $resourceService)
    {
        $uaId = Auth::user()->ua_id;
        $resourceType = $request->input('resourceType');
        if (!$resourceType || !in_array($resourceType, array_keys(ResourceService::SYS_RESOURCE_TYPE))) return $this->response('资源类型异常', 0);
        $list = $resourceService->resourceHistory($uaId, $resourceType);
        if ($list->count() == 0) $list = new stdClass();
        return $this->response('', 1, $list);
    }

}
