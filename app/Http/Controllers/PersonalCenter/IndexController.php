<?php

namespace App\Http\Controllers\PersonalCenter;

use App\Http\Controllers\Controller;
use App\Http\Resources\StudentInfoResource;
use App\Http\Services\Point\PointService;
use App\Http\Services\Student\PersonalCenterService;
use App\Models\Student\StudentInfoModel;
use App\Models\Verify\UserData;
use App\Utils\QueueUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndexController extends Controller
{
    /**
     * 个人中心首页
     * 暂时没用
     * @param PersonalCenterService $personalCenterService
     * @return JsonResponse
     */
    public function index(PersonalCenterService $personalCenterService)
    {
        $detail = $personalCenterService->studentDetail();
        return $this->response('', 1, $detail);
    }


    /**
     * 学习规划列表
     * @param Request $request
     * @param PersonalCenterService $personalCenterService
     * @return JsonResponse
     */
    public function learningPlanList(Request $request, PersonalCenterService $personalCenterService)
    {
        $page = $request->input('page', 1);
        $pageLimit = $request->input('pageLimit', 20);
        $sort = $request->input('sort', "modifiedtime");
        $sortOrder = $request->input('sortOrder', "DESC");
        $cookie = $request->header("cookie", "");
        try {
            $result = $personalCenterService->learningPlanList($cookie, $page, $pageLimit, $sort, $sortOrder);
        } catch (\Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        $resultArr = json_decode($result, true);
        if ($resultArr["success"]) {
            return $this->response('', 1, $resultArr["result"]);
        } else {
            return $this->response($resultArr["error"]["message"], 0);
        }
    }


    /**
     * 学习规划详情
     * @param Request $request
     * @param PersonalCenterService $personalCenterService
     * @return JsonResponse
     */
    public function learningPlanDetail(Request $request, PersonalCenterService $personalCenterService)
    {
        $planId = $request->input('planId', 0);
        $cookie = $request->header("cookie", "");
        try {
            $result = $personalCenterService->learningPlanDetail($cookie, $planId);
        } catch (\Exception $exception) {
            return $this->response($exception->getMessage(), 0);
        }
        $resultArr = json_decode($result, true);
        if ($resultArr["success"]) {
            return $this->response('', 1, $resultArr["result"]);
        } else {
            return $this->response($resultArr["error"]["message"] ?? "error", 0);
        }
    }

    /**
     * 获取当前登录学员详情，首次登录创建主要信息
     * @param Request $request
     * @param PersonalCenterService $personalCenterService
     * @return JsonResponse
     */
    public function studentDetail(Request $request, PersonalCenterService $personalCenterService)
    {
        $withLevel = $request->input('level', false);
        $detail = $personalCenterService->studentDetail($withLevel);
        if (!$detail) return $this->response('用户异常', 0);
        return $this->response('', 1, $detail);
    }


    /**
     * 获取课表
     * @param Request $request
     * @param PersonalCenterService $personalCenterService
     * @return JsonResponse
     */
    public function lessonList(Request $request, PersonalCenterService $personalCenterService)
    {
        $params = [
            'lessonStartDate' => $request->input('lessonStartDate', date('Y-m-d', strtotime('-2 days'))),
            'lessonEndDate' => $request->input('lessonEndDate', date('Y-m-d', strtotime('+11 days'))),
            'selectClassNum' => $request->input('selectClassNum', []),
            'selectStaffId' => $request->input('selectStaffId', []),
        ];
        $params['lessonList'] = $personalCenterService->getCasLessons($params);
        return $this->response('', 1, $params);
    }


    /**
     * 获取cas相关两个下拉框的数据
     * @param PersonalCenterService $personalCenterService
     * @return JsonResponse
     */
    public function lessonPickLists(PersonalCenterService $personalCenterService)
    {
        $picklists = $personalCenterService->getCasPickLists();
        return $this->response('', 1, $picklists);
    }


    /**
     * 修改目标
     * @param Request $request
     * @param PersonalCenterService $personalCenterService
     * @return JsonResponse
     */
    public function changeTarget(Request $request, PersonalCenterService $personalCenterService)
    {
        $target = $request->input('target');
        $uaId = Auth::user()->ua_id;
        //回调审核方案
        $data = [[
            "targetType" => UserData::TargetType_TEXT,
            "content" => $target,
            "platform" => env("CHECK_PLATFORM", 'ali'),
            "only" => true,
        ]];
        $userData = new UserData();
        $userData->data = $data;
        $userData->classPath = "App\Http\Services\Student\PersonalCenterService";
        $userData->methodName = "changeTargetAsynchronous";
        $userData->notifyData = ["uaId" => $uaId, "target" => $target];

        $studentInfo = QueueUtil::verifyInfoSync($userData);
        if ($studentInfo) {
            $data = StudentInfoResource::make($studentInfo);
            return $this->response('已提交', 1, $data);
        } else {
            return $this->response('提交失败', 0);
        }
    }

    /**
     * 更换昵称
     * @param Request $request
     * @param PersonalCenterService $personalCenterService
     * @param PointService $pointService
     * @return JsonResponse
     */
    public function changeNickname(Request $request, PersonalCenterService $personalCenterService, PointService $pointService)
    {
        $nickName = $request->input('nickname', "");
        $uaId = Auth::user()->ua_id;

        if ($nickName == "") return $this->response('未输入内容', 0);

        //查重
        $nickNameExist = StudentInfoModel::where([
            ['nickname', '=', $nickName, 'or'],
            ['nickname_checking', '=', $nickName, 'or'],
        ])->first();
        if ($nickNameExist) return $this->response('该昵称已存在或不可用', 0);

        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
        //扣除积分
        if ($studentInfo->nickname_change > 0) {
            $result = $pointService->changeNickname($uaId, 1);
        } else {
            $result = $pointService->changeNickname($uaId, 1, 0);
        }
        if (!$result["success"]) return $this->response($result["msg"], 0);
        //回调审核方案
        $data = [[
            "targetType" => UserData::TargetType_TEXT,
            "content" => $nickName,
            "platform" => env("CHECK_PLATFORM", 'ali'),
            "only" => true,
        ]];
        $userData = new UserData();
        $userData->data = $data;
        $userData->classPath = "App\Http\Services\Student\PersonalCenterService";
        $userData->methodName = "applyNicknameAsynchronous";
        $userData->notifyData = ["uaId" => $uaId, "nickName" => $nickName];

        $studentInfo = QueueUtil::verifyInfoSync($userData);
        if ($studentInfo) {
            $data = StudentInfoResource::make($studentInfo);
            return $this->response('昵称修改成功', 1, $data);
        } else {
            return $this->response('该昵称已存在或不可用', 0);
        }
    }
}
