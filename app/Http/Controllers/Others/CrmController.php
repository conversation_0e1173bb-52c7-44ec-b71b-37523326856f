<?php

namespace App\Http\Controllers\Others;

use App\Http\Controllers\Controller;
use App\Http\Services\Survey\SurveyService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CrmController extends Controller
{
    /**
     * CRM首笔订单后sc做对应动作
     * @param Request $request
     * @return JsonResponse
     */
    public function firstOrder(Request $request)
    {
        $uaId = $request->input('uaId');

        //todo 未来慢慢加入crm首次订单生效后的动作和所需参数
        $ext = [
            'uaId' => $uaId,
            'keyTime' => $request->input('keyTime', date("Y-m-d H:i:s")),
            'orderId' => $request->input('orderId'),
            'instanceId' => $request->input('instanceId'),
            'rightsId' => $request->input('orderId'),
            'startTime' => $request->input('startTime'),
            'endTime' => $request->input('endTime'),
        ];

        //增加首次订单的调研
        $surveyService = new SurveyService();
        $surveyResult = $surveyService->firstOrderSurvey($uaId, $ext['startTime'], $ext);
        if ($surveyResult["success"]) {
            return $this->response('', 1, $surveyResult["data"]);
        } else {
            return $this->response($surveyResult["data"], 0, new \stdClass());
        }
    }
}
