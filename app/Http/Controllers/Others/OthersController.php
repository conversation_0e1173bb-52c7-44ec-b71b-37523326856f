<?php


namespace App\Http\Controllers\Others;


use App\Http\Controllers\Controller;
use App\Http\Services\Other\OtherService;
use App\Http\Services\Other\ThirdPartRecordService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;

/**
 * 无法归类的
 * Class OthersController
 * @package App\Http\Controllers\Others
 */
class OthersController extends Controller
{
    public function uaLinks(ThirdPartRecordService $thirdPartRecordService)
    {
        $r = Cache::remember("sc:ua:link", Carbon::now()->addDays(), function () use ($thirdPartRecordService) {
            return $thirdPartRecordService->uaLinks();
        });
        return $this->response("", 1, $r);
    }

    public function uaTarget($id, ThirdPartRecordService $thirdPartRecordService)
    {
        $r = Cache::remember("sc:ua:link:target:" . $id, Carbon::now()->addDays(), function () use ($thirdPartRecordService, $id) {
           return $thirdPartRecordService->uaTarget($id);
        });
        return $this->response("", 1, $r);
    }

    public function menu(OtherService $otherService)
    {
        return $this->response("", 1, $otherService->menu());
    }


    public function ossCallback(Request $request, OtherService $otherService) {
        return $this->response("", 1, $otherService->ossCallback($request->all()));
    }

    public function readMessage(Request $request,  ThirdPartRecordService $thirdPartRecordService) {
        $type = $request->input("type", 'lms');
        if ($type == 'lms') {
            $thirdPartRecordService->readLmsMessage($request->input("id", 0));
        } else {
            $thirdPartRecordService->readWordMessage($request->input("id", 0));
        }
        return $this->response( !(int)$request->input("id", 0) ? "已读全部消息" : "已读该条消息" , 1);
    }

    public function hasOfThirdPartMessage(Request $request, ThirdPartRecordService $thirdPartRecordService) {
        $type = $request->input("type", '');
        return $this->response("", 1, $thirdPartRecordService->unReadMessage($type));
    }

    public function thirdPartMessage(Request $request, ThirdPartRecordService $thirdPartRecordService) {
        return $this->response("", 1, $thirdPartRecordService->messageList($request->input("type", 'lms'), $request->input("page", 1), $request->input("pageNum", 10)));
    }
}
