<?php

namespace App\Http\Controllers\Banner;

use App\Http\Controllers\Controller;
use App\Http\Services\BannerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class BannerController extends Controller
{
    public function lists(Request $request, BannerService $bannerService)
    {
        try {
            $arrList = Cache::remember("sc:banner:list", 3600, function () use ($bannerService) {
                return $bannerService->list();
            });
        } catch (\Exception $exception) {
            dd($exception);
        }
        return $this->response("", 1, $arrList);
    }

    public function classInfo(Request $request, BannerService $bannerService)
    {
        $iBannerId = $request->input('banner_id', 0);
        try {
            $classInfo = $bannerService->classInfo($iBannerId);
        } catch (\Exception $exception) {
            dd($exception);
        }
        return $this->response("", 1, $classInfo);
    }

    public function click(Request $request, BannerService $bannerService)
    {
        $iBannerId = $request->input('banner_id', 0);
        $iStudentId = Auth::user()->ua_id;
        $bannerService->click($iBannerId, $iStudentId);
        return $this->response("", 1);
    }

    public function reserve(Request $request, BannerService $bannerService)
    {
        $iBannerId = $request->input('banner_id', 0);
        $iStudentId = Auth::user()->ua_id;
        $strClassCode = $request->input('class_code', '');
        $bannerService->click($iBannerId, $iStudentId, 1);
        $response = $bannerService->reserve($request->cookie(env("SSO_COOKIE_KEY", "UA_TOKEN")), $strClassCode, $iBannerId, $iStudentId);
        return $this->response("", 1, $response);
    }

    /**
     * 首页sc banner 不同于ua banner
     * @param BannerService $bannerService
     */
    public function index(BannerService $bannerService) {
        $r = Cache::remember("sc:banner", 3600, function () use ($bannerService) {
            return $bannerService->scList();
        });
        return $this->response("", 1, $r);
    }
}
