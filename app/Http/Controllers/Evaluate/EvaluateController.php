<?php

namespace App\Http\Controllers\Evaluate;

use App\Http\Controllers\Controller;
use App\Http\Services\Evaluate\EvaluateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EvaluateController extends Controller
{
    public function debug(EvaluateService $evaluateService)
    {
//        $evaluateService->teacherEvalActiveConfig();
        $evaluateService->relationOfStudentWithTeacher("XKT000021707029644", [], null);
        return $this->response("rule");
    }


    /**
     * 获取当期登陆用户可调研的教师
     */
    public function evaluateUser(Request $request, EvaluateService  $evaluateService)
    {
        $fromWindows = $request->input("fromWindows", false);
        $uaId = Auth::user()->ua_id;
        return $this->success($evaluateService->studentEvalTeacherList($uaId, $fromWindows));
    }

    /**
     * @param Request $request
     * @param EvaluateService $evaluateService
     */
    public function submitEvaluate(Request $request, EvaluateService $evaluateService)
    {
        //todo 提交数据
        $data = [
            "configId" => $request->input("configId"),
            "eval" => $request->input("eval"),
            "uaId" => Auth::user()->ua_id,
            "isAnonymous" => (int) $request->input("isAnonymous", 0),
        ];
        $r = $evaluateService->submitEvaluate($data);
        if (is_array($r)) {
            return $this->success($r["msg"]);
        }
        return $this->response($r, 0);
    }

    public function closeButton(EvaluateService $evaluateService)
    {
        $evaluateService->closeEvaluateTips(Auth::user()->ua_id);
        return $this->success();
    }
}
