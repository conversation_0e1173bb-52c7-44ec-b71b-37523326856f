<?php

namespace App\Http\Controllers\Gift;

use App\Http\Controllers\Controller;
use App\Http\Services\Gift\GiftService;
use Illuminate\Http\Request;

class GiftController extends Controller
{

    public function sendGift(Request $request, GiftService $giftService)
    {
        $r = $giftService->sendGift($request->all());
        if (is_string($r)) {
            return $this->error($r);
        }
        return $this->success($r);
    }
}
