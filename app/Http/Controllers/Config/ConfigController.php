<?php

namespace App\Http\Controllers\Config;


use App\Http\Controllers\Controller;
use App\Http\Services\Point\PointService;
use App\Models\FastModel\FastQueryModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ConfigController extends Controller {

    public function config(Request $request) {
        $result = collect();
        $find = $request->input("name", "");
        if ($find == "CSAT评价标签配置") {
            $model = FastQueryModel::config()->whereNull("deleted_at")->where("type", "CSAT评价标签配置")->first();
            if ($model) {
                $result = json_decode($model->configs, true);
            }
        }
        if ($find == "CAST礼物配置") {
            //判断激活
            $active = FastQueryModel::config()->whereNull("deleted_at")->where("type", "CAST礼物开关")->first();
            if (!$active || $active->configs != "1") {
                $result = collect([
                    "point" => (new PointService())->getStudentPoint(optional(Auth::user())->ua_id),
                    "gifts" => collect(),
                    "status" => 0,
                ]);
                return $this->success($result);
            }
            $model = FastQueryModel::config()->whereNull("deleted_at")->where("type", "like", "CAST礼物配置%")->orderByRaw("CAST(JSON_UNQUOTE(JSON_EXTRACT(configs, '$.sort')) AS SIGNED) DESC")->get();
            if ($model) {
                $result = [];
                foreach ($model as $item) {
                    $configs = json_decode($item->configs, true);
                    $configs["id"] = $item->id;
                    $result[] = $configs;
                }
                $result = collect([
                    "point" => (new PointService())->getStudentPoint(optional(Auth::user())->ua_id),
                    "gifts" => $result,
                    "status" => 1,
                ]);
            }
        }
        return $this->success($result);
    }

}
