<?php

namespace App\Http\Controllers\ExchangeStore;

use App\Http\Controllers\Controller;
use App\Http\Services\ExchangeStore\ExchangeStoreService;
use Illuminate\Http\Request;

class ExchangeStoreController extends Controller
{

    public function exchangeList(Request $request, ExchangeStoreService $exchangeStoreService)
    {
        $type = $request->input('type', 1);
        $res = $exchangeStoreService->exchangeList($type);
        return $this->respondWithJson(100, $res);

    }


}
