<?php


namespace App\Http\Controllers\Student;


use App\Http\Controllers\Controller;
use App\Models\Student\StudentStudyTimeModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class StudyTimeController extends Controller
{
    public function index() {
        $uaId = Auth::user()->ua_id;
        $record = Cache::remember("sc:student:study:time:" . $uaId, 3600, function () use ($uaId) {
            return StudentStudyTimeModel::where("user_id", $uaId)->first();
        });
        $data = [];
        $start = date("Y-m-d", time());
        if ($record) {
            $data = Arr::get($record->record, "total", []);
            foreach ($data as $day => $total) {
                if ($day == $start) {
                    //今天的不要
                    continue;
                }
                $_day = date("Y年m月d日", strtotime($day));
                $data[$_day] = (int) ceil(bcdiv($total, 60, 10));
                unset($data[$day]);
            }
        }
        $start = strtotime(date("Y-m-d", time() - 86400));
        $_data = [];
        for ($i = 0;$i < 30;$i ++) {
            $_day = date("Y年m月d日", $start - (30 - $i - 1) * 86400);
            if (isset($data[$_day])) {
                $_data[$_day] = $data[$_day];
            } else {
                $_data[$_day] = 0;
            }
        }

        return $this->response("", 1, $_data);
    }
}
