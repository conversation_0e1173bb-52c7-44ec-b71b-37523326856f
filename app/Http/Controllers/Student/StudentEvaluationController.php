<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Http\Services\Student\StudentEvaluationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class StudentEvaluationController extends Controller
{
    public function config(StudentEvaluationService $studentEvaluationService)
    {
        return $this->response("", 1, $studentEvaluationService->config());
    }

    public function submitEvaluation(Request $request, StudentEvaluationService $studentEvaluationService)
    {
        $validator = Validator::make($request->all(), [
            'configId' => ['required', 'int', Rule::exists("student_evaluation_config", "id")->where("status", 1)->whereNull("deleted_at")],
            'content' => 'required|string|max:1000|min:1',
        ]);
        if ($validator->fails()) {
            return $this->response($validator->errors()->first(), 0);
        }
        return $this->response("", 1, $studentEvaluationService->submitEvaluation(['configId' => $request->input("configId"), "content" => $request->input("content")]));
    }
}
