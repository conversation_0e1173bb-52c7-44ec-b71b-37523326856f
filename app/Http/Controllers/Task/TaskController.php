<?php


namespace App\Http\Controllers\Task;


use App\Http\Controllers\Controller;
use App\Http\Services\Task\StudentTaskService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class TaskController extends Controller
{
    public function collect(StudentTaskService $studentTaskService, Request $request)
    {
        return $this->response("", 1, $studentTaskService->collect());
    }

    public function taskList(StudentTaskService $studentTaskService, Request $request)
    {
        return $this->response("", 1, $studentTaskService->taskList($request->input("type", "ems.meetings"), $request->input("page", 1), $request->input("pageNum", 10), $request->all()));
    }

    public function taskInfoOfWord(StudentTaskService $studentTaskService, Request $request) {
        return $this->response("", 1, $studentTaskService->taskInfoWord($request->input("id")));
    }
    public function taskOfWordAny(StudentTaskService $studentTaskService, Request $request) {
        return $this->response("", 1, $studentTaskService->taskOfWordAny($request->all()));
    }
    public function taskOfLmsAny(StudentTaskService $studentTaskService, Request $request) {
        $r = $studentTaskService->taskOfLmsAny($request->all());
        return $this->response(Arr::get($r, "message", ""), 1, Arr::get($r, "data"), [],0, Arr::get($r, "errorCode"));
    }

    public function taskOfGemsAny(StudentTaskService $studentTaskService, Request $request)
    {
        return $this->response("", 1, $studentTaskService->taskOfGemsAny($request->all()));
    }
}
