<?php

namespace App\Http\Middleware;

use App\Utils\QueueUtil;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OperationLog
{
    public function handle(Request $request, Closure $next)
    {
        $input = $request->except('_method', '_token');
        $input = !empty($input) ? json_encode($input, JSON_UNESCAPED_UNICODE) : '';
        $path = $request->path();
        $method = $request->method();
        $ip = $request->ip();
        $response = $next($request);
        self::writeLog($ip, $path, $method, $input);
        return $response;
    }

    /**
     * 操作记录
     */
    private function writeLog($ip, $path, $method, $input)
    {
        $userId = 0;
        if (Auth::user() != null) {
            $userId = Auth::user()->u_id;
        }
        $array = array();
        $array['table'] = 1;
        $array['user_id'] = $userId;
        $array['ip'] = $ip;
        $array['api'] = $path;
        $array['method'] = $method;
        $array['input'] = $input;
        $array['created_at'] = time();
        QueueUtil::log($array);
    }
}
