<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Auth\Middleware\Authenticate as Middleware;

class IP extends Middleware
{

    public function handle($request, Closure $next, ...$guards)
    {
        $env = env('APP_ENV');
        $arrIP = explode(',', env('IP_WHITELIST'));
        if ($env == 'production') {
            if (in_array($request->getClientIp(), $arrIP)) {
                return $next($request);
            } else {
                throw new \Exception('非法IP');
            }
        } else {
            return $next($request);
        }
    }
}
