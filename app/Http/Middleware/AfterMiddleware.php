<?php


namespace App\Http\Middleware;


use App\Http\Services\Point\PointService;
use App\Utils\CacheUtil;
use Closure;
use Illuminate\Support\Facades\Auth;
use Laravel\Lumen\Http\Request;

class AfterMiddleware
{
    public function handle(Request $request, Closure $next) {
        $response = $next($request);
        //进行每日登录逻辑

        $user = Auth::user();

        if ($user && $user->user_type_id == 2 && !CacheUtil::userVisitToday($user->ua_id)) {
            CacheUtil::userVisitToday($user->ua_id, true);
            (new PointService())->loginPoint($user->ua_id);
        }

        return $response;
    }
}
