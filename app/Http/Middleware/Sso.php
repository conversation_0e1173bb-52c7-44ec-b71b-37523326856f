<?php

namespace App\Http\Middleware;

use App\Http\Services\UaService;
use App\User;
use Closure;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Support\Facades\Auth;

class Sso extends Middleware
{
    public function handle($request, Closure $next, ...$guards)
    {
        $jwt = $request->cookie(env('SSO_COOKIE_KEY', 'UA_TOKEN'));
        try {
            $uaService = new UaService();
            if ($info = $uaService->verifyToken($jwt, env('SSO_SIGNATURE_KEY'))) {
                if ($info['exp'] < time()) {
                    throw new \Exception('jwt expire', -1);
                }
                if (isset($info['belong']) && $info['belong'] != 'xkt') {
                    throw new \Exception('not xkt user', -1);
                }
            } else {
                throw new \Exception('jwt error', -1);
            }
            $objUser=User::where(['ua_id' => $info['ua_id']])->firstOrFail();
            Auth::login($objUser);
        } catch (\Exception $exception) {
            return response([
                'success' => -1,
                'message' => $exception->getMessage(),
                'data' => new \stdClass()
            ]);
        }
        return $next($request);
    }
}
