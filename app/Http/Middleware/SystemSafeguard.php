<?php
namespace App\Http\Middleware;

use App\ConstDir\BaseConst;
use App\ConstDir\RoleConst;
use App\Models\EmsSystemSafeguardModel;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SystemSafeguard {
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     *
     * @throws \Illuminate\Session\TokenMismatchException
     */
    public function handle($request, Closure $next)
    {
        if (!env("SAFEGUARD_ON", false)) {
            return $next($request);
        }
        //开启debug
        $user = Auth::user();
        $uaId = optional($user)->ua_id;
        $debugUser = env("SAFEGUARD_DEBUG_USER", "");
        if ($debugUser) {
            $debugUser = explode(",", $debugUser);
            if (in_array($uaId, $debugUser)) {
                return $next($request);
            }
            $_data = [
                'success' => 0,
                'message' => '系统维护中',
                'data' => new \stdClass(),
                'errorCode' => 0,
                'code' => 0,
                'errorMessage' => '系统维护中',
            ];
            return new JsonResponse($_data, 503);
        }
        return $next($request);
    }
}
