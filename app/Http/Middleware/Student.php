<?php

namespace App\Http\Middleware;

use App\Models\Student\StudentInfoModel;
use Closure;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Cookie;

class Student extends Middleware
{
    public function handle($request, Closure $next, ...$guards)
    {
        $uaId = Auth::user()->ua_id;
        //先判断用户是否正常存在
        $studentInfo = StudentInfoModel::where('uaid', $uaId)->first();
//        $studentInfo = null;
        if (!$studentInfo) {
            return response([
                'success' => -1,
                'message' => "用户不存在",
                'data' => new \stdClass()
            ])->cookie(new Cookie(env('SSO_COOKIE_KEY', 'UA_TOKEN'), "", 0, "/", "thinktown.com"));
        }
        return $next($request);
    }
}
