<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePointRecord extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('point_record', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger("type")->default(0)->comment("1:登录积分;2:评论积分;3:计划积分;4:作业积分;5:单词积分;6:勋章积分;7:排行榜积分;8:积分消耗");
            $table->string("reason",255)->default("")->comment("原因");
            $table->tinyInteger("change_type")->default(1)->comment("1:增加;2:减少;3:返还");
            $table->integer("point")->default(0)->comment("积分值");
            $table->integer("resource_id")->default(0)->comment("资源id");
            $table->string("user_id",255)->comment("用户id");
            $table->dateTime("created_at")->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('point_record');
    }
}
