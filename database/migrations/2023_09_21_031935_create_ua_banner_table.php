<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUaBannerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ua_banner', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->tinyInteger('position')->default(0);
            $table->string('title')->default('');
            $table->string('banner')->default('');
            $table->string('url')->default('');
            $table->json('extension_json')->nullable()->default(null);
            $table->unsignedTinyInteger('sort')->default(0);
            $table->unsignedTinyInteger('is_show')->default(0);
            $table->dateTime('offline_time')->default(null)->nullable();
            $table->timestamps();
        });
        Schema::create('ua_banner_click', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('user_id')->default(0);
            $table->unsignedInteger('banner_id')->default(0);
            $table->unsignedTinyInteger('position')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ua_banner');
        Schema::dropIfExists('ua_banner_click');
    }
}
