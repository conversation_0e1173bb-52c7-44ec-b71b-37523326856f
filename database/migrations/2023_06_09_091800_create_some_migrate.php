<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSomeMigrate extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //关注表
        Schema::create('attention_record', function (Blueprint $table) {
            $table->string("u_ua_id", 64)->comment("用户uaid")->index("idx_u");
            $table->string("s_ua_id", 64)->comment("学生uaid")->index("idx_s");
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });
        //计划交接人
        Schema::create('plan_attach_user', function (Blueprint $table) {
            $table->string("u_ua_id", 64)->comment("用户uaid")->index("idx_u");
            $table->string("s_ua_id", 64)->comment("学生uaid")->index("idx_s");
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });
        Schema::create('plan_attach_record', function (Blueprint $table) {
            $table->string("s_ua_id", 64)->comment("学生uaid")->primary();
            $table->string("u_ua_id", 64)->comment("用户uaid 上一次更新者")->index("idx_u");
            $table->string("memo", 2000)->comment("备注")->default("");
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('attention_record');
        Schema::dropIfExists('plan_attach_user');
        Schema::dropIfExists('plan_attach_record');
    }
}
