<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStudentEvaluationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create("student_evaluation_config", function (Blueprint  $table) {
            $table->id();
            $table->string("type", 255)->comment("类型");
            $table->integer("status")->default(1);
            $table->integer("sort")->default(0);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('student_evaluation', function (Blueprint $table) {
            $table->id();
            $table->string("ua_id", 255)->comment("学生ID");
            $table->integer("config_id")->comment("配置ID");
            $table->text("content")->comment("反馈内容");
            $table->integer("status")->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_evaluation');
        Schema::dropIfExists('student_evaluation_config');
    }
}
