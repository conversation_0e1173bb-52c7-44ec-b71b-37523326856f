<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateStudentInterimReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //主表
        Schema::create('student_interim_report', function (Blueprint $table) {
            $table->id();
            $table->string("report_name", 255)->comment("报告名称");
            $table->string("student_id", 32)->comment("学生ID");
            $table->string("create_user", 32)->comment("创建人ID");
            $table->tinyInteger("status")->default(0)->comment("完成状态 0 未完成 1 已完成 2 部分完成");
            $table->date("report_start");
            $table->date("report_end");
            $table->string("source", 255)->comment("数据来源配置");
            $table->timestamps();
            $table->softDeletes();
        });
        DB::statement("ALTER TABLE `sc_student_interim_report` comment '阶段报告 - 主表'");

        Schema::create("student_interim_report_teacher", function (Blueprint $table) {
            $table->id();
            $table->integer("report_id")->index("idx_report_id");
            $table->string("teacher_id", 32)->index("idx_teacher_id");
            $table->string("relation")->comment("关联");
            $table->tinyInteger("status")->default(0)->comment("完成情况");
        });
        DB::statement("ALTER TABLE `sc_student_interim_report_teacher` comment '阶段报告 - 教师关联'");

        Schema::create("student_interim_report_info", function (Blueprint $table) {
            $table->id();
            $table->integer("report_id")->index("idx_report_id");
            $table->string("create_user", 32)->comment("创建用户 系统创建默认为空")->nullable();
            $table->string("update_user", 32)->comment("更新用户");
            $table->string("report_type")->comment("报告类型");
            $table->json("report_info")->comment("报告详情");
            $table->timestamps();
        });
        DB::statement("ALTER TABLE `sc_student_interim_report_info` comment '阶段报告 - 详情表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_interim_report');
        Schema::dropIfExists('student_interim_report_info');
        Schema::dropIfExists('student_interim_report_teacher');
    }
}
