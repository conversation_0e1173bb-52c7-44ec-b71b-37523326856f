<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class Create126Table extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_record', function (Blueprint $table) {
            //礼物记录
            $table->id();
            $table->integer("config_id");
            //CAST
            $table->string("belong_type")->comment("归属类型");
            $table->integer("belong_id")->comment("归属ID");
            $table->string("target_ua_id")->comment("目标用户");
            $table->string("ua_id")->comment("赠送者");
            $table->json("ext")->comment("赠送内容");
            $table->integer("gift_num")->comment("单次收到礼物数");
            $table->integer("gift_score")->comment("单次收到礼物积分数");
            $table->timestamp("created_at")->useCurrent();
            $table->timestamp("updated_at")->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });
        Schema::table("config", function (Blueprint $table) {
            $table->timestamp("deleted_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_record');
        Schema::table("config", function (Blueprint $table) {
            $table->dropColumn("deleted_at");
        });
    }
}
