<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBanner extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('banner', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedTinyInteger('type')->default(0)->comment('banner类型1广告，2互动');
            $table->string('title')->default('');
            $table->integer('resource_id')->default(0);
            $table->json('ext')->comment('额外配置');
            $table->unsignedTinyInteger('sort')->default(0);
            $table->dateTime('online_time')->comment('上线时间');
            $table->dateTime('offline_time')->comment('下线时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('banner');
    }
}
