<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMedalTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //勋章主表
        Schema::create('medal', function (Blueprint $table) {
            $table->id();
            $table->integer("category_id")->comment("所属类别ID")->index("idx_category_id");
            $table->string("name", 255)->comment("名称");
            $table->tinyInteger("type")->default(1)->comment("类型 1 等级 2计数");
            $table->string("from", 255)->default("sc")->comment("来源 默认来源sc 可能存在其它平台的勋章");
            $table->tinyInteger("repeat")->default(1)->comment("是否可重复获得 1可以 0 不可以");
            $table->string("desc")->default("")->comment("描述");
            $table->timestamps();
            $table->softDeletes();
        });
        //勋章细节表
        Schema::create("medal_detail", function (Blueprint $table) {
            $table->id();
            $table->integer("medal_id")->index("idx_medal_id");
            $table->string("level", 255)->comment("级别表示");
            $table->integer("step")->comment("步数，等级的数字表示");
            $table->integer("progress")->comment("进阶进度表示");
            $table->integer("reward_score")->comment("活得时可获取到积分");
            $table->json("image")->comment("对应级别图标");
            $table->json("image2")->comment("对应级别图标 未激活");
            $table->timestamps();
            $table->softDeletes();
        });
        //勋章掉落表
        Schema::create("medal_drop", function (Blueprint $table) {
            $table->id();
            $table->string("user_id")->comment("关联用户ID")->index("idx_user_id");
            $table->integer("medal_log_id")->comment("勋章日志关联ID")->index("idx_medal_log_id");
            $table->tinyInteger("status")->comment("掉落状态");
            $table->timestamps();
            $table->softDeletes();
        });
        //用户勋章列表
        Schema::create("user_medal", function (Blueprint $table) {
            $table->id();
            $table->string("user_id")->comment("关联用户ID")->index("idx_user_id");
            $table->integer("medal_id")->comment("关联勋章ID")->index("idx_medal_id");
            $table->integer("medal_detail_id")->comment("当前获得勋章的最高级")->index("idx_medal_detail_id");
            $table->integer("progress")->comment("进度");
            $table->timestamps();
            $table->softDeletes();
        });
        //用户勋章历史记录
        Schema::create("user_medal_log", function (Blueprint $table) {
            $table->id();
            $table->string("user_id")->comment("关联用户ID")->index("idx_user_id");
            $table->integer("medal_detail_id")->comment("勋章详情ID")->index("idx_medal_detail_id");
            $table->integer("source_id")->comment("掉落关联资源ID");
            $table->string("source_type")->comment("关联资源类型");
            $table->integer("reward_score")->comment("获得积分");
            $table->string("from")->default("sc")->comment("来源 默认sc");
            $table->timestamps();
            $table->softDeletes();
            $table->index(['source_id', 'source_type'], "idx_with_source_and_type");
        });
        //勋章分类
        Schema::create("medal_category", function (Blueprint $table) {
            $table->id();
            $table->string("name")->comment("类别名称");
            $table->tinyInteger("status")->comment("状态 0 失效 1有效");
            $table->integer("sort")->comment("排序")->default(0);
            $table->integer("icon")->comment("icon")->default(0);
            $table->json("color")->comment("颜色");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('medal');
        Schema::dropIfExists('model_detail');
        Schema::dropIfExists('model_drop');
        Schema::dropIfExists('user_medal_log');
        Schema::dropIfExists('medal_category');
    }
}
