<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGrade extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('grade', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title')->default('');
            $table->integer('resource_id')->default(0)->comment('资源id');
            $table->string('resource2')->default("")->comment('资源2');
            $table->integer('lower_score')->default(0)->comment('下限积分');
            $table->integer('upper_score')->default(0)->comment('上限积分');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('grade');
    }
}
