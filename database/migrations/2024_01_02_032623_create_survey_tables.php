<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSurveyTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('survey', function (Blueprint $table) {
            //调研模版主表
            $table->id('survey_id');
            $table->string("name")->comment("后台展示用的名称，防止标题重名无法识别");
            $table->string('title')->comment('标题')->nullable();
            $table->string('describe')->comment('描述')->nullable();
            $table->unsignedTinyInteger('regular_type')->default(0)->comment('周期类型 0非定期，1月度');
            $table->json('regular')->comment('周期信息')->nullable();
            $table->unsignedTinyInteger('target_type')->default(0)->comment('目标类型 0全局，1静态目标，2动态目标');
            $table->integer('rank')->default(0)->comment('权重 数字越小权重越高');
            $table->json('target')->comment('静态目标学员数组')->nullable();
            $table->string('handle')->comment('动态目标对应function')->nullable();
            $table->json('ext')->comment('额外信息')->nullable();
            $table->unsignedTinyInteger('status')->comment("状态 0无效1有效")->default(0);
            $table->dateTime('start_time')->comment("时间范围开始时间");
            $table->dateTime('end_time')->comment("时间范围结束时间");
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });

        Schema::create('survey_detail', function (Blueprint $table) {
            //调研模版题目表
            $table->id("survey_detail_id");
            $table->integer('survey_id')->comment('调研模板id');
            $table->string('title')->comment('标题')->nullable();
            $table->string('describe')->comment('描述')->nullable();
            $table->unsignedTinyInteger('type')->comment("选项类型 0无选项 1单选 2多选");
            $table->unsignedTinyInteger('has_tag')->comment("是否有标签 0无标签 1有标签");
            $table->unsignedTinyInteger('has_comment')->comment("是否可评论 0无评论 1有评论");
            $table->integer('sequence')->comment('排序');
            $table->json('options')->comment('具体选项和标签范围')->nullable();
            $table->json('tags')->comment('公用标签')->nullable();
            $table->unsignedTinyInteger('status')->comment("状态 0失效 1有效");
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });

        Schema::create('student_survey', function (Blueprint $table) {
            //学员的调研表
            $table->id("student_survey_id");
            $table->string('uaid')->default('')->comment('uaid');
            $table->integer('survey_id')->comment('调研模板id');
            $table->unsignedTinyInteger('show')->comment("0未弹出 1已弹出");
            $table->unsignedTinyInteger('status')->comment("0无效 1未填写 2已填写");
            $table->dateTime('start_time')->comment("时间范围开始时间");
            $table->dateTime('end_time')->comment("时间范围结束时间");
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });

        Schema::create('student_survey_detail', function (Blueprint $table) {
            //学员的调研题目表
            $table->id("student_survey_detail_id");
            $table->integer('student_survey_id')->comment('学员的调研id');
            $table->integer('survey_detail_id')->comment('调研模版题目id');
            $table->json("option")->comment('选项结果数组')->nullable();
            $table->json("tags")->comment('标签')->nullable();
            $table->text('comment')->comment('评论')->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('survey');
        Schema::dropIfExists('survey_detail');
        Schema::dropIfExists('student_survey');
        Schema::dropIfExists('student_survey_detail');
    }
}
