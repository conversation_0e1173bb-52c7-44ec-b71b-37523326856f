<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTalkCommentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('talk_comments', function (Blueprint $table) {
            $table->dropColumn('cont');
        });
        Schema::table('talk_comments', function (Blueprint $table) {
            $table->longText('cont')->nullable()->comment('内容')->after("id");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('talk_comments', function (Blueprint $table) {
            $table->dropColumn('cont');
        });
        Schema::table('talk_comments', function (Blueprint $table) {
            $table->string('cont', 2000)->nullable()->comment('内容')->after("id");
        });
    }
}
