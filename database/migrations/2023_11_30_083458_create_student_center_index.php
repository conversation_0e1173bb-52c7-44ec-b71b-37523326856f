<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateStudentCenterIndex extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('comment_imgs', function (Blueprint $table) {
            $table->index(['comment_id', 'status', 'deleted_at'], 'index1');
        });

        Schema::table('talk_comment_actions', function (Blueprint $table) {
            $table->index(['comment_id', 'like_category', 'like_status', 'deleted_at'], 'index1');
        });

        Schema::table('talk_comments', function (Blueprint $table) {
            $table->index(['comment_id', 'status'], 'index1');
            $table->index(['talk_id', 'comment_id'], 'index2');
        });

        Schema::table('talks', function (Blueprint $table) {
            $table->index(['status', 'deleted_at'], 'index1');
        });

        Schema::table('talk_imgs', function (Blueprint $table) {
            $table->index(['talk_id', 'status', 'deleted_at'], 'index1');
        });

        Schema::table('student_ranking_types', function (Blueprint $table) {
            $table->index(['type_id', 'start_time', 'end_time'], 'index1');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('comment_imgs', function (Blueprint $table) {
            $table->dropIndex('index1');
        });

        Schema::table('talk_comment_actions', function (Blueprint $table) {
            $table->dropIndex('index1');
        });

        Schema::table('talk_comments', function (Blueprint $table) {
            $table->dropIndex('index1');
            $table->dropIndex('index2');
        });

        Schema::table('talks', function (Blueprint $table) {
            $table->dropIndex('index1');
        });

        Schema::table('talk_imgs', function (Blueprint $table) {
            $table->dropIndex('index1');
        });

        Schema::table('student_ranking_types', function (Blueprint $table) {
            $table->dropIndex('index1');
        });
    }
}
