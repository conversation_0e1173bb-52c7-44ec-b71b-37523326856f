<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateThirdPartRecord extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('third_part_record', function (Blueprint $table) {
            $table->id();
            $table->string("user_id")->comment("用户ID")->index("index_user_id");
            $table->string("platform")->comment("平台")->index("index_platform");
            $table->string("source_type")->comment("资源类型")->index("index_source_type");;
            $table->string("source_id")->comment("资源ID");
            $table->dateTime("record_time")->comment("记录时间");
            $table->integer("record_date")->comment("记录天")->index("index_record_date");
            $table->json("ext")->comment("拓展字段");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('third_part_record');
    }
}
