<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableWith124 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('evaluate_record_info', function (Blueprint $table) {
            $table->id();
            $table->integer("config_id")->comment("配置ID")->index("idx_config");
            $table->integer("belong_id")->comment("所属于Record ID");
            $table->string("type")->default("")->comment("所属类型 student or teacher");
            $table->string("code")->default("")->comment("编码 A1 A2 B C D E F 等")->index("idx_code");
            $table->timestamp("last_open_time")->nullable()->comment("上一次打开时间");
            $table->tinyInteger("close_status")->default(0)->comment("关闭状态");
            $table->timestamps();
            $table->index(["belong_id", "type", "code"], "idx_belong_type_code");
        });
        Schema::table("teacher_evaluate", function (Blueprint $table) {
            $table->integer("belong_record_id")->nullable()->comment("所属关联信息ID");
            $table->integer("parent_id")->nullable()->comment("爹");
            $table->integer("main_id")->nullable()->comment("每个财务月关系的主ID")->index("idx_main_id");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('evaluate_record_info');
        Schema::table("teacher_evaluate", function (Blueprint $table) {
           $table->dropColumn("belong_record_id");
           $table->dropColumn("parent_id");
        });
    }
}
