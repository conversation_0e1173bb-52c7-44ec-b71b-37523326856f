<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AlterStudentCenterTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('comment_imgs');

        Schema::dropIfExists('student_ranking_types');

        Schema::dropIfExists('talks');

        Schema::dropIfExists('talk_actions');

        Schema::create('comment_imgs', function (Blueprint $table) {
            $table->bigInteger('id', true);
            $table->integer('comment_id')->nullable()->comment('评论主键');
            $table->integer('img_id')->nullable()->comment('话题的图片');
            $table->enum('status', ['审核中', '审核已过', '审核未过'])->nullable()->default('审核中')->comment('状态');
            $table->integer('created_opper')->nullable()->comment('创建人');
            $table->integer('updated_opper')->nullable()->comment('最后更新人');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->comment('最后更新时间');
            $table->softDeletes()->comment('不为空即为删除');
        });

        Schema::create('student_ranking_types', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('title')->nullable()->comment('排行榜标题');
            $table->tinyInteger('title_orders')->nullable()->comment('排行榜标题排序');
            $table->integer('type_id')->nullable()->comment('排行榜编号');
            $table->string('type_name')->nullable()->comment('排行榜名称');
            $table->tinyInteger('type_orders')->nullable()->comment('排行榜名称排序');
            $table->dateTime('start_time')->nullable()->comment('排行榜有效开始时间');
            $table->dateTime('end_time')->nullable()->comment('排行榜有效结束时间');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->comment('最后更新时间');
        });

        Schema::create('talks', function (Blueprint $table) {
            $table->bigInteger('id', true);
            $table->string('title')->nullable()->comment('标题');
            $table->string('cont')->nullable()->comment('内容');
            $table->integer('orders')->nullable()->default(0)->comment('排序,数字越大越排前面');
            $table->tinyInteger('istop')->nullable()->default(0)->comment('是否置顶,0 不置顶,1 置顶');
            $table->tinyInteger('ishot')->nullable()->default(0)->comment('是否热门0 不热门,1 热门');
            $table->integer('ishigh')->nullable()->default(0)->comment('1 标识是高分榜');
            $table->enum('status', ['已保存', '已上线', '已下线'])->nullable()->default('已保存')->comment('状态，1\'已保存\',2\'已上线\',3\'已下线\'');
            $table->integer('created_opper')->nullable()->comment('创建人');
            $table->integer('updated_opper')->nullable()->comment('最后更新人');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->comment('最后更新时间');
            $table->softDeletes()->comment('不为空即为删除');
        });

        Schema::create('talk_actions', function (Blueprint $table) {
            $table->bigInteger('id', true);
            $table->string('action_user', 64)->nullable()->comment('操作人');
            $table->bigInteger('talk_id')->nullable()->comment('话题id');
            $table->tinyInteger('category')->nullable()->comment('1表示关闭提醒');
            $table->tinyInteger('status')->nullable()->comment('1表示是，0表示已取消');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->comment('最后更新时间');
            $table->softDeletes()->comment('不为空即为删除');
        });

        Schema::table('stu_message', function (Blueprint $table) {
            $table->dropColumn(['froms']);
            $table->dropColumn(['action_user']);
            $table->dropColumn(['recive_user']);
            $table->dropColumn(['party_type']);
            $table->dropColumn(['created_opper']);
        });

        Schema::table('stu_message', function (Blueprint $table) {
            $table->enum('froms', ['LMS', '百万单词', '修改昵称成功', '修改昵称失败', '评论审核通过', '评论审核未通过', '修改昵称失败返还积分', '头像审核通过', '头像审核未通过', '后台删除评论', '积分消息', '评论消息', '排行榜积分'])->nullable()->comment('来源,子类型，例如百万单词')->after("msgtype");
            $table->string('action_user', 64)->nullable()->comment('消息发送人')->after("froms");
            $table->string('recive_user', 64)->nullable()->comment('消息接受人')->after("action_user");
            $table->enum('party_type', ['话题', '评论', '踩', '赞', 'LMS', '百万单词'])->nullable()->comment('关联的其他表类型')->after("memo");
        });

        Schema::table('talk_comment_actions', function (Blueprint $table) {
            $table->dropColumn(['id']);
            $table->dropColumn(['action_user']);
        });

        Schema::table('talk_comment_actions', function (Blueprint $table) {
            $table->bigInteger('id', true)->first();
            $table->string('action_user', 64)->nullable()->comment('操作人')->after("id");
        });

        Schema::table('talk_comments', function (Blueprint $table) {
            $table->dropColumn(['created_opper']);
            $table->dropColumn(['img_id']);
        });

        Schema::table('talk_comments', function (Blueprint $table) {
            $table->string('created_opper', 64)->nullable()->comment('评论人id')->after('comment_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('comment_imgs');

        Schema::dropIfExists('student_ranking_types');

        Schema::dropIfExists('talks');

        Schema::dropIfExists('talk_actions');

        Schema::table('stu_message', function (Blueprint $table) {
            $table->dropColumn(['froms']);
            $table->dropColumn(['action_user']);
            $table->dropColumn(['recive_user']);
            $table->dropColumn(['party_type']);
        });

        Schema::table('stu_message', function (Blueprint $table) {
            $table->enum('froms', ['LMS', '百万单词', '修改昵称成功', '修改昵称失败', '评论审核通过', '评论审核未通过', '修改昵称失败返还积分', '头像审核通过', '头像审核未通过', '后台删除评论'])->nullable()->comment('来源,子类型，例如百万单词')->after("msgtype");
            $table->integer('action_user')->nullable()->comment('消息发送人')->after("froms");
            $table->integer('recive_user')->nullable()->comment('消息接受人')->after("action_user");
            $table->enum('party_type', ['话题', 'LMS', '百万单词'])->nullable()->comment('关联的其他表类型')->after("memo");
            $table->integer('created_opper')->nullable()->comment('创建人')->after("party_id");
        });

        Schema::table('talk_comment_actions', function (Blueprint $table) {
            $table->dropColumn(['id']);
            $table->dropColumn(['action_user']);
        });

        Schema::table('talk_comment_actions', function (Blueprint $table) {
            $table->bigInteger('id')->primary()->first();
            $table->bigInteger('action_user')->nullable()->comment('操作人')->after("id");
        });

        Schema::table('talk_comments', function (Blueprint $table) {
            $table->dropColumn(['created_opper']);
        });

        Schema::table('talk_comments', function (Blueprint $table) {
            $table->integer('created_opper')->nullable()->comment('评论人id')->after('comment_id');
            $table->integer('img_id')->nullable()->comment('图片')->after("talk_id");
        });
    }
}
