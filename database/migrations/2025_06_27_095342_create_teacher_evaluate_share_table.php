<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateTeacherEvaluateShareTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('teacher_evaluate_share', function (Blueprint $table) {
            $table->id();
            $table->date("belong_date")->comment("归属财务月");
            $table->string("ua_id")->comment("创建者");
            $table->string("target_ua_id")->comment("被分享者");
            $table->integer("from_eval_id")->comment("评价id");
            $table->string("share_key")->comment("分享key")->unique("idx_share_key");
            $table->timestamp("created_at")->useCurrent();
        });

        Schema::create("teacher_evaluate_share_info", function (Blueprint $table) {
            $table->id();
            $table->date("belong_date");
            $table->string("target_ua_id")->comment("被分享者");
            $table->integer("share_id")->comment("第一次分享的ID");
            $table->integer("eval_id")->comment("需要评价的ID")->unique("idx_eval_id");
            $table->tinyInteger("status")->comment("分享的评价状态");
            $table->integer("from_share_id")->nullable()->comment("当被评价时，标记来源分享ID");
            $table->integer("final_eval_id")->nullable()->comment("最终评价ID");
            $table->timestamp("updated_at")->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))->comment("更新时间，或者评价时间");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('teacher_evaluate_share');
    }
}
