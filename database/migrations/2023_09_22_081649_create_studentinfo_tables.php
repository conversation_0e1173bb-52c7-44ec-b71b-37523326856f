<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStudentinfoTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //学生主要信息表
        Schema::create('student_info', function (Blueprint $table) {
            $table->string('uaid')->default('')->comment('uaid')->primary();
            $table->string('code')->default('')->comment('学号');
            $table->integer('gender')->default(0)->comment('照搬ua 0男1女');
            $table->integer('level')->default(0)->comment('等级');
            $table->string('name')->default('')->comment('姓名');
            $table->string('nickname')->default('')->comment('昵称');
            $table->integer("nickname_change")->comment("昵称变更次数")->nullable(false)->default(0);
            $table->string('nickname_checking')->default('')->comment('审核中昵称');
            $table->integer('avater')->default(0)->comment('头像');
            $table->integer('avater_checking')->default(0)->comment('审核中头像');
            $table->integer('avater_frame')->default(0)->comment('头像框');
            $table->integer('skin')->default(0)->comment('背景图');
            $table->text('target')->comment('学习目标')->nullable();
            $table->integer('point')->comment('当前积分')->default(0);
            $table->integer('history_point')->comment('历史积分/总积分')->default(0);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

        });
        //学生资源关联表
        Schema::create('student_resource', function (Blueprint $table) {
            $table->increments('id');
            $table->string('uaid')->default('')->comment('uaid');
            $table->integer('type')->comment("类型 1头像2头像框3皮肤4昵称");
            $table->integer('resource_id')->comment("资源id")->default(0);
            $table->integer('status')->comment("状态 0关闭1有效2审核中3审核不通过")->default(0);
            $table->json('ext')->comment('额外信息');
            $table->timestamp('created_at')->comment("添加时间")->useCurrent();
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
            $table->dateTime('start_time')->comment("生效时间");
            $table->dateTime('end_time')->comment("结束时间");
            $table->string("check_id")->comment("审核id")->nullable(true)->default(null);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_info');
        Schema::dropIfExists('student_resource');
    }
}
