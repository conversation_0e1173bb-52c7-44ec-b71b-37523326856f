<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateScheduleDetail extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('schedule_detail', function (Blueprint $table) {
            $table->unsignedInteger('schedule_id')->unique();
            $table->string('name', 150)->comment('名称');
            $table->string('remark', 500)->default('')->comment('备注');
            $table->unsignedTinyInteger('from')->default(0)->comment('来源');
            $table->unsignedTinyInteger('type')->default(0)->comment('类型');
            $table->unsignedTinyInteger('is_checked')->default(0)->comment('是否完成');
            $table->unsignedTinyInteger('is_freeze')->default(0)->comment('是否冻结');
            $table->json('extension')->nullable()->default(null);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('schedule_detail');
    }
}
