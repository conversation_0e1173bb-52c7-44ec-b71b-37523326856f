<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AlterStudentSurvey2 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('student_survey', function (Blueprint $table) {
            $table->tinyInteger("first")->default(0)->comment("是否是该学员首次调研");
            $table->timestamp('show_time')->nullable()->comment("调研弹出时间");
            $table->timestamp('complete_time')->nullable()->comment("调研完成时间");
        });

        DB::update("ALTER TABLE sc_student_survey MODIFY COLUMN `updated_at` timestamp NOT NULL AFTER `created_at`");

        DB::update("UPDATE sc_student_survey ss
SET ss.complete_time=(
SELECT created_at FROM sc_student_survey_detail WHERE student_survey_id=ss.student_survey_id) WHERE ss.`status`=2");

        DB::update("UPDATE sc_student_survey ss
SET ss.show_time=updated_at WHERE ss.`status`=1 AND ss.`show`=1");

        DB::update("UPDATE sc_student_survey ss
SET ss.show_time=ss.complete_time WHERE ss.`show_time` IS NULL AND ss.`show`=1");

        DB::update("UPDATE sc_student_survey ss
SET ss.show_time=ss.updated_at WHERE ss.`show_time` IS NULL AND ss.`show`=1");

        DB::update("UPDATE sc_student_survey a,(
SELECT ss.student_survey_id FROM sc_student_survey ss LEFT JOIN sc_student_survey ss2 ON ss2.uaid=ss.uaid AND ss2.`show`=1 AND ss2.show_time< ss.show_time WHERE ss.`show`=1 AND ss2.student_survey_id IS NULL) AS b
SET a.`first`=1 WHERE a.student_survey_id=b.student_survey_id");

        DB::update("ALTER TABLE sc_student_survey MODIFY COLUMN `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`;");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('student_survey', function (Blueprint $table) {
            $table->dropColumn('first');
            $table->dropColumn('show_time');
            $table->dropColumn('complete_time');
        });
    }
}
