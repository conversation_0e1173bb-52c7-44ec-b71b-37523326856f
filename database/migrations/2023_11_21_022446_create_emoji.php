<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmoji extends Migration
{
    /**
     * 表情配置
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //表情分类
        Schema::create('emoji_category', function (Blueprint $table) {
            $table->id();
            $table->string("name")->comment("名称");
            $table->softDeletes();
            $table->timestamps();
        });
        //表情配置
        Schema::create('emoji_config', function (Blueprint $table) {
            $table->id();
            $table->integer("category_id")->comment("所属分类")->index("idx_category_id");
            $table->string("name")->comment("表情名称");
            $table->tinyInteger("type")->comment("表情类别 0图片表情 1 emoji")->default(0);
            $table->string("source")->comment("资源详情 如果是0 就是path 如果是 1 就是emoji 编码");
            $table->integer("sort")->comment("排序");
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('emoji_category');
        Schema::dropIfExists('emoji_config');
    }
}
