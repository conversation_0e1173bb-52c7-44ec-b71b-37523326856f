<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBlackUserConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('black_user_config', function (Blueprint $table) {
            $table->id();
            $table->string("user_id")->comment("目标用户UA_ID");
            $table->integer("type")->comment("类型 1话题禁止评论")->default(1);
            $table->string("times")->comment("操作天数");
            $table->dateTime("start_time")->comment("启用时间");
            $table->dateTime("open_time")->comment("解禁时间");
            $table->string("reason")->comment("原因");
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('black_user_config');
    }
}
