<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStudentRanking extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('student_ranking', function (Blueprint $table) {
            $table->string("user_id", 255)->comment("用户id")->unique("user_id_unique");
            $table->integer("week_point")->default(0)->comment("周积分");
            $table->integer("month_point")->default(0)->comment("月积分");
            $table->integer("total_point")->default(0)->comment("总积分");
            $table->integer("month_homework_num")->default(0)->comment("月作业数");
            $table->integer("month_word_num")->default(0)->comment("月抽查单词数");
            $table->integer("month_mock_toefl_score")->default(0)->comment("月模考分数-托福");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_ranking');
    }
}
