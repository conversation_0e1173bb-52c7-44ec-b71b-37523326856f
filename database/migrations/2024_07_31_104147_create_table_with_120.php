<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableWith120 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('navigation_config', function (Blueprint $table) {
            $table->id();
            $table->string("name")->default("")->comment("名称");
            $table->string("en_name")->default("")->comment("英文名称");
            $table->string("url")->default("")->comment("链接");
            $table->string("icon")->default("")->comment("图标");
            $table->string("target")->default("blank")->comment("打开方式");
            $table->tinyInteger("type")->default(0)->comment("类型 0本地 1外部");
            $table->tinyInteger("status")->default(0)->comment("状态");
            $table->integer("sort")->default(0)->comment("排序");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('navigation_config');
    }
}
