<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterStudentSurvey extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('student_survey', function (Blueprint $table) {
            $table->json('ext')->comment('额外信息')->nullable();
        });
        Schema::table('survey_detail', function (Blueprint $table) {
            $table->json('ext')->comment('额外信息')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('student_survey', function (Blueprint $table) {
            $table->dropColumn('ext');
        });
        Schema::table('survey_detail', function (Blueprint $table) {
            $table->dropColumn('ext');
        });
    }
}
