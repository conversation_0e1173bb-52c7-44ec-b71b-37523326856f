<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExchangeStore extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('exchange_store', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedTinyInteger('type')->default(0)->comment('兑换1头像框，2皮肤（背景）');
            $table->integer('resource_id')->default(0)->comment('资源id');
            $table->string('title')->default('');
            $table->json("ext")->comment("拓展字段")->nullable()->default(null);
            $table->integer('score')->default(0)->comment('兑换所需积分');
            $table->integer('sort')->default(0);
            $table->unsignedTinyInteger('status')->default(0)->comment('0不启用，1启用');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('exchange_store');
    }
}
