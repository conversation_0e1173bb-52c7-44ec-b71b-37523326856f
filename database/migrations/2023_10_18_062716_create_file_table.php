<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFileTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('file', function (Blueprint $table) {
            $table->id();
            $table->string("user_id", 255)->default("")->comment("上传者");
            $table->string("file_path", 255)->comment("文件路径");
            $table->string("file_name", 255)->comment("上传时的文件名字");
            $table->json("oss_path")->comment("oss 地址");
            $table->tinyInteger("oss_status")->default(0)->comment("oss 同步状态 0 未同步 1同步");
            $table->tinyInteger("status")->default(0)->comment("使用标记 0 未使用 1使用中");
            $table->tinyInteger("check_status")->default(0)->comment("审核状态 0 不通过 1通过 2 需要人工审核");
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('file');
    }
}
