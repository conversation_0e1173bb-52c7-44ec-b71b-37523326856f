<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSchedule extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('schedule', function (Blueprint $table) {
            $table->id();
            $table->date('schedule_date')->comment('日期');
            $table->time('begin')->comment('开始时间');
            $table->time('end')->comment('结束时间');
            $table->char('owner_uaid', 18)->comment('所属人ID');
            $table->char('creator_uaid', 18)->comment('创建人ID');
            $table->unsignedTinyInteger('all_day')->default(0)->comment('是否全天');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['schedule_date', 'owner_uaid']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('schedule');
    }
}
