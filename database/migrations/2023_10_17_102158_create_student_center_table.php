<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateStudentCenterTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('logic_log', function (Blueprint $table) {
            $table->bigInteger('id', true);
            $table->integer('user_id')->nullable()->comment('操作人id');
            $table->string('ip')->nullable()->comment('ip');
            $table->json('details')->nullable()->comment('内容详情');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
        });

        Schema::create('operating_log', function (Blueprint $table) {
            $table->bigInteger('id', true);
            $table->integer('user_id')->nullable()->comment('操作人id');
            $table->string('ip')->nullable()->comment('ip');
            $table->string('api', 30)->nullable()->comment('api接口全称');
            $table->string('method')->nullable()->comment('调用方法');
            $table->longText('input')->nullable()->comment('参数详情');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');

            $table->index(['user_id', 'api'], 'userid');
        });

        Schema::create('stu_message', function (Blueprint $table) {
            $table->bigInteger('id', true);
            $table->enum('msgtype', ['任务消息', '积分消息', '评论消息', '系统消息'])->nullable()->comment('消息类型');
            $table->enum('froms', ['LMS', '百万单词', '修改昵称成功', '修改昵称失败', '评论审核通过', '评论审核未通过', '修改昵称失败返还积分', '头像审核通过', '头像审核未通过', '后台删除评论'])->nullable()->comment('来源,子类型，例如百万单词');
            $table->integer('action_user')->nullable()->comment('消息发送人');
            $table->integer('recive_user')->nullable()->comment('消息接受人');
            $table->longText('msg')->nullable()->comment('消息体');
            $table->enum('status', ['未读取', '已读取'])->nullable()->default('未读取')->comment('消息状态');
            $table->string('memo')->nullable()->comment('备注，例如可放积分值');
            $table->enum('party_type', ['话题', 'LMS', '百万单词'])->nullable()->comment('关联的其他表类型');
            $table->integer('party_id')->nullable()->default(0)->comment('第三方数据表的id');
            $table->integer('created_opper')->nullable()->comment('创建人');
            $table->integer('updated_opper')->nullable()->comment('最后更新人');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->comment('最后更新时间');
            $table->softDeletes()->comment('不为空即为删除');
        });

        Schema::create('tags', function (Blueprint $table) {
            $table->bigInteger('id', true);
            $table->string('tag')->nullable()->comment('标签名字');
            $table->string('color_ft')->nullable()->comment('标签字体颜色');
            $table->string('color_bg')->nullable()->comment('标签背景颜色');
            $table->integer('orders')->nullable()->default(0)->comment('排序');
            $table->integer('created_opper')->nullable()->comment('创建人');
            $table->integer('updated_opper')->nullable()->comment('最后更新人');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->comment('最后更新时间');
            $table->softDeletes()->comment('不为空即为删除');
        });

        Schema::create('talk_comment_actions', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->bigInteger('action_user')->nullable()->comment('操作人');
            $table->bigInteger('comment_id')->nullable()->comment('评论id');
            $table->enum('like_category', ['点赞', '踩'])->nullable()->comment('1表示点赞，2表示踩');
            $table->tinyInteger('like_status')->nullable()->default(0)->comment('1表示已点，0表示已取消');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->comment('最后更新时间');
            $table->softDeletes()->comment('不为空即为删除');
        });

        Schema::create('talk_comments', function (Blueprint $table) {
            $table->bigInteger('id', true);
            $table->string('cont', 2000)->nullable()->comment('内容');
            $table->integer('orders')->nullable()->default(0)->comment('排序');
            $table->enum('status', ['审核中', '审核已过', '审核未过'])->nullable()->default('审核中')->comment('状态，1\'审核中\',2\'审核已过\',3\'审核未过\'');
            $table->bigInteger('talk_id')->nullable()->comment('话题主键');
            $table->integer('img_id')->nullable()->comment('图片');
            $table->bigInteger('comment_id')->nullable()->comment('评论父id');
            $table->integer('created_opper')->nullable()->comment('评论人id');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->comment('最后更新时间');
            $table->softDeletes()->comment('不为空即为删除');
        });

        Schema::create('talk_imgs', function (Blueprint $table) {
            $table->bigInteger('id', true);
            $table->integer('talk_id')->nullable()->comment('话题主键');
            $table->integer('img_id')->nullable()->comment('话题的图片');
            $table->enum('status', ['审核中', '已审核'])->nullable()->default('已审核')->comment('状态');
            $table->integer('created_opper')->nullable()->comment('创建人');
            $table->integer('updated_opper')->nullable()->comment('最后更新人');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->comment('最后更新时间');
            $table->softDeletes()->comment('不为空即为删除');
        });

        Schema::create('talk_tags', function (Blueprint $table) {
            $table->bigInteger('id', true);
            $table->integer('talk_id')->nullable()->comment('话题主键');
            $table->integer('tag_id')->nullable()->comment('标签主键');
            $table->integer('created_opper')->nullable()->comment('创建人');
            $table->integer('updated_opper')->nullable()->comment('最后更新人');
            $table->timestamp('created_at')->nullable()->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->comment('最后更新时间');
            $table->softDeletes()->comment('不为空即为删除');

            $table->index(['talk_id', 'tag_id'], 'talk_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('talk_tags');

        Schema::dropIfExists('talk_imgs');

        Schema::dropIfExists('talk_comments');

        Schema::dropIfExists('talk_comment_actions');

        Schema::dropIfExists('tags');

        Schema::dropIfExists('stu_message');

        Schema::dropIfExists('operating_log');

        Schema::dropIfExists('logic_log');
    }
}
