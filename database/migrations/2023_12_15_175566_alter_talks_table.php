<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTalksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('talks', function (Blueprint $table) {
            $table->dropColumn('cont');
        });
        Schema::table('talks', function (Blueprint $table) {
            $table->longText('cont')->nullable()->comment('内容')->after("id");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('talks', function (Blueprint $table) {
            $table->dropColumn('cont');
        });
        Schema::table('talks', function (Blueprint $table) {
            $table->string('cont', 255)->nullable()->comment('内容')->after("id");
        });
    }
}
