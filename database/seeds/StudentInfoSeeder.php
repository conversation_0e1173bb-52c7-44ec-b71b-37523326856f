<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StudentInfoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::insert(DB::raw("INSERT INTO student_center.sc_student_info (uaid,`code`,gender,`level`,`name`,nickname,created_at) SELECT va.ua_id AS uaid,va.smpid AS `code`,IF (va.account_type='Female',1,0) AS gender,1 AS `level`,va.accountname AS `name`,va.accountname AS nickname,vca.createdtime AS created_at FROM cas_crm.vtiger_account va JOIN cas_crm.vtiger_crmentity vca ON va.accountid=vca.crmid WHERE va.ua_id IS NOT NULL"));
    }
}
