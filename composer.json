{"name": "laravel/lumen", "description": "The Laravel Lumen Framework.", "keywords": ["framework", "laravel", "lumen"], "license": "MIT", "type": "project", "require": {"php": "^7.2.5", "guzzlehttp/guzzle": "^7.5", "illuminate/redis": "^7.30", "laravel/lumen-framework": "^7.0", "league/flysystem": "^1.0", "php-amqplib/php-amqplib": "^3.5", "predis/predis": "^2.2", "tymon/jwt-auth": "^1.0", "ext-json": "*"}, "require-dev": {"fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^8.5"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"classmap": ["tests/"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}}